﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.Models
{
    public enum PayMethodType
    {
        [Display(Name = "Espèces")]
        Drawer,

        [Display(Name = "Virement bancaire")]
        Bank,

        [Display(Name = "Sur compte")]
        Account,

        [Display(Name = "Carte de paiement")]
        PayCard,

        [Display(Name = "Reçu de caisse")]
        CashNote
    }
}
