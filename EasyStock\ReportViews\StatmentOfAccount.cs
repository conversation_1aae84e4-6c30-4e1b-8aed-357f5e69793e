﻿using DevExpress.Data;
using DevExpress.LookAndFeel;
using DevExpress.Utils;
using DevExpress.Utils.Extensions;
using DevExpress.XtraCharts;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controls;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Drawing;
using System.Linq;

namespace EasyStock.ReportViews
{
    public partial class StatmentOfAccount : ReportForm
    {
        public StatmentOfAccount() : base(new ReportFilter[]
        {
            ReportFilter.Account,
            ReportFilter.CostCenter,
            ReportFilter.Date
        }, typeof(StatmentOfAccountModel))
        {
            this.InitializeComponent();
            this.gridView1.CustomColumnDisplayText += this.GridView1_CustomColumnDisplayText;
            this.gridView1.RowCellStyle += this.GridView1_RowCellStyle;
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.gridView1.DataSourceChanged += this.GridView1_DataSourceChanged;
            base.FiltersForm.Accounts.MultiSelect = false;
            base.FiltersForm.CostCenter.MultiSelect = false;
            base.FiltersForm.ItemForAccounts.Text = "Compte";
            this.AddTextBoxes();
        }

        private void AddChart()
        {
            var balances = (from jd in AccountJournalsInPeriod
                            group jd by DbFunctions.TruncateTime(jd.Journal.Date) into g
                            select new BalanceInDate
                            {
                                Date = g.Key.Value,
                                Balance = (db.JournalDetails
                                            .Where(j => j.Account.Number.StartsWith(AccountNumber) && j.Journal.Date <= g.Key.Value)
                                            .Sum(j => (double?)(j.Debit * j.CurrencyRate)) ?? 0.0)
                                        - (db.JournalDetails
                                            .Where(j => j.Account.Number.StartsWith(AccountNumber) && j.Journal.Date <= g.Key.Value)
                                            .Sum(j => (double?)(j.Credit * j.CurrencyRate)) ?? 0.0)
                            }).ToList();

            List<BalanceInDate> dates = (from x in EasyStock.Common.Exteintions.EachDay(startDate, endtDate)
                                         select new BalanceInDate
                                         {
                                             Date = x.Date
                                         }).ToList();

            for (int i = 0; i < dates.Count; i++)
            {
                BalanceInDate currentDate = dates[i];
                double? balance = balances.FirstOrDefault(b => b.Date == currentDate.Date)?.Balance;

                if (!balance.HasValue && i > 0)
                {
                    balance = dates[i - 1].Balance;
                }

                currentDate.Balance = balance.GetValueOrDefault();
            }

            Series balanceSeries = new Series("Solde du compte", ViewType.Line)
            {
                ArgumentScaleType = ScaleType.DateTime,
                ArgumentDataMember = "Date",
                ValueScaleType = ScaleType.Numerical,
                DataSource = dates
            };
            balanceSeries.ValueDataMembers.AddRange("Balance");

            chartControl.Series.Clear();
            chartControl.Series.Add(balanceSeries);

            XYDiagram diagram = (XYDiagram)chartControl.Diagram;
            diagram.AxisY.Visibility = DefaultBoolean.True;
            diagram.AxisX.Visibility = DefaultBoolean.True;
            chartControl.Legend.Visibility = DefaultBoolean.True;
            chartControl.AnimationStartMode = ChartAnimationMode.OnDataChanged;

            diagram.AxisX.VisualRange.Auto = false;
            diagram.AxisX.VisualRange.SetMinMaxValues(((DateTime)diagram.AxisX.WholeRange.MaxValue).AddDays(-30.0), diagram.AxisX.WholeRange.MaxValue);
            diagram.EnableAxisXScrolling = true;
            diagram.EnableAxisXZooming = true;
        }

        private void AddTextBoxes()
        {
            this.OpenBalanceTextEdit.Properties.ReadOnly = true;
            this.TotalDebitTextEdit.Properties.ReadOnly = true;
            this.TotalCreditTextEdit.Properties.ReadOnly = true;
            this.MovmentBalanceTextEdit.Properties.ReadOnly = true;
            this.FianlBalanceTextEdit.Properties.ReadOnly = true;
            this.CurrentBalanceTextEdit.Properties.ReadOnly = true;
            this.layoutForTotal = new LayoutControl();
            this.layoutForTotal.OptionsPrint.OldPrinting = true;
            this.layoutForTotal.MaximumSize = new Size(300, 0);
            this.layoutForTotal.Padding = new System.Windows.Forms.Padding(0);
            LayoutControlGroup totalGroup = this.Root.AddGroup("Totaux");
            totalGroup.Padding = new DevExpress.XtraLayout.Utils.Padding(0);
            totalGroup.AddItem(" ", this.layoutForTotal);
            this.Root.Add(totalGroup);

            LayoutControlGroup chartGroup = this.Root.AddGroup("Statistiques", totalGroup, InsertType.Left);
            chartGroup.AddItem(string.Empty, this.chartControl);
            chartGroup.OptionsPrint.AllowPrint = false;

            this.layoutForTotal.AddItem("Solde d'ouverture", this.OpenBalanceTextEdit).TextLocation = Locations.Left;
            this.layoutForTotal.AddItem("Total Débit", this.TotalDebitTextEdit).TextLocation = Locations.Left;
            this.layoutForTotal.AddItem("Total Crédit", this.TotalCreditTextEdit).TextLocation = Locations.Left;
            this.layoutForTotal.AddItem("Mouvement de Solde", this.MovmentBalanceTextEdit).TextLocation = Locations.Left;
            this.layoutForTotal.AddItem("Solde de Clôture", this.FianlBalanceTextEdit).TextLocation = Locations.Left;
            this.layoutForTotal.AddItem("Solde Actuel", this.CurrentBalanceTextEdit).TextLocation = Locations.Left;
            this.SetTextAlignment();
        }

        private void GridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "Balance")
            {
                if (e.Value is double value)
                {
                    string type = ((value < 0.0) ? "Créditeur" : "Débiteur");
                    e.DisplayText = $"{Math.Abs(value):N} {type}";
                }
            }
            else if (e.Column.FieldName == "Date" && e.Value is DateTime date)
            {
                e.DisplayText = date.ToString("yyyy-MM-dd hh:mm:ss tt  ");
            }
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            gridView1.ClearSorting();
            GridColumn colDate = gridView1.Columns["Date"];
            if (colDate != null)
            {
                colDate.SortOrder = ColumnSortOrder.Ascending;
            }
            if (db.Currencies.Count() == 1)
            {
                if (gridView1.Columns["Currency"] != null)
                {
                    gridView1.Columns["Currency"].VisibleIndex = -1;
                }
                if (gridView1.Columns["CurrencyID"] != null)
                {
                    gridView1.Columns["CurrencyID"].VisibleIndex = -1;
                }
                if (gridView1.Columns["CurrencyRate"] != null)
                {
                    gridView1.Columns["CurrencyRate"].VisibleIndex = -1;
                }
                if (gridView1.Columns["LocalCredit"] != null)
                {
                    gridView1.Columns["LocalCredit"].VisibleIndex = -1;
                }
                if (gridView1.Columns["LocalDebit"] != null)
                {
                    gridView1.Columns["LocalDebit"].VisibleIndex = -1;
                }
            }
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            if ((info.InRow || info.InRowCell) && view.GetFocusedRowCellValue("ProcessID") is int id && view.GetFocusedRowCellValue("ProcessType") is SystemProcess process)
            {
                Utilities.OpenProcess(process, id);
            }
        }

        private void GridView1_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            if (e.Column.FieldName == "Balance")
            {
                if (e.CellValue is double value)
                {
                    Color color = Color.FromArgb(200, DXSkinColors.FillColors.Warning);
                    if (value < 0.0)
                    {
                        color = Color.FromArgb(200, DXSkinColors.FillColors.Success);
                    }
                    e.Appearance.BackColor = color;
                }
            }
            else
            {
                if ((!(e.Column.FieldName == "Credit") && !(e.Column.FieldName == "Debit")) || !(e.CellValue is double value))
                {
                    return;
                }
                if (value == 0.0)
                {
                    e.Appearance.ForeColor = Color.LightGray;
                    return;
                }
                Color color = Color.FromArgb(200, DXSkinColors.FillColors.Warning);
                if (e.Column.FieldName == "Credit")
                {
                    color = Color.FromArgb(200, DXSkinColors.FillColors.Success);
                }
                e.Appearance.BackColor = color;
            }
        }

        private void SetTextAlignment()
        {
            layoutForTotal.Items.ForEach(delegate (BaseLayoutItem x)
            {
                x.AppearanceItemCaption.TextOptions.HAlignment = HorzAlignment.Far;
            });
        }
        private Account Account
        {
            get
            {
                return this.db.Accounts.SingleOrDefault((Account x) => x.ID == this.AccountID);
            }
        }

        private CostCenter SelectedCostCenter
        {
            get
            {
                return this.db.CostCenters.SingleOrDefault((CostCenter x) => x.ID == this.CostCenterID);
            }
        }

        private int AccountID
        {
            get
            {
                GridPopupContainerControl accounts = base.FiltersForm.Accounts;
                int? num;
                if (accounts == null)
                {
                    num = null;
                }
                else
                {
                    IList<int> editValue = accounts.EditValue;
                    if (editValue == null)
                    {
                        num = null;
                    }
                    else
                    {
                        List<int> list = editValue.ToList<int>();
                        num = ((list != null) ? new int?(list.FirstOrDefault<int>()) : null);
                    }
                }
                int? num2 = num;
                return num2.GetValueOrDefault();
            }
        }

        private int CostCenterID
        {
            get
            {
                GridPopupContainerControl costCenter = base.FiltersForm.CostCenter;
                int? num;
                if (costCenter == null)
                {
                    num = null;
                }
                else
                {
                    IList<int> editValue = costCenter.EditValue;
                    if (editValue == null)
                    {
                        num = null;
                    }
                    else
                    {
                        List<int> list = editValue.ToList<int>();
                        num = ((list != null) ? new int?(list.FirstOrDefault<int>()) : null);
                    }
                }
                int? num2 = num;
                return num2.GetValueOrDefault();
            }
        }

        private IQueryable<JournalDetail> AccountJournals
        {
            get
            {
                IQueryable<JournalDetail> query = from x in this.db.JournalDetails.Include((JournalDetail x) => x.Account).Include((JournalDetail x) => x.Journal).Include((JournalDetail x) => x.Currency).Include((JournalDetail x) => x.CostCenter)
                                                  orderby x.Journal.Date
                                                  select x into jd
                                                  where jd.Account.Number.IndexOf(this.AccountNumber) == 0
                                                  select jd;
                bool flag = this.SelectedCostCenter != null;
                if (flag)
                {
                    query = from x in query
                            where x.CostCenter.Code.IndexOf(this.SelectedCostCenter.Code) == 0
                            select x;
                }
                return query;
            }
        }

        private IQueryable<JournalDetail> AccountJournalsInPeriod
        {
            get
            {
                return from jd in this.AccountJournals
                       where DbFunctions.TruncateTime((DateTime?)jd.Journal.Date) >= (DateTime?)this.startDate && DbFunctions.TruncateTime((DateTime?)jd.Journal.Date) <= (DateTime?)this.endtDate
                       select jd;
            }
        }

        private string AccountNumber
        {
            get
            {
                Account account = this.Account;
                return (account != null) ? account.Number : null;
            }
        }

        private string CostCenterNumber
        {
            get
            {
                Account account = this.Account;
                return (account != null) ? account.Number : null;
            }
        }

        private DateTime endtDate
        {
            get
            {
                bool hasEndtDate = base.FiltersForm.hasEndtDate;
                DateTime date;
                if (hasEndtDate)
                {
                    date = base.FiltersForm.ToDate.DateTime.Date;
                }
                else
                {
                    date = (this.AccountJournals.Max((JournalDetail x) => (DateTime?)x.Journal.Date) ?? DateTime.Now).Date;
                }
                return date;
            }
        }

        private DateTime startDate
        {
            get
            {
                bool hasStartDate = base.FiltersForm.hasStartDate;
                DateTime date;
                if (hasStartDate)
                {
                    date = base.FiltersForm.FromDate.DateTime.Date;
                }
                else
                {
                    date = (this.AccountJournals.Min((JournalDetail x) => (DateTime?)x.Journal.Date) ?? DateTime.Now).Date;
                }
                return date;
            }
        }

        internal override bool ValidateFilters()
        {
            IList<int> editValue = base.FiltersForm.Accounts.EditValue;
            bool flag = ((editValue != null) ? editValue.Count : 0) == 0;
            bool result;
            if (flag)
            {
                base.FiltersForm.Accounts.ErrorText = "Le compte doit être sélectionné";
                result = false;
            }
            else
            {
                result = true;
            }
            return result;
        }

        public override IQueryable GetQuery()
        {
            if (Account == null)
            {
                return null;
            }
            return from jd in AccountJournalsInPeriod
                   join br in db.Branches on jd.Journal.BranchID equals br.ID
                   select new StatmentOfAccountModel
                   {
                       DueDate = jd.DueDate,
                       Account = jd.Account.Name,
                       AccountID = jd.AccountID,
                       StoreID = br.ID,
                       ID = jd.JournalID,
                       Code = jd.Journal.Code,
                       Credit = jd.Credit,
                       Debit = jd.Debit,
                       Statement = jd.Statement,
                       Currency = jd.Currency.Name,
                       CurrencyID = jd.CurrencyID,
                       CurrencyRate = jd.CurrencyRate,
                       Date = jd.Journal.Date,
                       ProcessID = jd.Journal.ProcessID,
                       CostCenter = ((jd.CostCenter != null) ? jd.CostCenter.Name : ""),
                       ProcessType = jd.Journal.ProcessType,
                       Balance = db.JournalDetails.Where(j => j.AccountID == jd.AccountID && j.Journal.Date <= jd.Journal.Date).Sum(x => (double?)(x.Debit * x.CurrencyRate) ?? 0.0) - db.JournalDetails.Where(j => j.AccountID == jd.AccountID && j.Journal.Date <= jd.Journal.Date).Sum(x => (double?)(x.Credit * x.CurrencyRate) ?? 0.00)
                   };
        }
        public override void Print()
        {
            GridReportP.Print(this.layoutControl1, this.Text, this.labelControl1.Text, true);
        }

        public override void RefreshDataSource()
        {
            base.RefreshDataSource();
            this.OpenBalanceTextEdit.Text = this.Account.GetBalanceAsString(this.startDate.Date, null);
            double totalDebit = this.AccountJournalsInPeriod.Sum(x => (double?)(x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = this.AccountJournalsInPeriod.Sum(x => (double?)(x.Credit * x.CurrencyRate)).GetValueOrDefault();
            this.TotalDebitTextEdit.Text = totalDebit.ToString();
            this.TotalCreditTextEdit.Text = totalCredit.ToString();
            double balance = Math.Abs(totalCredit - totalDebit);
            string type = (totalCredit > totalDebit) ? "Créditeur" : "Débiteur";
            this.MovmentBalanceTextEdit.Text = string.Format("{0:N} {1}", balance, type);
            this.FianlBalanceTextEdit.Text = this.Account.GetBalanceAsString(this.endtDate.AddMinutes(1439.0).AddSeconds(59.0), this.db);
            this.CurrentBalanceTextEdit.Text = this.Account.GetBalance(null, null);
            this.AddChart();
        }

        private ChartControl chartControl = new ChartControl();

        private LayoutControl layoutForTotal;

        private TextEdit CurrentBalanceTextEdit = new TextEdit();

        private TextEdit FianlBalanceTextEdit = new TextEdit();

        private TextEdit MovmentBalanceTextEdit = new TextEdit();

        private TextEdit OpenBalanceTextEdit = new TextEdit();

        private TextEdit TotalCreditTextEdit = new TextEdit();

        private TextEdit TotalDebitTextEdit = new TextEdit();

        public class BalanceInDate
        {
            [Display(Name = "Solde")]
            public double Balance { get; set; }

            [Display(Name = "Date")]
            public DateTime Date { get; set; }
        }
    }
}
