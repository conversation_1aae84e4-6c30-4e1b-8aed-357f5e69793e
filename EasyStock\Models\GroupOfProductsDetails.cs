﻿using EasyStock.Classes;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Détails des Catégorie de Produits")]
    public class GroupOfProductsDetails : BaseNotifyPropertyChangedModel
    {
        [ReadOnly(true)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "")]
        public GroupOfProducts Group
        {
            get
            {
                return this.group;
            }
            set
            {
                base.SetProperty<GroupOfProducts>(ref this.group, value, "Group");
            }
        }

        [Display(Name = "")]
        public int GroupID
        {
            get
            {
                return this.groupID;
            }
            set
            {
                base.SetProperty<int>(ref this.groupID, value, "GroupID");
            }
        }

        [NotMapped]
        public Product Product
        {
            get
            {
                bool flag = this._product == null || this._product.ID != this.ProductID;
                if (flag)
                {
                    this._product = CurrentSession.Products.SingleOrDefault((Product x) => x.ID == this.ProductID);
                }
                return this._product;
            }
        }

        [Display(Name = "Produit")]
        public int ProductID { get; set; }

        [NotMapped]
        public ProductUnit Unit
        {
            get
            {
                bool flag = this._unit == null || this._unit.ID != this.UnitID;
                if (flag)
                {
                    this._unit = CurrentSession.ProductUnits.SingleOrDefault((ProductUnit x) => x.ID == this.UnitID);
                }
                return this._unit;
            }
        }

        [Display(Name = "Unité")]
        [Range(1, 2147483647, ErrorMessage = "Veuillez sélectionner une unité")]
        public int UnitID { get; set; }

        [Display(Name = "Quantité")]
        public double Quantity
        {
            get
            {
                return this.quantity;
            }
            set
            {
                base.SetProperty<double>(ref this.quantity, value, "Quantity");
            }
        }

        [Display(Name = "Prix")]
        public double Price
        {
            get
            {
                return this.price;
            }
            set
            {
                base.SetProperty<double>(ref this.price, value, "Price");
            }
        }

        [Display(Name = "Taux de Réduction")]
        [Range(0.0, 1.0, ErrorMessage = "Valeur non valide")]
        public double Discount
        {
            get
            {
                return this.discount;
            }
            set
            {
                base.SetProperty<double>(ref this.discount, value, "Discount");
            }
        }

        [Display(Name = "Prix Après Réduction")]
        [NotMapped]
        public double PriceAfterDiscount
        {
            get
            {
                return this.price - this.price * this.Discount;
            }
        }

        [Display(Name = "Total")]
        [NotMapped]
        public double Total
        {
            get
            {
                return this.PriceAfterDiscount * this.Quantity;
            }
        }

        private int id;

        private GroupOfProducts group;

        private int groupID;

        private Product _product;

        private ProductUnit _unit;

        private double quantity;

        private double price;

        private double discount;
    }
}
