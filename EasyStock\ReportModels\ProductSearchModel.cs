﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    internal class ProductSearchModel
    {
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Display(Name = "N° pièce")]
        public string Code { get; set; }

        [Display(Name = "Code-barres")]
        public List<string> Barcodes { get; set; }

        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Description")]
        public string Descreption { get; set; }

        [Display(Name = "Code Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Code de Fabricant")]
        public string Company { get; set; }

        [Display(Name = "#")]
        public string CustomField1 { get; set; }

        [Display(Name = "#")]
        public string CustomField2 { get; set; }

        [Display(Name = "#")]
        public string CustomField3 { get; set; }

        [Display(Name = "#")]
        public string CustomField4 { get; set; }
    }
}
