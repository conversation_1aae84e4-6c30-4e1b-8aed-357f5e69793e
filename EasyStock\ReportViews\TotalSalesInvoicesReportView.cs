﻿using DevExpress.XtraLayout.Utils;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class TotalSalesInvoicesReportView : XtraReportForm
    {
        public TotalSalesInvoicesReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Customer,
            ReportFilter.Store
        })
        {
            this.InitializeComponent();
            base.FiltersForm.ItemForFromDate.Text = "Date";
            base.FiltersForm.ItemForToDate.Visibility = LayoutVisibility.Never;
            base.FiltersForm.FromDate.EditValue = DateTime.Now;
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                result = true;
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                TotalSalesInvoicesReport rpt = new TotalSalesInvoicesReport();
                ICollection<TotalSalesInvoicesReportModel> dataSource = this.GetData();
                rpt.DataSource = dataSource;
                rpt.SetCompanyInfo(dataSource.FirstOrDefault<TotalSalesInvoicesReportModel>());
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<TotalSalesInvoicesReportModel> GetData()
        {
            int[] StoresIDs = base.FiltersForm.Stores.EditValue?.ToArray();
            int[] CustomersIDs = base.FiltersForm.Customers.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<SalesInvoice> filterdInvoices = context.SalesInvoices.Where((SalesInvoice inv) => DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate);
                if (CustomersIDs != null && CustomersIDs.Count() > 0)
                {
                    filterdInvoices = filterdInvoices.Where((SalesInvoice c) => CustomersIDs.Contains(c.CustomerID));
                }
                if (StoresIDs != null && StoresIDs.Count() > 0)
                {
                    filterdInvoices = filterdInvoices.Where((SalesInvoice c) => StoresIDs.Contains(c.StoreID));
                }
                IQueryable<TotalSalesInvoicesReportModel> filterdInv = from x in filterdInvoices
                                                                       orderby x.Date
                                                                       select x into inv
                                                                       join Bran in context.Stores on inv.StoreID equals Bran.ID
                                                                       join cust in context.Customers on inv.CustomerID equals cust.ID
                                                                       from customerCat in context.CustomersGroups.Where((CustomerGroup customerCat) => (int?)customerCat.ID == cust.GroupID).DefaultIfEmpty()
                                                                       select new TotalSalesInvoicesReportModel
                                                                       {
                                                                           InvStore = Bran.Name,
                                                                           CustomerCat = customerCat.Name,
                                                                           Customer = cust.Name,
                                                                           InvoiceID = inv.ID,
                                                                           InvoiceDate = inv.Date,
                                                                           PaidAmount = (context.PayDetails.Where((PayDetail p) => (int)p.SourceType == 4 && p.SourceID == inv.ID).Sum(x => (double?)(x.Amount * x.CurrancyRate) ?? 0.0)),
                                                                           TotalAmount = inv.Total,
                                                                           Expences = inv.OtherExpenses,
                                                                           Discount = inv.Discount,
                                                                           Taxes = inv.Tax,
                                                                           NetAmount = inv.Total + inv.OtherExpenses + inv.Tax - inv.Discount,
                                                                           DueDate = inv.DueDate
                                                                       };
                return filterdInv.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }

    }
}
