﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Catégorie de Produits")]
    public class GroupOfProducts : BaseNotifyPropertyChangedModel
    {
        public GroupOfProducts()
        {
            this.Details = new BindingList<GroupOfProductsDetails>();
        }

        [Display(Name = "N°")]
        [ReadOnly(true)]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Un code unique est requis")]
        public string Code
        {
            get
            {
                return this.code;
            }
            set
            {
                base.SetProperty<string>(ref this.code, value, "Code");
            }
        }

        [Display(Name = "Nom")]
        [Required(AllowEmptyStrings = false, ErrorMessage = "Un nom unique est requis")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Type d'expiration")]
        public GroupOfProductsExpireType ExpireType
        {
            get
            {
                return this.expireType;
            }
            set
            {
                base.SetProperty<GroupOfProductsExpireType>(ref this.expireType, value, "ExpireType");
            }
        }

        [Display(Name = "À partir de")]
        public DateTime? StartDate
        {
            get
            {
                return this.startDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.startDate, value, "StartDate");
            }
        }

        [Display(Name = "Jusqu'au")]
        public DateTime? EndDate
        {
            get
            {
                return this.endDate;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.endDate, value, "EndDate");
            }
        }

        [Display(Name = "Type de réduction")]
        public GroupOfProductDiscountType DiscountType
        {
            get
            {
                return this.discountType;
            }
            set
            {
                base.SetProperty<GroupOfProductDiscountType>(ref this.discountType, value, "DiscountType");
            }
        }

        [Display(Name = "Taux de réduction")]
        [Range(0.0, 1.0, ErrorMessage = "Valeur non valide")]
        public double Discount
        {
            get
            {
                return this.discount;
            }
            set
            {
                base.SetProperty<double>(ref this.discount, value, "Discount");
            }
        }

        [Display(Name = "Autoriser la modification de la quantité des articles lors de la vente")]
        public bool CanChangeQuantity
        {
            get
            {
                return this.canChangeQuantity;
            }
            set
            {
                base.SetProperty<bool>(ref this.canChangeQuantity, value, "CanChangeQuantity");
            }
        }

        [Display(Name = "Articles")]
        public BindingList<GroupOfProductsDetails> Details { get; set; }

        [Display(Name = "Total")]
        public double Total
        {
            get
            {
                BindingList<GroupOfProductsDetails> details = this.Details;
                double result;
                if (details == null)
                {
                    result = 0.0;
                }
                else
                {
                    result = details.Sum((GroupOfProductsDetails x) => x.Total);
                }
                return result;
            }
        }

        private int id;

        private string code;

        private string name;

        private GroupOfProductsExpireType expireType;

        private DateTime? startDate;

        private DateTime? endDate;

        private GroupOfProductDiscountType discountType;

        private double discount;

        private bool canChangeQuantity;
    }
}
