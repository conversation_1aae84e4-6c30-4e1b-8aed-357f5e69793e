﻿using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class SalesProductCatergoriesReportView : XtraReportForm
    {
        public SalesProductCatergoriesReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Product
        })
        {
            this.InitializeComponent();
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                SalesProductCatergotiesMainReport rpt = new SalesProductCatergotiesMainReport();
                ICollection<SalesProductCategoriesMainModel> dataSource = this.GetData();
                rpt.DataSource = dataSource;
                SalesProductCategoriesSubReport Subrpt = new SalesProductCategoriesSubReport();
                ICollection<SalesProductCategoriesSubModel> SubdataSource = this.GetSubData();
                rpt.subReportDataSource = SubdataSource.ToList<SalesProductCategoriesSubModel>();
                rpt.SetCompanyInfo(dataSource.FirstOrDefault<SalesProductCategoriesMainModel>());
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<SalesProductCategoriesMainModel> GetData()
        {
            int[] ProductIDs = base.FiltersForm.Products.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<Product> products = context.Products.AsQueryable();
                if (ProductIDs != null && ProductIDs.Count() > 0)
                {
                    products = products.Where((Product Prod) => ProductIDs.Contains(Prod.ID));
                }
                var filterdTrans = from x in context.ProductTransactions
                                   where (int)x.TransactionState == 2
                                   select x into inv
                                   where (int)inv.Type == 4 && DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate
                                   select new
                                   {
                                       ProductID = inv.ProductID,
                                       Quantity = inv.Quantity * inv.Factor,
                                       Price = inv.Price,
                                       CostValue = inv.CostValue
                                   };
                var data = from tr in filterdTrans
                           group tr by tr.ProductID into g
                           join pr in products on g.Key equals pr.ID
                           from cat in context.ProductCategories.Where((ProductCategory x) => (int?)x.ID == pr.CategoryID).DefaultIfEmpty()
                           select new
                           {
                               Category = ((cat != null) ? cat.Name : "(Sans famille)"),
                               Qty = g.Sum(x => x.Quantity),
                               TotalCost = (g.Sum(x => (double?)x.Quantity * (double?)x.CostValue) ?? 0.0),
                               TotalSales = (g.Sum(x => (double?)x.Quantity * (double?)x.Price) ?? 0.0)
                           };
                IQueryable<SalesProductCategoriesMainModel> CatData = from ct in data
                                                                      group ct by ct.Category into gCat
                                                                      select new SalesProductCategoriesMainModel
                                                                      {
                                                                          Category = gCat.Key,
                                                                          TotalCost = (gCat.Sum(x => (double?)x.TotalCost) ?? 0.0),
                                                                          TotalSales = (gCat.Sum(x => (double?)x.TotalSales) ?? 0.0)
                                                                      };
                return CatData.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }

        private ICollection<SalesProductCategoriesSubModel> GetSubData()
        {
            int[] ProductIDs = base.FiltersForm.Products.EditValue?.ToArray();
            int[] CustomersIDs = base.FiltersForm.Customers.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<Product> products = context.Products.AsQueryable();
                IQueryable<Customer> customers = context.Customers.AsQueryable();
                if (ProductIDs != null && ProductIDs.Count() > 0)
                {
                    products = products.Where((Product Prod) => ProductIDs.Contains(Prod.ID));
                }
                if (CustomersIDs != null && CustomersIDs.Count() > 0)
                {
                    customers = customers.Where((Customer cus) => CustomersIDs.Contains(cus.ID));
                }
                var filterdTrans = from inv in context.InvoicesDetails
                                   join invoice in context.SalesInvoices on inv.BillID equals invoice.ID
                                   where (int)inv.Type == 4 && DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate
                                   select new
                                   {
                                       ProductID = inv.ProductID,
                                       Quantity = inv.Quantity * inv.Factor,
                                       Price = inv.Price,
                                       CostValue = inv.CostValue,
                                       Discount = inv.Discount,
                                       Tax = inv.Tax
                                   };
                IQueryable<SalesProductCategoriesSubModel> data = from tr in filterdTrans
                                                                  group tr by tr.ProductID into g
                                                                  join pr in products on g.Key equals pr.ID
                                                                  from cat in context.ProductCategories.Where((ProductCategory x) => (int?)x.ID == pr.CategoryID).DefaultIfEmpty()
                                                                  select new SalesProductCategoriesSubModel
                                                                  {
                                                                      Category = ((cat != null) ? cat.Name : "(Sans famille)"),
                                                                      ProductName = pr.Name,
                                                                      Qty = (g.Sum(x => (double?)x.Quantity) ?? 0.0),
                                                                      TotalCost = (g.Sum(x => (double?)x.Quantity * (double?)x.CostValue) ?? 0.0),
                                                                      TotalSales = (g.Sum(x => (double?)x.Quantity * (double?)x.Price) ?? 0.0),
                                                                      Discount = (g.Sum(x => (double?)x.Discount) ?? 0.0),
                                                                      Taxes = (g.Sum(x => (double?)x.Tax) ?? 0.0)
                                                                  };
                return data.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
    }
}
