﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    public class FrequentlyUsedScreen
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID { get; set; }

        public int UserID { get; set; }

        public int Count { get; set; }

        public int DayOfWeek { get; set; }

        public int Hour { get; set; }

        public int ScreenID { get; set; }
    }
}
