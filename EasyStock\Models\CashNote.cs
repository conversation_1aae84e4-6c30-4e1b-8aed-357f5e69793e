﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Recus et paiements")]
    public class CashNote : BaseNotifyPropertyChangedModel
    {
        public void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                int maxLength = (from x in db.CashNotes.AsNoTracking()
                                 where (int)x.Type == (int)this.Type
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    this.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.CashNotes.AsNoTracking()
                                      where (int)x.Type == (int)this.Type
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<CashNote>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        this.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        [Display(Name = "N°")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this._id;
            }
            set
            {
                base.SetProperty<int>(ref this._id, value, "ID");
            }
        }

        [Display(Name = "")]
        public Branch Branch { get; set; }

        [Display(Name = "Succursale")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int BranchID { get; set; }

        [Display(Name = "Code")]
        public string Code { get; set; }

        [Display(Name = "Date")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime Date { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Facture")]
        public int? LinkID { get; set; }

        [Display(Name = "Lié au document")]
        public CashLinkType LinkType { get; set; }

        [Display(Name = "Notes")]
        public string Note { get; set; }

        [Display(Name = "Partenaire commercial")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int PersonalID { get; set; }

        [Display(Name = "Type de partenaire commercial")]
        public PersonalType PersonalType { get; set; }

        [Display(Name = "Total payé")]
        public double TotalPaid { get; set; }

        [Display(Name = "Type de document")]
        public CashNoteType Type { get; set; }

        public Journal Journal { get; set; }

        public int JournalID { get; set; }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de coûts")]
        public int? CostCenterID { get; set; }

        [Display(Name = "Utilisateur")]
        public int UserID { get; set; }

        private int _id;

    }
}
