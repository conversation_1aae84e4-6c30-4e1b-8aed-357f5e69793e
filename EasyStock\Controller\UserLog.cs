﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using EasyStock.Common;

namespace EasyStock.Controller
{
	public class UserLog
	{
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		[Display(Name = "Date")]
		public DateTime Date { get; set; }
		[Display(Name = "Code utilisateur")]
		public int UserID { get; set; }
		[Display(Name = "Nom de l'écran")]
		public string ScreenName { get; set; }
		public string Screen { get; set; }

		[Display(Name = "Action")]
		public WindowActions Action { get; set; }

		[Display(Name = "�����")]
		public int EntityID { get; set; }

		[Display(Name = "�����")]
		public string EntityCode { get; set; }

		[Display(Name = "������ �����")]
		public List<ChangeTrackerItem> ChangeTrackerItems { get; set; }
	}
}
