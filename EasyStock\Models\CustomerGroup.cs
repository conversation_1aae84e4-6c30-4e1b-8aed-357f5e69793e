﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    public class CustomerGroup : PersonalGroup
    {
        [Display(Name = "Clients")]
        [NotMapped]
        public ICollection<Customer> Customers
        {
            get
            {
                return (ICollection<Customer>)base.Personals;
            }
            set
            {
                base.Personals = (ICollection<Personal>)value;
            }
        }
    }
}
