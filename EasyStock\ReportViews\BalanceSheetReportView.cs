﻿using DevExpress.Data;
using DevExpress.Utils;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.ReportModels.IncomStatment;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;

namespace EasyStock.ReportViews
{
    public partial class BalanceSheetReportView : ReportForm
    {
        public BalanceSheetReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date
        }, typeof(BalanceSheetReportModel))
        {
            this.InitializeComponent();
            base.FiltersForm.FromDate.EditValue = new DateTime(DateTime.Now.Year, 1, 1);
            base.FiltersForm.ToDate.EditValue = new DateTime(DateTime.Now.Year, 12, 31);
            this.bandedGridView = new BandedGridView();
            this.gridControl1.ViewCollection.Clear();
            this.gridControl1.MainView = this.bandedGridView;
            this.gridControl1.ViewCollection.Add(this.bandedGridView);
            this.bandedGridView.Columns.Clear();
            this.col_A_AccountName = this.bandedGridView.Columns.AddField("A_AccountName");
            this.col_L_AccountName = this.bandedGridView.Columns.AddField("L_AccountName");
            this.col_A_AccountBalance = this.bandedGridView.Columns.AddField("A_AccountBalance");
            this.col_L_AccountBalance = this.bandedGridView.Columns.AddField("L_AccountBalance");
            this.bandedGridView.OptionsView.ShowFooter = true;
            this.bandedGridView.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView.Appearance.BandPanel.TextOptions.HAlignment = HorzAlignment.Center;
            this.bandedGridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView.Appearance.HeaderPanel.TextOptions.HAlignment = HorzAlignment.Center;
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsBehavior.FocusLeaveOnTab = true;
            this.bandedGridView.OptionsNavigation.EnterMoveNextColumn = true;
            this.bandedGridView.OptionsView.EnableAppearanceEvenRow = true;
            this.bandedGridView.OptionsView.RowAutoHeight = true;
            this.bandedGridView.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView.OptionsView.ShowFooter = true;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            this.col_A_AccountName.Caption = "Compte";
            this.col_L_AccountName.Caption = "Compte";
            this.col_A_AccountBalance.Caption = "Total partiel";
            this.col_L_AccountBalance.Caption = "Total partiel";

            BandedGridColumn colA_Final = new BandedGridColumn
            {
                Name = "A_FinalTotal",
                FieldName = "A_FinalTotal",
                Caption = "Total général",
                UnboundType = UnboundColumnType.Decimal,
                VisibleIndex = 0
            };

            BandedGridColumn colL_Final = new BandedGridColumn
            {
                Name = "L_FinalTotal",
                FieldName = "L_FinalTotal",
                Caption = "Total général",
                UnboundType = UnboundColumnType.Decimal,
                VisibleIndex = 0
            };

            this.bandedGridView.Columns.Add(colA_Final);
            this.bandedGridView.Columns.Add(colL_Final);

            GridBand A_Band = new GridBand
            {
                Name = "A_Band",
                Caption = "Actifs",
                VisibleIndex = 0
            };

            A_Band.Columns.Add(colA_Final);
            A_Band.Columns.Add(this.col_A_AccountBalance);
            A_Band.Columns.Add(this.col_A_AccountName);

            GridBand L_Band = new GridBand
            {
                Name = "L_Band",
                Caption = "Passifs et Capitaux propres",
                VisibleIndex = 1
            };

            L_Band.Columns.Add(colL_Final);
            L_Band.Columns.Add(this.col_L_AccountBalance);
            L_Band.Columns.Add(this.col_L_AccountName);
            this.col_A_AccountBalance.VisibleIndex = 0;
            this.col_L_AccountBalance.VisibleIndex = 0;
            this.col_A_AccountName.VisibleIndex = 0;
            this.col_L_AccountName.VisibleIndex = 0;
            this.bandedGridView.Bands.Clear();
            this.bandedGridView.Bands.AddRange(new GridBand[]
            {
                A_Band,
                L_Band
            });
            A_Band.AppearanceHeader.FontStyleDelta = FontStyle.Bold;
            A_Band.AppearanceHeader.FontSizeDelta = 2;
            L_Band.AppearanceHeader.FontStyleDelta = FontStyle.Bold;
            L_Band.AppearanceHeader.FontSizeDelta = 2;
            A_Band.RowCount = 2;
            L_Band.RowCount = 2;
            base.Load += this.BalanceSheetReportView_Load;
        }

        private void BalanceSheetReportView_Load(object sender, EventArgs e)
        {
            DateTime date = DateTime.Now.AddMonths(-1);
            base.FiltersForm.FromDate.EditValue = new DateTime(date.Year, 1, 1);
            base.FiltersForm.ToDate.EditValue = new DateTime(date.Year, 12, 31);
            this.bandedGridView.CustomColumnDisplayText += this.bandedGridView_CustomColumnDisplayText;
            this.bandedGridView.RowCellStyle += this.BandedGridView_RowCellStyle;
            this.bandedGridView.CustomUnboundColumnData += this.bandedGridView_CustomUnboundColumnData;
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void Print()
        {
            GridReportP.Print(this.layoutControl1, this.Text, this.labelControl1.Text, false);
        }

        public override void RefreshDataSource()
        {
            if (!ValidateFilters())
            {
                return;
            }
            DateTime startDate = base.FiltersForm.startDate.Date;
            DateTime EndDate = base.FiltersForm.endtDate.Date;

            IQueryable<JournalDetail> journalDetails = from j in db.JournalDetails.Include(j => j.Journal)
                                                       where DbFunctions.TruncateTime(j.Journal.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(j.Journal.Date) <= FiltersForm.endtDate
                                                       select j;

            // Retrieve account details
            Account AssetAccount = db.Accounts.SingleOrDefault(x => x.ID == ERPDataContext.SystemSettings.AssetsAccount);
            Account CurrentAssetAccount = db.Accounts.SingleOrDefault(x => x.ID == ERPDataContext.SystemSettings.CurrentAssetsAccount);
            Account liabilitiesAndOwnersEquityAccount = db.Accounts.SingleOrDefault(x => x.ID == ERPDataContext.SystemSettings.LiabilitiesAndOwnersEquity);
            Account OwnersEquityAccount = db.Accounts.SingleOrDefault(x => x.ID == ERPDataContext.SystemSettings.OwnersEquityAccount);

            IQueryable<JournalDetail> AQ1 = from x in journalDetails.Include(x => x.Account)
                                                            where x.Account.Number.Contains(AssetAccount.Number)
                                                            select x;

            IQueryable<JournalDetail> LQ1 = from x in journalDetails.Include(x => x.Account)
                                                                  where x.Account.Number.Contains(liabilitiesAndOwnersEquityAccount.Number)
                                                                  select x;

            List<BalanceSheetReportModel> AQ2 = (from jd in AQ1
                                                           group jd by jd.AccountID into g
                                                           join ac in db.Accounts on g.Key equals ac.ID
                                                           select new BalanceSheetReportModel
                                                           {
                                                               A_AccountBalance = (double?)g.Sum(x => x.Credit * x.CurrencyRate) ?? 0.00 - (double?)g.Sum(x => x.Debit * x.CurrencyRate) ?? 0.00,
                                                               A_AccountLevel = ((ac.Number != null) ? ac.Number.Length : 3),
                                                               A_AccountName = ac.Name,
                                                               A_AccountNumber = ac.Number,
                                                               A_IsParent = false
                                                           }).ToList();

            // Calculate account balances for liabilities accounts
            List<BalanceSheetReportModel> LQ2 = (from jd in LQ1
                                                 group jd by jd.AccountID into g
                                                                 join ac in db.Accounts on g.Key equals ac.ID
                                                                 select new BalanceSheetReportModel
                                                                 {
                                                                     L_AccountBalance = g.Sum(x => x.Credit * x.CurrencyRate) - g.Sum(x => x.Debit * x.CurrencyRate),
                                                                     L_AccountLevel = ac.Number.Length,
                                                                     L_AccountName = ac.Name,
                                                                     L_AccountNumber = ac.Number,
                                                                     L_IsParent = false
                                                                 }).ToList();


            IncomeStatmentModel IncomeStatmentData = IncomeStatmentReportView.GetIncomeStatment(startDate, EndDate);
            BalanceSheetReportModel incomeSatetment = new BalanceSheetReportModel
            {
                L_AccountBalance = IncomeStatmentData.NetProfits.Credit - IncomeStatmentData.NetProfits.Debit,
                L_AccountLevel = OwnersEquityAccount.Number.Length + 2,
                L_AccountName = IncomeStatmentData.NetProfits.Account,
                L_AccountNumber = OwnersEquityAccount.Number + "0A",
                L_IsParent = false
            };
            LQ2.Add(incomeSatetment);
            BalanceSheetReportModel ClosingStock = new BalanceSheetReportModel
            {
                A_AccountBalance = IncomeStatmentData.ClosingStock.Debit - IncomeStatmentData.ClosingStock.Credit,
                A_AccountLevel = CurrentAssetAccount.Number.Length + 2,
                A_AccountName = IncomeStatmentData.ClosingStock.Account,
                A_AccountNumber = CurrentAssetAccount.Number + "__",
                A_IsParent = false
            };
            AQ2.Add(ClosingStock);
            List<BalanceSheetReportModel> all_A_Accounts = (from x in db.Accounts.Where((Account x) => x.Number.IndexOf(AssetAccount.Number) >= 0 && x.ID != AssetAccount.ID).ToList()
                                                            where !AQ2.Select((BalanceSheetReportModel a) => a.A_AccountNumber).Contains(x.Number)
                                                            select new BalanceSheetReportModel
                                                            {
                                                                A_AccountBalance = AQ2.Where((BalanceSheetReportModel c) => c != null && c.A_AccountNumber?.IndexOf(x.Number) >= 0).Sum((Func<BalanceSheetReportModel, double?>)((BalanceSheetReportModel c) => c.A_AccountBalance)).GetValueOrDefault(),
                                                                A_AccountLevel = x.Number.Length,
                                                                A_AccountName = x.Name,
                                                                A_AccountNumber = x.Number,
                                                                A_IsParent = true
                                                            } into x
                                                            where x.A_AccountBalance != 0.0
                                                            select x).ToList();
            AQ2.AddRange(all_A_Accounts);
            List<BalanceSheetReportModel> all_L_Accounts = (from x in db.Accounts.Where((Account x) => x.Number.IndexOf(liabilitiesAndOwnersEquityAccount.Number) >= 0 && x.ID != liabilitiesAndOwnersEquityAccount.ID).ToList()
                                                            where !LQ2.Select((BalanceSheetReportModel a) => a.L_AccountNumber).Contains(x.Number)
                                                            select new BalanceSheetReportModel
                                                            {
                                                                L_AccountBalance = LQ2.Where((BalanceSheetReportModel c) => c != null && c.L_AccountNumber?.IndexOf(x.Number) >= 0).Sum((Func<BalanceSheetReportModel, double?>)((BalanceSheetReportModel c) => c.L_AccountBalance)).GetValueOrDefault(),
                                                                L_AccountLevel = x.Number.Length,
                                                                L_AccountName = x.Name,
                                                                L_AccountNumber = x.Number,
                                                                L_IsParent = true
                                                            } into x
                                                            where x.L_AccountBalance != 0.0
                                                            select x).ToList();
            LQ2.AddRange(all_L_Accounts);
            AQ2 = AQ2.OrderBy((BalanceSheetReportModel x) => x.A_AccountNumber).ToList();
            LQ2 = LQ2.OrderBy((BalanceSheetReportModel x) => x.L_AccountNumber).ToList();
            List<BalanceSheetReportModel> data = new List<BalanceSheetReportModel>();
            int maxCount = ((AQ2.Count > LQ2.Count) ? AQ2.Count : LQ2.Count);
            for (int i = 0; i < maxCount; i++)
            {
                BalanceSheetReportModel item = new BalanceSheetReportModel();
                if (AQ2.Count > i)
                {
                    item.A_AccountBalance = AQ2[i].A_AccountBalance;
                    item.A_AccountLevel = AQ2[i].A_AccountLevel;
                    item.A_AccountName = AQ2[i].A_AccountName;
                    item.A_AccountNumber = AQ2[i].A_AccountNumber;
                    item.A_IsParent = AQ2[i].A_IsParent;
                }
                if (LQ2.Count > i)
                {
                    item.L_AccountBalance = LQ2[i].L_AccountBalance;
                    item.L_AccountLevel = LQ2[i].L_AccountLevel;
                    item.L_AccountName = LQ2[i].L_AccountName;
                    item.L_AccountNumber = LQ2[i].L_AccountNumber;
                    item.L_IsParent = LQ2[i].L_IsParent;
                }
                data.Add(item);
            }
            gridControl1.DataSource = data;
            IEnumerable<BalanceSheetReportModel> A_ChildsOnly = data.Where((BalanceSheetReportModel x) => !x.A_IsParent);
            double A_sum = Math.Abs(A_ChildsOnly.Sum((BalanceSheetReportModel x) => x.A_AccountBalance));
            IEnumerable<BalanceSheetReportModel> L_ChildsOnly = data.Where((BalanceSheetReportModel x) => !x.L_IsParent);
            double L_sum = Math.Abs(L_ChildsOnly.Sum((BalanceSheetReportModel x) => x.L_AccountBalance));
            col_A_AccountBalance.SummaryItem.SetSummary(SummaryItemType.Custom, A_sum.ToString());
            col_L_AccountBalance.SummaryItem.SetSummary(SummaryItemType.Custom, L_sum.ToString());
        }

        private void BandedGridView_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            bool flag = e.Column.FieldName.Contains("A_");
            if (flag)
            {
                object row = this.bandedGridView.GetRowCellValue(e.RowHandle, "A_IsParent");
                bool flag2 = row != null && row.GetType() == typeof(bool);
                if (flag2)
                {
                    bool flag3 = row.Equals(true);
                    if (flag3)
                    {
                        e.Appearance.BackColor = Color.LightGray;
                        e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
                        e.Appearance.TextOptions.HAlignment = HorzAlignment.Near;
                    }
                }
            }
            else
            {
                bool flag4 = e.Column.FieldName.Contains("L_");
                if (flag4)
                {
                    object row2 = this.bandedGridView.GetRowCellValue(e.RowHandle, "L_IsParent");
                    bool flag5 = row2 != null && row2.GetType() == typeof(bool);
                    if (flag5)
                    {
                        bool flag6 = row2.Equals(true);
                        if (flag6)
                        {
                            e.Appearance.BackColor = Color.LightGray;
                            e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
                            e.Appearance.TextOptions.HAlignment = HorzAlignment.Near;
                        }
                    }
                }
            }
        }

        private void bandedGridView_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            bool flag = e.Column.FieldName == "A_FinalTotal";
            if (flag)
            {
                BalanceSheetReportModel row = this.bandedGridView.GetRow(this.bandedGridView.GetRowHandle(e.ListSourceRowIndex)) as BalanceSheetReportModel;
                bool flag2 = row == null;
                if (flag2)
                {
                    return;
                }
                bool a_IsParent = row.A_IsParent;
                if (a_IsParent)
                {
                    e.Value = Math.Round(row.A_AccountBalance, 2);
                }
            }
            bool flag3 = e.Column.FieldName == "L_FinalTotal";
            if (flag3)
            {
                BalanceSheetReportModel row2 = this.bandedGridView.GetRow(this.bandedGridView.GetRowHandle(e.ListSourceRowIndex)) as BalanceSheetReportModel;
                bool flag4 = row2 == null;
                if (!flag4)
                {
                    bool l_IsParent = row2.L_IsParent;
                    if (l_IsParent)
                    {
                        e.Value = Math.Round(row2.L_AccountBalance, 2);
                    }
                }
            }
        }
        private void bandedGridView_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (!(bandedGridView.GetRow(bandedGridView.GetRowHandle(e.ListSourceRowIndex)) is BalanceSheetReportModel row))
            {
                return;
            }
            if (e.Value is double value && value == 0.0)
            {
                e.DisplayText = string.Empty;
            }
            else if (e.Column == col_A_AccountName)
            {
                object value2 = e.Value;
                if (value2 != null && value2.ToString()?.Length > 0)
                {
                    e.DisplayText = ".".PadLeft(row.A_AccountLevel - 4) + row.A_AccountName;
                }
            }
            else if (e.Column == col_L_AccountName)
            {
                object value3 = e.Value;
                if (value3 != null && value3.ToString()?.Length > 0)
                {
                    e.DisplayText = ".".PadLeft(row.L_AccountLevel - 4) + row.L_AccountName;
                }
            }
            else if (e.Column == col_A_AccountBalance)
            {
                if (row.A_IsParent)
                {
                    e.DisplayText = string.Empty;
                }
                else
                {
                    e.DisplayText = ((row.A_AccountBalance < 0.0) ? Math.Abs(row.A_AccountBalance).ToString() : $"({Math.Abs(row.A_AccountBalance)})");
                }
            }
            else if (e.Column == col_L_AccountBalance)
            {
                if (row.L_IsParent)
                {
                    e.DisplayText = string.Empty;
                }
                else
                {
                    e.DisplayText = ((row.L_AccountBalance > 0.0) ? Math.Abs(row.L_AccountBalance).ToString() : $"({Math.Abs(row.L_AccountBalance)})");
                }
            }
            else if (e.Column.FieldName == "A_FinalTotal" && e.Value is double val1)
            {
                if (!row.A_IsParent)
                {
                    e.DisplayText = string.Empty;
                }
                else
                {
                    e.DisplayText = ((val1 < 0.0) ? Math.Abs(val1).ToString() : $"({Math.Abs(val1)})");
                }
            }
            else if (e.Column.FieldName == "L_FinalTotal" && e.Value is double val2)
            {
                if (!row.L_IsParent)
                {
                    e.DisplayText = string.Empty;
                }
                else
                {
                    e.DisplayText = ((val2 > 0.0) ? Math.Abs(val2).ToString() : $"({Math.Abs(val2)})");
                }
            }
        }

        private BandedGridColumn col_A_AccountName;
        private BandedGridColumn col_L_AccountName;
        private BandedGridColumn col_A_AccountBalance;
        private BandedGridColumn col_L_AccountBalance;
        private BandedGridView bandedGridView;
    }
}
