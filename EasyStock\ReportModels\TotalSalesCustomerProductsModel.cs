﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class TotalSalesCustomerProductsModel : CompanyInfoReportModel
    {
        [Display(Name = "Client")]
        public string Customer { get; set; }

        [Display(Name = "Article")]
        public string Product { get; set; }

        [Display(Name = "Nombre de factures")]
        public int InvoiceCount { get; set; }

        [Display(Name = "Quantité")]
        public double Qty { get; set; }

        [Display(Name = "Total")]
        public double TotalAmount { get; set; }
    }
}
