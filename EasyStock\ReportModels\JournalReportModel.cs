﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class JournalReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Display(Name = "Code de l'écriture")]
        public string Code { get; set; }

        [Display(Name = "Date", GroupName = "Informations")]
        public DateTime Date { get; set; }

        [Display(Name = "Notes", GroupName = "Informations")]
        public string Note { get; set; }

        [Display(Name = "Utilisateur", GroupName = "Détails de la facture")]
        public string UserID { get; set; }

        [Display(Name = "Source de l'entrée", GroupName = "Informations")]
        public SystemProcess ProcessType { get; set; }

        [Display(Name = "Code Source", GroupName = "Informations")]
        public int ProcessID { get; set; }

        [Display(Name = "Détails", GroupName = "Détails")]
        public ICollection<JournalDetailReportModel> Details { get; set; }
    }
}
