﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.Parameters;
using DevExpress.XtraReports.UI;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x02000372 RID: 882
	public class SalesProductCategoriesSubReport : XtraReport
	{
		// Token: 0x060014FF RID: 5375 RVA: 0x0000AB8F File Offset: 0x00008D8F
		public SalesProductCategoriesSubReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001500 RID: 5376 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void SalesProductCategoriesSubReport_BeforePrint(object sender, PrintEventArgs e)
		{
		}

		// Token: 0x06001501 RID: 5377 RVA: 0x0018FEC0 File Offset: 0x0018E0C0
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001502 RID: 5378 RVA: 0x0018FEF8 File Offset: 0x0018E0F8
		private void InitializeComponent()
		{
			this.components = new Container();
			XRSummary xrSummary = new XRSummary();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.GroupHeader1 = new GroupHeaderBand();
			this.table1 = new XRTable();
			this.tableRow1 = new XRTableRow();
			this.tableCell1 = new XRTableCell();
			this.tableCell2 = new XRTableCell();
			this.tableCell3 = new XRTableCell();
			this.tableCell4 = new XRTableCell();
			this.tableCell5 = new XRTableCell();
			this.tableCell6 = new XRTableCell();
			this.tableCell7 = new XRTableCell();
			this.tableCell8 = new XRTableCell();
			this.tableCell9 = new XRTableCell();
			this.tableCell10 = new XRTableCell();
			this.Detail = new DetailBand();
			this.table2 = new XRTable();
			this.tableRow2 = new XRTableRow();
			this.tableCell12 = new XRTableCell();
			this.tableCell13 = new XRTableCell();
			this.tableCell14 = new XRTableCell();
			this.tableCell15 = new XRTableCell();
			this.tableCell16 = new XRTableCell();
			this.tableCell17 = new XRTableCell();
			this.tableCell18 = new XRTableCell();
			this.tableCell19 = new XRTableCell();
			this.tableCell20 = new XRTableCell();
			this.tableCell21 = new XRTableCell();
			this.tableCell22 = new XRTableCell();
			this.Title = new XRControlStyle();
			this.DetailCaption1 = new XRControlStyle();
			this.DetailData1 = new XRControlStyle();
			this.DetailData3_Odd = new XRControlStyle();
			this.PageInfo = new XRControlStyle();
			this.Cat = new DevExpress.XtraReports.Parameters.Parameter();
			this.objectDataSource2 = new ObjectDataSource(this.components);
			this.tableCell11 = new XRTableCell();
			((ISupportInitialize)this.table1).BeginInit();
			((ISupportInitialize)this.table2).BeginInit();
			((ISupportInitialize)this.objectDataSource2).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.TopMargin.HeightF = 0f;
			this.TopMargin.Name = "TopMargin";
			this.BottomMargin.HeightF = 4.458332f;
			this.BottomMargin.Name = "BottomMargin";
			this.GroupHeader1.Controls.AddRange(new XRControl[]
			{
				this.table1
			});
			this.GroupHeader1.GroupUnion = GroupUnion.WithFirstDetail;
			this.GroupHeader1.HeightF = 26.95834f;
			this.GroupHeader1.KeepTogether = true;
			this.GroupHeader1.Name = "GroupHeader1";
			this.GroupHeader1.RepeatEveryPage = true;
			this.table1.LocationFloat = new PointFloat(2.145767E-05f, 0f);
			this.table1.Name = "table1";
			this.table1.Rows.AddRange(new XRTableRow[]
			{
				this.tableRow1
			});
			this.table1.SizeF = new SizeF(1044f, 26f);
			this.tableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.tableCell1,
				this.tableCell2,
				this.tableCell3,
				this.tableCell4,
				this.tableCell5,
				this.tableCell6,
				this.tableCell7,
				this.tableCell8,
				this.tableCell9,
				this.tableCell10,
				this.tableCell11
			});
			this.tableRow1.Name = "tableRow1";
			this.tableRow1.Weight = 1.0;
			this.tableCell1.BackColor = Color.LightBlue;
			this.tableCell1.Borders = BorderSide.None;
			this.tableCell1.BorderWidth = 1f;
			this.tableCell1.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell1.ForeColor = Color.Black;
			this.tableCell1.Name = "tableCell1";
			this.tableCell1.StyleName = "DetailCaption1";
			this.tableCell1.StylePriority.UseBackColor = false;
			this.tableCell1.StylePriority.UseBorders = false;
			this.tableCell1.StylePriority.UseBorderWidth = false;
			this.tableCell1.StylePriority.UseFont = false;
			this.tableCell1.StylePriority.UseForeColor = false;
			this.tableCell1.StylePriority.UseTextAlignment = false;
			this.tableCell1.Text = "م";
			this.tableCell1.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell1.Weight = 0.031547594312885646;
			this.tableCell2.BackColor = Color.LightBlue;
			this.tableCell2.BorderWidth = 1f;
			this.tableCell2.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell2.ForeColor = Color.Black;
			this.tableCell2.Multiline = true;
			this.tableCell2.Name = "tableCell2";
			this.tableCell2.StyleName = "DetailCaption1";
			this.tableCell2.StylePriority.UseBackColor = false;
			this.tableCell2.StylePriority.UseBorderWidth = false;
			this.tableCell2.StylePriority.UseFont = false;
			this.tableCell2.StylePriority.UseForeColor = false;
			this.tableCell2.StylePriority.UseTextAlignment = false;
			this.tableCell2.Text = "الصنف \r\n";
			this.tableCell2.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell2.Weight = 0.12407713210455779;
			this.tableCell3.BackColor = Color.LightBlue;
			this.tableCell3.BorderWidth = 1f;
			this.tableCell3.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell3.ForeColor = Color.Black;
			this.tableCell3.Name = "tableCell3";
			this.tableCell3.StyleName = "DetailCaption1";
			this.tableCell3.StylePriority.UseBackColor = false;
			this.tableCell3.StylePriority.UseBorderWidth = false;
			this.tableCell3.StylePriority.UseFont = false;
			this.tableCell3.StylePriority.UseForeColor = false;
			this.tableCell3.StylePriority.UseTextAlignment = false;
			this.tableCell3.Text = "الكمية";
			this.tableCell3.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell3.Weight = 0.06843649063352836;
			this.tableCell4.BackColor = Color.LightBlue;
			this.tableCell4.BorderWidth = 1f;
			this.tableCell4.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell4.ForeColor = Color.Black;
			this.tableCell4.Name = "tableCell4";
			this.tableCell4.StyleName = "DetailCaption1";
			this.tableCell4.StylePriority.UseBackColor = false;
			this.tableCell4.StylePriority.UseBorderWidth = false;
			this.tableCell4.StylePriority.UseFont = false;
			this.tableCell4.StylePriority.UseForeColor = false;
			this.tableCell4.StylePriority.UseTextAlignment = false;
			this.tableCell4.Text = "اجمالى التكلفة";
			this.tableCell4.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell4.Weight = 0.09993690592787682;
			this.tableCell5.BackColor = Color.LightBlue;
			this.tableCell5.BorderWidth = 1f;
			this.tableCell5.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell5.ForeColor = Color.Black;
			this.tableCell5.Name = "tableCell5";
			this.tableCell5.StyleName = "DetailCaption1";
			this.tableCell5.StylePriority.UseBackColor = false;
			this.tableCell5.StylePriority.UseBorderWidth = false;
			this.tableCell5.StylePriority.UseFont = false;
			this.tableCell5.StylePriority.UseForeColor = false;
			this.tableCell5.StylePriority.UseTextAlignment = false;
			this.tableCell5.Text = "اجمالى سعر البيع";
			this.tableCell5.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell5.Weight = 0.10918620106294326;
			this.tableCell6.BackColor = Color.LightBlue;
			this.tableCell6.BorderWidth = 1f;
			this.tableCell6.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell6.ForeColor = Color.Black;
			this.tableCell6.Name = "tableCell6";
			this.tableCell6.StyleName = "DetailCaption1";
			this.tableCell6.StylePriority.UseBackColor = false;
			this.tableCell6.StylePriority.UseBorderWidth = false;
			this.tableCell6.StylePriority.UseFont = false;
			this.tableCell6.StylePriority.UseForeColor = false;
			this.tableCell6.StylePriority.UseTextAlignment = false;
			this.tableCell6.Text = "خصم نقدى";
			this.tableCell6.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell6.Weight = 0.1177968312517543;
			this.tableCell7.BackColor = Color.LightBlue;
			this.tableCell7.BorderWidth = 1f;
			this.tableCell7.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell7.ForeColor = Color.Black;
			this.tableCell7.Name = "tableCell7";
			this.tableCell7.StyleName = "DetailCaption1";
			this.tableCell7.StylePriority.UseBackColor = false;
			this.tableCell7.StylePriority.UseBorderWidth = false;
			this.tableCell7.StylePriority.UseFont = false;
			this.tableCell7.StylePriority.UseForeColor = false;
			this.tableCell7.StylePriority.UseTextAlignment = false;
			this.tableCell7.Text = "اجمالى بعد الخصم";
			this.tableCell7.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell7.Weight = 0.11036785810477152;
			this.tableCell8.BackColor = Color.LightBlue;
			this.tableCell8.BorderWidth = 1f;
			this.tableCell8.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell8.ForeColor = Color.Black;
			this.tableCell8.Name = "tableCell8";
			this.tableCell8.StyleName = "DetailCaption1";
			this.tableCell8.StylePriority.UseBackColor = false;
			this.tableCell8.StylePriority.UseBorderWidth = false;
			this.tableCell8.StylePriority.UseFont = false;
			this.tableCell8.StylePriority.UseForeColor = false;
			this.tableCell8.StylePriority.UseTextAlignment = false;
			this.tableCell8.Text = "ضريبة مضافة";
			this.tableCell8.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell8.Weight = 0.09877097377225062;
			this.tableCell9.BackColor = Color.LightBlue;
			this.tableCell9.BorderWidth = 1f;
			this.tableCell9.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell9.ForeColor = Color.Black;
			this.tableCell9.Name = "tableCell9";
			this.tableCell9.StyleName = "DetailCaption1";
			this.tableCell9.StylePriority.UseBackColor = false;
			this.tableCell9.StylePriority.UseBorderWidth = false;
			this.tableCell9.StylePriority.UseFont = false;
			this.tableCell9.StylePriority.UseForeColor = false;
			this.tableCell9.StylePriority.UseTextAlignment = false;
			this.tableCell9.Text = "اجمالي بعد الضريبة";
			this.tableCell9.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell9.Weight = 0.10472754686077729;
			this.tableCell10.BackColor = Color.LightBlue;
			this.tableCell10.Borders = (BorderSide.Left | BorderSide.Right);
			this.tableCell10.BorderWidth = 1f;
			this.tableCell10.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell10.ForeColor = Color.Black;
			this.tableCell10.Name = "tableCell10";
			this.tableCell10.StyleName = "DetailCaption1";
			this.tableCell10.StylePriority.UseBackColor = false;
			this.tableCell10.StylePriority.UseBorders = false;
			this.tableCell10.StylePriority.UseBorderWidth = false;
			this.tableCell10.StylePriority.UseFont = false;
			this.tableCell10.StylePriority.UseForeColor = false;
			this.tableCell10.StylePriority.UseTextAlignment = false;
			this.tableCell10.Text = "هامش الربح";
			this.tableCell10.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell10.Weight = 0.07497201947617878;
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.table2
			});
			this.Detail.HeightF = 25f;
			this.Detail.Name = "Detail";
			this.table2.LocationFloat = new PointFloat(0f, 0f);
			this.table2.Name = "table2";
			this.table2.OddStyleName = "DetailData3_Odd";
			this.table2.Rows.AddRange(new XRTableRow[]
			{
				this.tableRow2
			});
			this.table2.SizeF = new SizeF(1044f, 25f);
			this.tableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.tableCell12,
				this.tableCell13,
				this.tableCell14,
				this.tableCell15,
				this.tableCell16,
				this.tableCell17,
				this.tableCell18,
				this.tableCell19,
				this.tableCell20,
				this.tableCell21,
				this.tableCell22
			});
			this.tableRow2.Name = "tableRow2";
			this.tableRow2.Weight = 11.5;
			this.tableCell12.Borders = BorderSide.None;
			this.tableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "sumRecordNumber([Category])")
			});
			this.tableCell12.Name = "tableCell12";
			this.tableCell12.StyleName = "DetailData1";
			this.tableCell12.StylePriority.UseBorders = false;
			this.tableCell12.StylePriority.UseTextAlignment = false;
			xrSummary.Running = SummaryRunning.Report;
			this.tableCell12.Summary = xrSummary;
			this.tableCell12.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell12.Weight = 0.031547590461403825;
			this.tableCell13.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ProductName]")
			});
			this.tableCell13.Name = "tableCell13";
			this.tableCell13.StyleName = "DetailData1";
			this.tableCell13.StylePriority.UseTextAlignment = false;
			this.tableCell13.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell13.Weight = 0.12407721755388396;
			this.tableCell14.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Qty]")
			});
			this.tableCell14.Name = "tableCell14";
			this.tableCell14.StyleName = "DetailData1";
			this.tableCell14.StylePriority.UseTextAlignment = false;
			this.tableCell14.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell14.TextFormatString = "{0:#.00}";
			this.tableCell14.Weight = 0.06843634641836815;
			this.tableCell15.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalCost]")
			});
			this.tableCell15.Name = "tableCell15";
			this.tableCell15.StyleName = "DetailData1";
			this.tableCell15.StylePriority.UseTextAlignment = false;
			this.tableCell15.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell15.TextFormatString = "{0:#.00}";
			this.tableCell15.Weight = 0.09993708095319948;
			this.tableCell16.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalSales]")
			});
			this.tableCell16.Name = "tableCell16";
			this.tableCell16.StyleName = "DetailData1";
			this.tableCell16.StylePriority.UseTextAlignment = false;
			this.tableCell16.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell16.TextFormatString = "{0:#.00}";
			this.tableCell16.Weight = 0.10918625909290751;
			this.tableCell17.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Discount]")
			});
			this.tableCell17.Name = "tableCell17";
			this.tableCell17.StyleName = "DetailData1";
			this.tableCell17.StylePriority.UseTextAlignment = false;
			this.tableCell17.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell17.TextFormatString = "{0:#.00}";
			this.tableCell17.Weight = 0.11779663506904245;
			this.tableCell18.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalAfterDiscount]")
			});
			this.tableCell18.Name = "tableCell18";
			this.tableCell18.StyleName = "DetailData1";
			this.tableCell18.StylePriority.UseTextAlignment = false;
			this.tableCell18.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell18.TextFormatString = "{0:#.00}";
			this.tableCell18.Weight = 0.11036800448974997;
			this.tableCell19.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Taxes]")
			});
			this.tableCell19.Name = "tableCell19";
			this.tableCell19.StyleName = "DetailData1";
			this.tableCell19.StylePriority.UseTextAlignment = false;
			this.tableCell19.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell19.TextFormatString = "{0:#.00}";
			this.tableCell19.Weight = 0.09877080344840632;
			this.tableCell20.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[NetAmount]")
			});
			this.tableCell20.Name = "tableCell20";
			this.tableCell20.StyleName = "DetailData1";
			this.tableCell20.StylePriority.UseTextAlignment = false;
			this.tableCell20.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell20.TextFormatString = "{0:#.00}";
			this.tableCell20.Weight = 0.10472762677386417;
			this.tableCell21.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ProfitMargin]")
			});
			this.tableCell21.Name = "tableCell21";
			this.tableCell21.StyleName = "DetailData1";
			this.tableCell21.StylePriority.UseTextAlignment = false;
			this.tableCell21.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell21.TextFormatString = "{0:#.00}";
			this.tableCell21.Weight = 0.07497200834173723;
			this.tableCell22.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ProfitMarginPer]")
			});
			this.tableCell22.Name = "tableCell22";
			this.tableCell22.StyleName = "DetailData1";
			this.tableCell22.StylePriority.UseTextAlignment = false;
			this.tableCell22.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell22.TextFormatString = "{0:0.00%}";
			this.tableCell22.Weight = 0.06596085242321659;
			this.Title.BackColor = Color.Transparent;
			this.Title.BorderColor = Color.Black;
			this.Title.Borders = BorderSide.None;
			this.Title.BorderWidth = 1f;
			this.Title.Font = new Font("Arial", 14.25f);
			this.Title.ForeColor = Color.FromArgb(64, 70, 80);
			this.Title.Name = "Title";
			this.Title.Padding = new PaddingInfo(6, 6, 0, 0, 100f);
			this.DetailCaption1.BackColor = Color.FromArgb(153, 212, 246);
			this.DetailCaption1.BorderColor = Color.White;
			this.DetailCaption1.Borders = BorderSide.Left;
			this.DetailCaption1.BorderWidth = 2f;
			this.DetailCaption1.Font = new Font("Arial", 8.25f, FontStyle.Bold);
			this.DetailCaption1.ForeColor = Color.FromArgb(64, 70, 80);
			this.DetailCaption1.Name = "DetailCaption1";
			this.DetailCaption1.Padding = new PaddingInfo(6, 6, 0, 0, 100f);
			this.DetailCaption1.TextAlignment = TextAlignment.MiddleLeft;
			this.DetailData1.BorderColor = Color.Transparent;
			this.DetailData1.Borders = BorderSide.Left;
			this.DetailData1.BorderWidth = 2f;
			this.DetailData1.Font = new Font("Arial", 8.25f);
			this.DetailData1.ForeColor = Color.Black;
			this.DetailData1.Name = "DetailData1";
			this.DetailData1.Padding = new PaddingInfo(6, 6, 0, 0, 100f);
			this.DetailData1.TextAlignment = TextAlignment.MiddleLeft;
			this.DetailData3_Odd.BackColor = Color.FromArgb(243, 245, 248);
			this.DetailData3_Odd.BorderColor = Color.Transparent;
			this.DetailData3_Odd.Borders = BorderSide.None;
			this.DetailData3_Odd.BorderWidth = 1f;
			this.DetailData3_Odd.Font = new Font("Arial", 8.25f);
			this.DetailData3_Odd.ForeColor = Color.Black;
			this.DetailData3_Odd.Name = "DetailData3_Odd";
			this.DetailData3_Odd.Padding = new PaddingInfo(6, 6, 0, 0, 100f);
			this.DetailData3_Odd.TextAlignment = TextAlignment.MiddleLeft;
			this.PageInfo.Font = new Font("Arial", 8.25f, FontStyle.Bold);
			this.PageInfo.ForeColor = Color.FromArgb(64, 70, 80);
			this.PageInfo.Name = "PageInfo";
			this.PageInfo.Padding = new PaddingInfo(6, 6, 0, 0, 100f);
			this.Cat.Description = "Cat";
			this.Cat.Name = "Cat";
			this.objectDataSource2.DataSource = typeof(SalesProductCategoriesSubModel);
			this.objectDataSource2.Name = "objectDataSource2";
			this.tableCell11.BackColor = Color.LightBlue;
			this.tableCell11.BorderDashStyle = BorderDashStyle.Solid;
			this.tableCell11.Borders = BorderSide.Left;
			this.tableCell11.BorderWidth = 2f;
			this.tableCell11.Font = new Font("Arial", 9.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.tableCell11.ForeColor = Color.Black;
			this.tableCell11.Multiline = true;
			this.tableCell11.Name = "tableCell11";
			this.tableCell11.StyleName = "DetailCaption1";
			this.tableCell11.StylePriority.UseBackColor = false;
			this.tableCell11.StylePriority.UseBorderDashStyle = false;
			this.tableCell11.StylePriority.UseBorders = false;
			this.tableCell11.StylePriority.UseBorderWidth = false;
			this.tableCell11.StylePriority.UseFont = false;
			this.tableCell11.StylePriority.UseForeColor = false;
			this.tableCell11.StylePriority.UseTextAlignment = false;
			this.tableCell11.Text = "نسبة الربح";
			this.tableCell11.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell11.Weight = 0.06596087195926022;
			base.Bands.AddRange(new Band[]
			{
				this.TopMargin,
				this.BottomMargin,
				this.GroupHeader1,
				this.Detail
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource2
			});
			base.DataSource = this.objectDataSource2;
			this.FilterString = "[Category] = ?Cat";
			this.Font = new Font("Arial", 9.75f);
			base.Landscape = true;
			base.Margins = new Margins(20, 36, 0, 4);
			base.PageHeight = 850;
			base.PageWidth = 1100;
			base.Parameters.AddRange(new DevExpress.XtraReports.Parameters.Parameter[]
			{
				this.Cat
			});
			this.RightToLeft = RightToLeft.Yes;
			base.RightToLeftLayout = RightToLeftLayout.Yes;
			base.StyleSheet.AddRange(new XRControlStyle[]
			{
				this.Title,
				this.DetailCaption1,
				this.DetailData1,
				this.DetailData3_Odd,
				this.PageInfo
			});
			base.Version = "20.2";
			this.BeforePrint += this.SalesProductCategoriesSubReport_BeforePrint;
			((ISupportInitialize)this.table1).EndInit();
			((ISupportInitialize)this.table2).EndInit();
			((ISupportInitialize)this.objectDataSource2).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x04001C88 RID: 7304
		private string name;

		// Token: 0x04001C89 RID: 7305
		private IContainer components = null;

		// Token: 0x04001C8A RID: 7306
		private TopMarginBand TopMargin;

		// Token: 0x04001C8B RID: 7307
		private BottomMarginBand BottomMargin;

		// Token: 0x04001C8C RID: 7308
		private GroupHeaderBand GroupHeader1;

		// Token: 0x04001C8D RID: 7309
		private XRTable table1;

		// Token: 0x04001C8E RID: 7310
		private XRTableRow tableRow1;

		// Token: 0x04001C8F RID: 7311
		private XRTableCell tableCell1;

		// Token: 0x04001C90 RID: 7312
		private XRTableCell tableCell2;

		// Token: 0x04001C91 RID: 7313
		private XRTableCell tableCell3;

		// Token: 0x04001C92 RID: 7314
		private XRTableCell tableCell4;

		// Token: 0x04001C93 RID: 7315
		private XRTableCell tableCell5;

		// Token: 0x04001C94 RID: 7316
		private XRTableCell tableCell6;

		// Token: 0x04001C95 RID: 7317
		private XRTableCell tableCell7;

		// Token: 0x04001C96 RID: 7318
		private XRTableCell tableCell8;

		// Token: 0x04001C97 RID: 7319
		private XRTableCell tableCell9;

		// Token: 0x04001C98 RID: 7320
		private XRTableCell tableCell10;

		// Token: 0x04001C99 RID: 7321
		private DetailBand Detail;

		// Token: 0x04001C9A RID: 7322
		private XRTable table2;

		// Token: 0x04001C9B RID: 7323
		private XRTableRow tableRow2;

		// Token: 0x04001C9C RID: 7324
		private XRTableCell tableCell12;

		// Token: 0x04001C9D RID: 7325
		private XRTableCell tableCell13;

		// Token: 0x04001C9E RID: 7326
		private XRTableCell tableCell14;

		// Token: 0x04001C9F RID: 7327
		private XRTableCell tableCell15;

		// Token: 0x04001CA0 RID: 7328
		private XRTableCell tableCell16;

		// Token: 0x04001CA1 RID: 7329
		private XRTableCell tableCell17;

		// Token: 0x04001CA2 RID: 7330
		private XRTableCell tableCell18;

		// Token: 0x04001CA3 RID: 7331
		private XRTableCell tableCell19;

		// Token: 0x04001CA4 RID: 7332
		private XRTableCell tableCell20;

		// Token: 0x04001CA5 RID: 7333
		private XRTableCell tableCell21;

		// Token: 0x04001CA6 RID: 7334
		private XRTableCell tableCell22;

		// Token: 0x04001CA7 RID: 7335
		private XRControlStyle Title;

		// Token: 0x04001CA8 RID: 7336
		private XRControlStyle DetailCaption1;

		// Token: 0x04001CA9 RID: 7337
		private XRControlStyle DetailData1;

		// Token: 0x04001CAA RID: 7338
		private XRControlStyle DetailData3_Odd;

		// Token: 0x04001CAB RID: 7339
		private XRControlStyle PageInfo;

		// Token: 0x04001CAC RID: 7340
		private DevExpress.XtraReports.Parameters.Parameter Cat;

		// Token: 0x04001CAD RID: 7341
		private ObjectDataSource objectDataSource2;

		// Token: 0x04001CAE RID: 7342
		private XRTableCell tableCell11;
	}
}
