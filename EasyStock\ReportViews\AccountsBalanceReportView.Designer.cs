﻿namespace EasyStock.ReportViews
{
	// Token: 0x0200030C RID: 780
	public partial class AccountsBalanceReportView : global::EasyStock.MainViews.XtraReportForm
	{
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}
		private void InitializeComponent()
		{
			base.SuspendLayout();
			this.documentViewer1.Location = new global::System.Drawing.Point(0, 49);
			this.documentViewer1.Size = new global::System.Drawing.Size(800, 379);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			base.Name = "AccountsBalanceReportView";
			this.Text = "Réconcilier les comptes";
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		private global::System.ComponentModel.IContainer components = null;
	}
}
