﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class BarcodeModel : CompanyInfoReportModel
    {
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Display(Name = "Code Article")]
        public string Code { get; set; }

        [Display(Name = "Nom", GroupName = "Données")]
        public string Name { get; set; }

        [Display(Name = "Description", GroupName = "Données")]
        public string Descreption { get; set; }

        [Display(Name = "Catégorie", GroupName = "Données")]
        public string Category { get; set; }

        [Display(Name = "Entreprise", GroupName = "Données")]
        public string Company { get; set; }

        [Display(Name = "Fournisseur", GroupName = "Données")]
        public string ProductVendor { get; set; }

        [Display(Name = "Lieu du produit")]
        public string Location { get; set; }

        [Display(Name = "Nom de l'unité")]
        public string UnitName { get; set; }

        [Display(Name = "Prix de vente")]
        public double SellPrice { get; set; }

        [Display(Name = "Code-barres")]
        public string Barcode { get; set; }

        [Display(Name = "Code-barres de l'unité")]
        public string UnitBarcode { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Couleur")]
        public string Color { get; set; }

        [Display(Name = "Taille")]
        public string Size { get; set; }
    }
}