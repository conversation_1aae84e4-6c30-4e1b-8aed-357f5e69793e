﻿using EasyStock.Controller;
using EasyStock.Controls;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels.IncomStatment;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class IncomeStatmentReportView : XtraReportForm
    {
        public IncomeStatmentReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.CostCenter
        })
        {
            this.InitializeComponent();
            base.FiltersForm.FromDate.EditValue = new DateTime(DateTime.Now.Year, 1, 1);
            base.FiltersForm.ToDate.EditValue = new DateTime(DateTime.Now.Year, 12, 31);
            base.FiltersForm.CostCenter.MultiSelect = false;
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                IncomeStatmentModel model = IncomeStatmentReportView.GetIncomeStatment(base.FiltersForm.startDate, base.FiltersForm.endtDate, this.CostCenterID);
                IncomeStatmentReport rpt = new IncomeStatmentReport(model);
                rpt.SetCompanyInfo(model);
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private int? CostCenterID
        {
            get
            {
                GridPopupContainerControl costCenter = base.FiltersForm.CostCenter;
                int? result;
                if (costCenter == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = costCenter.EditValue;
                    if (editValue == null)
                    {
                        result = null;
                    }
                    else
                    {
                        List<int> list = editValue.ToList<int>();
                        result = ((list != null) ? new int?(list.FirstOrDefault<int>()) : null);
                    }
                }
                return result;
            }
        }

        public static IncomeStatmentModel GetIncomeStatment(DateTime startDate, DateTime EndDate, int? costcenterID = null)
        {
            startDate = startDate.Date;
            EndDate = EndDate.Date;
            IncomeStatmentModel model = new IncomeStatmentModel();
            using (ERPDataContext db = new ERPDataContext())
            {
                IQueryable<JournalDetail> filterdJournal = from j in db.JournalDetails.Include((JournalDetail x) => x.Journal).Include((JournalDetail x) => x.CostCenter)
                                                           where DbFunctions.TruncateTime(j.Journal.Date) >= startDate && DbFunctions.TruncateTime(j.Journal.Date) <= EndDate
                                                           select j;
                string costcenterCode = db.CostCenters.SingleOrDefault((CostCenter x) => (int?)x.ID == costcenterID)?.Code;
                if (costcenterID.HasValue)
                {
                    filterdJournal = filterdJournal.Where((JournalDetail x) => x.CostCenter.Code.IndexOf(costcenterCode) == 0);
                }
                Account salesAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.SalesAccount);
                IQueryable<JournalDetail> salesQ = from j in filterdJournal
                                                   join ac in db.Accounts on j.AccountID equals ac.ID
                                                   where ac.Number.IndexOf(salesAccount.Number) >= 0
                                                   select j;
                model.SalesIncome = new IncomeStatmentModelDetial
                {
                    Account = "Ventes",
                    Credit = salesQ.Sum(x => (double?)x.Credit).GetValueOrDefault(),
                    Debit = salesQ.Sum(x => (double?)x.Debit).GetValueOrDefault()
                };
                Account salesReturnAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.SalesReturnAccount);
                IQueryable<JournalDetail> salesReturnQ = from j in filterdJournal
                                                         join ac in db.Accounts on j.AccountID equals ac.ID
                                                         where ac.Number.IndexOf(salesReturnAccount.Number) >= 0
                                                         select j;
                model.SalesReturn = new IncomeStatmentModelDetial
                {
                    Account = "Retour sur ventes",
                    Credit = salesReturnQ.Sum(x => (double?)x.Credit).GetValueOrDefault(),
                    Debit = salesReturnQ.Sum(x => (double?)x.Debit).GetValueOrDefault()
                };
                Account salesDiscountAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.SalesDiscountAccount);
                IQueryable<JournalDetail> salesDiscountQ = from j in filterdJournal
                                                           join ac in db.Accounts on j.AccountID equals ac.ID
                                                           where ac.Number.IndexOf(salesDiscountAccount.Number) >= 0
                                                           select j;
                model.SalesDiscount = new IncomeStatmentModelDetial
                {
                    Account = "Remise autorisée",
                    Credit = salesDiscountQ.Sum(x => (double?)x.Credit).GetValueOrDefault(),
                    Debit = salesDiscountQ.Sum(x => (double?)x.Debit).GetValueOrDefault()
                };
                IQueryable<ProductTransaction> products = from x in db.ProductTransactions
                                                          where (int)x.TransactionState == 2
                                                          select x into jd
                                                          join product in db.Products on jd.ProductID equals product.ID
                                                          where (int)product.Type != 1
                                                          select jd;
                IQueryable<ProductTransaction> beforePeriodTransQ = products.Where((ProductTransaction x) => DbFunctions.TruncateTime(x.Date) <= startDate);
                double OpeningStock = (from x in beforePeriodTransQ
                                       where (int)x.TransactionType == 0
                                       select (double?)x.CostValue * (double?)x.Quantity).Sum().GetValueOrDefault() - (from x in beforePeriodTransQ
                                                                                                                       where (int)x.TransactionType == 1
                                                                                                                       select (double?)x.CostValue * (double?)x.Quantity).Sum().GetValueOrDefault();
                model.OpeningStock = new IncomeStatmentModelDetial
                {
                    Account = "Stock initial",
                    Credit = ((OpeningStock < 0.0) ? Math.Abs(OpeningStock) : 0.0),
                    Debit = ((OpeningStock >= 0.0) ? OpeningStock : 0.0)
                };
                IQueryable<ProductTransaction> afterPeriodTransQ = products.Where((ProductTransaction x) => DbFunctions.TruncateTime(x.Date) <= EndDate);
                double ClosingStock = (from x in afterPeriodTransQ
                                       where (int)x.TransactionType == 0
                                       select (double?)x.CostValue * (double?)x.Quantity).Sum().GetValueOrDefault() - (from x in afterPeriodTransQ
                                                                                                                       where (int)x.TransactionType == 1
                                                                                                                       select (double?)x.CostValue * (double?)x.Quantity).Sum().GetValueOrDefault();
                model.ClosingStock = new IncomeStatmentModelDetial
                {
                    Account = "Stock final",
                    Credit = ((ClosingStock >= 0.0) ? ClosingStock : 0.0),
                    Debit = ((ClosingStock < 0.0) ? Math.Abs(ClosingStock) : 0.0)
                };
                Account purchaseAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.PurchasesAccount);
                IQueryable<JournalDetail> purchaseQ = from j in filterdJournal
                                                      join ac in db.Accounts on j.AccountID equals ac.ID
                                                      where ac.Number.IndexOf(purchaseAccount.Number) >= 0
                                                      select j;
                model.Purchases = new IncomeStatmentModelDetial
                {
                    Account = "Achats",
                    Credit = purchaseQ.Sum(x => (double?)x.Credit).GetValueOrDefault(),
                    Debit = purchaseQ.Sum(x => (double?)x.Debit).GetValueOrDefault()
                };
                Account purchaseReturnAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.PurchasesReturnAccount);
                IQueryable<JournalDetail> purchaseReturnQ = from j in filterdJournal
                                                            join ac in db.Accounts on j.AccountID equals ac.ID
                                                            where ac.Number.IndexOf(purchaseReturnAccount.Number) >= 0
                                                            select j;
                model.PurchasesReturn = new IncomeStatmentModelDetial
                {
                    Account = "Retour sur achats",
                    Credit = purchaseReturnQ.Sum(x => (double?)x.Credit).GetValueOrDefault(),
                    Debit = purchaseReturnQ.Sum(x => (double?)x.Debit).GetValueOrDefault()
                };
                Account purchaseDiscountAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.PurchaseDiscountAccount);
                IQueryable<JournalDetail> purchaseDiscountQ = from j in filterdJournal
                                                              join ac in db.Accounts on j.AccountID equals ac.ID
                                                              where ac.Number.IndexOf(purchaseDiscountAccount.Number) >= 0
                                                              select j;
                model.PurchasesDiscount = new IncomeStatmentModelDetial
                {
                    Account = "Remise obtenue",
                    Credit = purchaseDiscountQ.Sum(x => (double?)x.Credit).GetValueOrDefault(),
                    Debit = purchaseDiscountQ.Sum(x => (double?)x.Debit).GetValueOrDefault()
                };
                Account ExpencesAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.ExpensesAccount);
                List<IncomeStatmentModelDetial> ExpencesQ = (from j in filterdJournal.Include((JournalDetail x) => x.Account)
                                                             where j.Account.Number.IndexOf(ExpencesAccount.Number) >= 0
                                                             group j by j.AccountID into g
                                                             join ac in db.Accounts on g.Key equals ac.ID
                                                             select new IncomeStatmentModelDetial
                                                             {
                                                                 Account = ac.Name,
                                                                 Credit = (double?)g.Sum(x => x.Credit) ?? 0.0,
                                                                 Debit = (double?)g.Sum(x => x.Debit) ?? 0.0
                                                             }).ToList();
                model.Expences = ExpencesQ;
                Account RevenueAccount = db.Accounts.SingleOrDefault((Account x) => x.ID == ERPDataContext.SystemSettings.RevenueAccount);
                List<IncomeStatmentModelDetial> RevenueQ = (from j in filterdJournal.Include((JournalDetail x) => x.Account)
                                                            where j.Account.Number.IndexOf(RevenueAccount.Number) >= 0
                                                            group j by j.AccountID into g
                                                            join ac in db.Accounts on g.Key equals ac.ID
                                                            select new IncomeStatmentModelDetial
                                                            {
                                                                Account = ac.Name,
                                                                Credit = (double?)g.Sum(x => x.Credit) ?? 0.0,
                                                                Debit = (double?)g.Sum(x => x.Debit) ?? 0.0
                                                            }).ToList();
                model.Revnue = RevenueQ;
            }
            return model;
        }

    }
}
