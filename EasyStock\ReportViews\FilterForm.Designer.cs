﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000360 RID: 864
	public partial class FilterForm : global::DevExpress.XtraEditors.XtraForm
	{
		// Token: 0x060014BF RID: 5311 RVA: 0x00175780 File Offset: 0x00173980
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060014C0 RID: 5312 RVA: 0x001757B8 File Offset: 0x001739B8
		private void InitializeComponent()
		{
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.ToDate = new DevExpress.XtraEditors.DateEdit();
            this.FromDate = new DevExpress.XtraEditors.DateEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.ItemForFromDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForToDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.ReportName = new DevExpress.XtraLayout.SimpleLabelItem();
            this.simpleSeparator1 = new DevExpress.XtraLayout.SimpleSeparator();
            this.simpleSeparator2 = new DevExpress.XtraLayout.SimpleSeparator();
            this.layoutControlItem7 = new DevExpress.XtraLayout.LayoutControlItem();
            this.Stores = new EasyStock.Controls.GridPopupContainerControl();
            this.Accounts = new EasyStock.Controls.GridPopupContainerControl();
            this.Products = new EasyStock.Controls.GridPopupContainerControl();
            this.Vendors = new EasyStock.Controls.GridPopupContainerControl();
            this.Customers = new EasyStock.Controls.GridPopupContainerControl();
            this.CostCenter = new EasyStock.Controls.GridPopupContainerControl();
            this.ItemForCustomers = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForVendors = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForProducts = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAccounts = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForStore = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCostCenter = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ToDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFromDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForToDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ReportName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Stores.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Accounts.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Products.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Vendors.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Customers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CostCenter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCustomers)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForVendors)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForProducts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccounts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForStore)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCostCenter)).BeginInit();
            this.SuspendLayout();
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.Stores);
            this.layoutControl1.Controls.Add(this.simpleButton1);
            this.layoutControl1.Controls.Add(this.ToDate);
            this.layoutControl1.Controls.Add(this.FromDate);
            this.layoutControl1.Controls.Add(this.Accounts);
            this.layoutControl1.Controls.Add(this.Products);
            this.layoutControl1.Controls.Add(this.Vendors);
            this.layoutControl1.Controls.Add(this.Customers);
            this.layoutControl1.Controls.Add(this.CostCenter);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.Root = this.Root;
            this.layoutControl1.Size = new System.Drawing.Size(484, 291);
            this.layoutControl1.TabIndex = 0;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // simpleButton1
            // 
            this.simpleButton1.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.actions_zoom;
            this.simpleButton1.ImageOptions.SvgImageSize = new System.Drawing.Size(24, 24);
            this.simpleButton1.Location = new System.Drawing.Point(352, 246);
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Size = new System.Drawing.Size(120, 33);
            this.simpleButton1.StyleController = this.layoutControl1;
            this.simpleButton1.TabIndex = 9;
            this.simpleButton1.Text = "Vue";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // ToDate
            // 
            this.ToDate.EditValue = null;
            this.ToDate.Location = new System.Drawing.Point(100, 184);
            this.ToDate.Name = "ToDate";
            this.ToDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ToDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ToDate.Size = new System.Drawing.Size(372, 20);
            this.ToDate.StyleController = this.layoutControl1;
            this.ToDate.TabIndex = 7;
            // 
            // FromDate
            // 
            this.FromDate.EditValue = null;
            this.FromDate.Location = new System.Drawing.Point(100, 160);
            this.FromDate.Name = "FromDate";
            this.FromDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FromDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.FromDate.Size = new System.Drawing.Size(372, 20);
            this.FromDate.StyleController = this.layoutControl1;
            this.FromDate.TabIndex = 6;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem1,
            this.ItemForCustomers,
            this.ItemForVendors,
            this.ItemForProducts,
            this.ItemForAccounts,
            this.ItemForFromDate,
            this.ItemForToDate,
            this.emptySpaceItem2,
            this.ReportName,
            this.simpleSeparator1,
            this.simpleSeparator2,
            this.ItemForStore,
            this.ItemForCostCenter,
            this.layoutControlItem7});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(484, 291);
            this.Root.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 220);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(464, 13);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // ItemForFromDate
            // 
            this.ItemForFromDate.Control = this.FromDate;
            this.ItemForFromDate.Location = new System.Drawing.Point(0, 148);
            this.ItemForFromDate.Name = "ItemForFromDate";
            this.ItemForFromDate.Size = new System.Drawing.Size(464, 24);
            this.ItemForFromDate.Text = "À partir de la date";
            this.ItemForFromDate.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForFromDate.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForFromDate.TextToControlDistance = 5;
            // 
            // ItemForToDate
            // 
            this.ItemForToDate.Control = this.ToDate;
            this.ItemForToDate.Location = new System.Drawing.Point(0, 172);
            this.ItemForToDate.Name = "ItemForToDate";
            this.ItemForToDate.Size = new System.Drawing.Size(464, 24);
            this.ItemForToDate.Text = "Jusqu\'à la date";
            this.ItemForToDate.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForToDate.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForToDate.TextToControlDistance = 5;
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 234);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(340, 37);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // ReportName
            // 
            this.ReportName.AllowHotTrack = false;
            this.ReportName.AppearanceItemCaption.Font = new System.Drawing.Font("Tahoma", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ReportName.AppearanceItemCaption.Options.UseFont = true;
            this.ReportName.Location = new System.Drawing.Point(0, 0);
            this.ReportName.Name = "ReportName";
            this.ReportName.Size = new System.Drawing.Size(464, 27);
            this.ReportName.TextSize = new System.Drawing.Size(171, 23);
            // 
            // simpleSeparator1
            // 
            this.simpleSeparator1.AllowHotTrack = false;
            this.simpleSeparator1.Location = new System.Drawing.Point(0, 233);
            this.simpleSeparator1.Name = "simpleSeparator1";
            this.simpleSeparator1.Size = new System.Drawing.Size(464, 1);
            // 
            // simpleSeparator2
            // 
            this.simpleSeparator2.AllowHotTrack = false;
            this.simpleSeparator2.Location = new System.Drawing.Point(0, 27);
            this.simpleSeparator2.Name = "simpleSeparator2";
            this.simpleSeparator2.Size = new System.Drawing.Size(464, 1);
            // 
            // layoutControlItem7
            // 
            this.layoutControlItem7.Control = this.simpleButton1;
            this.layoutControlItem7.Location = new System.Drawing.Point(340, 234);
            this.layoutControlItem7.MaxSize = new System.Drawing.Size(124, 37);
            this.layoutControlItem7.MinSize = new System.Drawing.Size(124, 37);
            this.layoutControlItem7.Name = "layoutControlItem7";
            this.layoutControlItem7.Size = new System.Drawing.Size(124, 37);
            this.layoutControlItem7.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.layoutControlItem7.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem7.TextVisible = false;
            // 
            // Stores
            // 
            this.Stores.DataSource = null;
            this.Stores.DisplayMember = "Name";
            this.Stores.EditValue = null;
            this.Stores.Location = new System.Drawing.Point(100, 136);
            this.Stores.MultiSelect = true;
            this.Stores.Name = "Stores";
            this.Stores.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.DropDown)});
            this.Stores.Size = new System.Drawing.Size(372, 20);
            this.Stores.StyleController = this.layoutControl1;
            this.Stores.TabIndex = 5;
            this.Stores.ValueMember = "ID";
            // 
            // Accounts
            // 
            this.Accounts.DataSource = null;
            this.Accounts.DisplayMember = "Name";
            this.Accounts.EditValue = null;
            this.Accounts.Location = new System.Drawing.Point(100, 88);
            this.Accounts.MultiSelect = true;
            this.Accounts.Name = "Accounts";
            this.Accounts.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.Accounts.Size = new System.Drawing.Size(372, 20);
            this.Accounts.StyleController = this.layoutControl1;
            this.Accounts.TabIndex = 3;
            this.Accounts.ValueMember = "ID";
            // 
            // Products
            // 
            this.Products.DataSource = null;
            this.Products.DisplayMember = "Name";
            this.Products.EditValue = null;
            this.Products.Location = new System.Drawing.Point(100, 112);
            this.Products.MultiSelect = true;
            this.Products.Name = "Products";
            this.Products.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.Products.Size = new System.Drawing.Size(372, 20);
            this.Products.StyleController = this.layoutControl1;
            this.Products.TabIndex = 4;
            this.Products.ValueMember = "ID";
            // 
            // Vendors
            // 
            this.Vendors.DataSource = null;
            this.Vendors.DisplayMember = "Name";
            this.Vendors.EditValue = null;
            this.Vendors.Location = new System.Drawing.Point(100, 64);
            this.Vendors.MultiSelect = true;
            this.Vendors.Name = "Vendors";
            this.Vendors.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.Vendors.Size = new System.Drawing.Size(372, 20);
            this.Vendors.StyleController = this.layoutControl1;
            this.Vendors.TabIndex = 2;
            this.Vendors.ValueMember = "ID";
            // 
            // Customers
            // 
            this.Customers.DataSource = null;
            this.Customers.DisplayMember = "Name";
            this.Customers.EditValue = null;
            this.Customers.Location = new System.Drawing.Point(100, 40);
            this.Customers.MultiSelect = true;
            this.Customers.Name = "Customers";
            this.Customers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.Customers.Properties.NullText = "Tout";
            this.Customers.Size = new System.Drawing.Size(372, 20);
            this.Customers.StyleController = this.layoutControl1;
            this.Customers.TabIndex = 0;
            this.Customers.ValueMember = "ID";
            // 
            // CostCenter
            // 
            this.CostCenter.DataSource = null;
            this.CostCenter.DisplayMember = "Name";
            this.CostCenter.EditValue = null;
            this.CostCenter.Location = new System.Drawing.Point(100, 208);
            this.CostCenter.MultiSelect = true;
            this.CostCenter.Name = "CostCenter";
            this.CostCenter.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.CostCenter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.CostCenter.Size = new System.Drawing.Size(372, 20);
            this.CostCenter.StyleController = this.layoutControl1;
            this.CostCenter.TabIndex = 8;
            this.CostCenter.ValueMember = "ID";
            // 
            // ItemForCustomers
            // 
            this.ItemForCustomers.Control = this.Customers;
            this.ItemForCustomers.Location = new System.Drawing.Point(0, 28);
            this.ItemForCustomers.Name = "ItemForCustomers";
            this.ItemForCustomers.Size = new System.Drawing.Size(464, 24);
            this.ItemForCustomers.Text = "Clients";
            this.ItemForCustomers.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForCustomers.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForCustomers.TextToControlDistance = 5;
            // 
            // ItemForVendors
            // 
            this.ItemForVendors.Control = this.Vendors;
            this.ItemForVendors.Location = new System.Drawing.Point(0, 52);
            this.ItemForVendors.Name = "ItemForVendors";
            this.ItemForVendors.Size = new System.Drawing.Size(464, 24);
            this.ItemForVendors.Text = "Fournisseurs";
            this.ItemForVendors.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForVendors.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForVendors.TextToControlDistance = 5;
            // 
            // ItemForProducts
            // 
            this.ItemForProducts.Control = this.Products;
            this.ItemForProducts.Location = new System.Drawing.Point(0, 100);
            this.ItemForProducts.Name = "ItemForProducts";
            this.ItemForProducts.Size = new System.Drawing.Size(464, 24);
            this.ItemForProducts.Text = "Articles";
            this.ItemForProducts.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForProducts.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForProducts.TextToControlDistance = 5;
            // 
            // ItemForAccounts
            // 
            this.ItemForAccounts.Control = this.Accounts;
            this.ItemForAccounts.Location = new System.Drawing.Point(0, 76);
            this.ItemForAccounts.Name = "ItemForAccounts";
            this.ItemForAccounts.Size = new System.Drawing.Size(464, 24);
            this.ItemForAccounts.Text = "Accounts";
            this.ItemForAccounts.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForAccounts.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForAccounts.TextToControlDistance = 5;
            // 
            // ItemForStore
            // 
            this.ItemForStore.Control = this.Stores;
            this.ItemForStore.Location = new System.Drawing.Point(0, 124);
            this.ItemForStore.Name = "ItemForStore";
            this.ItemForStore.Size = new System.Drawing.Size(464, 24);
            this.ItemForStore.Text = "Inventory";
            this.ItemForStore.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForStore.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForStore.TextToControlDistance = 5;
            // 
            // ItemForCostCenter
            // 
            this.ItemForCostCenter.Control = this.CostCenter;
            this.ItemForCostCenter.ControlAlignment = System.Drawing.ContentAlignment.TopRight;
            this.ItemForCostCenter.CustomizationFormText = "Centre de coûts";
            this.ItemForCostCenter.Location = new System.Drawing.Point(0, 196);
            this.ItemForCostCenter.Name = "ItemForCostCenterID";
            this.ItemForCostCenter.Size = new System.Drawing.Size(464, 24);
            this.ItemForCostCenter.Text = "Centre de coût";
            this.ItemForCostCenter.TextAlignMode = DevExpress.XtraLayout.TextAlignModeItem.CustomSize;
            this.ItemForCostCenter.TextSize = new System.Drawing.Size(83, 13);
            this.ItemForCostCenter.TextToControlDistance = 5;
            // 
            // FilterForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(484, 291);
            this.Controls.Add(this.layoutControl1);
            this.FormBorderEffect = DevExpress.XtraEditors.FormBorderEffect.Shadow;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FilterForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.SurfaceMaterial = DevExpress.XtraEditors.SurfaceMaterial.Acrylic;
            this.Text = "Filtres du rapport";
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ToDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ToDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.FromDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForFromDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForToDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ReportName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.simpleSeparator2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Stores.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Accounts.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Products.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Vendors.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Customers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CostCenter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCustomers)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForVendors)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForProducts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAccounts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForStore)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCostCenter)).EndInit();
            this.ResumeLayout(false);

		}

		// Token: 0x04001A11 RID: 6673
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04001A12 RID: 6674
		private global::DevExpress.XtraLayout.LayoutControl layoutControl1;

		// Token: 0x04001A13 RID: 6675
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x04001A14 RID: 6676
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04001A15 RID: 6677
		private global::DevExpress.XtraEditors.SimpleButton simpleButton1;

		// Token: 0x04001A16 RID: 6678
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem7;

		// Token: 0x04001A17 RID: 6679
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x04001A18 RID: 6680
		private global::DevExpress.XtraLayout.SimpleSeparator simpleSeparator1;

		// Token: 0x04001A19 RID: 6681
		private global::DevExpress.XtraLayout.SimpleSeparator simpleSeparator2;

		// Token: 0x04001A1A RID: 6682
		public global::EasyStock.Controls.GridPopupContainerControl Products;

		// Token: 0x04001A1B RID: 6683
		public global::EasyStock.Controls.GridPopupContainerControl Vendors;

		// Token: 0x04001A1C RID: 6684
		public global::EasyStock.Controls.GridPopupContainerControl Customers;

		// Token: 0x04001A1D RID: 6685
		public global::EasyStock.Controls.GridPopupContainerControl Accounts;

		// Token: 0x04001A1E RID: 6686
		public global::DevExpress.XtraEditors.DateEdit ToDate;

		// Token: 0x04001A1F RID: 6687
		public global::DevExpress.XtraEditors.DateEdit FromDate;

		// Token: 0x04001A20 RID: 6688
		public global::DevExpress.XtraLayout.SimpleLabelItem ReportName;

		// Token: 0x04001A21 RID: 6689
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForCustomers;

		// Token: 0x04001A22 RID: 6690
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForVendors;

		// Token: 0x04001A23 RID: 6691
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForProducts;

		// Token: 0x04001A24 RID: 6692
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForAccounts;

		// Token: 0x04001A25 RID: 6693
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForFromDate;

		// Token: 0x04001A26 RID: 6694
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForToDate;

		// Token: 0x04001A27 RID: 6695
		public global::EasyStock.Controls.GridPopupContainerControl Stores;

		// Token: 0x04001A28 RID: 6696
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForStore;

		// Token: 0x04001A29 RID: 6697
		public global::EasyStock.Controls.GridPopupContainerControl CostCenter;

		// Token: 0x04001A2A RID: 6698
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCostCenter;
	}
}
