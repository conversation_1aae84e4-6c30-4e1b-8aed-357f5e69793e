﻿using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace EasyStock.ReportModels
{
    public class ProductInOutModel : CompanyInfoReportModel
    {
        [Display(Name = "Code Article")]
        public int ProductID { get; set; }
        [Display(Name = "Nom de l'article")]
        public string ProductName { get; set; }
        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Code Dépôt")]
        public int StoreID { get; set; }

        public ICollection<InOutModel> InOut { get; set; }

        public static ProductInOutModel GetProductInOut(int productID, DateTime fromDate, DateTime toDate)
        {
            using ERPDataContext db = new ERPDataContext();
            IQueryable<ProductTransaction> q = (from x in db.ProductTransactions
                                                where (int)x.TransactionState == 2
                                                where x.ProductID == productID && x.Date >= fromDate
                                                select x).AsQueryable();
            IQueryable<InOutModel> productTransactions = from x in q
                                                         where x.Date <= toDate
                                                         select x into pt
                                                         select new InOutModel
                                                         {
                                                             Date = pt.Date,
                                                             In = (((int)pt.TransactionType == 0) ? (pt.Quantity * pt.Factor) : 0.0),
                                                             Out = (((int)pt.TransactionType == 1) ? (pt.Quantity * pt.Factor) : 0.0),
                                                             ProductTransaction = q.Where((ProductTransaction x) => x.Date <= pt.Date).ToList()
                                                         };
            ProductInOutModel productInOutModel = new ProductInOutModel();
            Product product = db.Products.Find(productID);
            productInOutModel.ProductName = product.Name;
            productInOutModel.ProductID = product.ID;
            productInOutModel.InOut = productTransactions.ToList();
            return productInOutModel;
        }
    }
}
