﻿using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{

    public partial class TotalProductTransactionsReportView : XtraReportForm
    {

        public TotalProductTransactionsReportView()
            : base(new ReportFilter[3]
            {
            ReportFilter.Product,
            ReportFilter.Store,
            ReportFilter.Date
            })
        {
            InitializeComponent();
        }

        internal override bool ValidateFilters()
        {
            if (!base.FiltersForm.hasStartDate)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                return false;
            }
            if (!base.FiltersForm.hasEndtDate)
            {
                base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                return false;
            }
            return true;
        }

        public override void RefreshDataSource()
        {
            if (ValidateFilters())
            {
                TotalProductTransactionsReport rpt = new TotalProductTransactionsReport();
                ICollection<TotalProductTransactionsReportModel> dataSource = (ICollection<TotalProductTransactionsReportModel>)(rpt.DataSource = GetData());
                rpt.SetCompanyInfo(dataSource.FirstOrDefault());
                rpt.Cell_ReportName.Text = Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<TotalProductTransactionsReportModel> GetData()
        {
            int[] ProductIDs = base.FiltersForm.Products.EditValue?.ToArray();
            int[] StoreIDs = base.FiltersForm.Stores.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<ProductTransaction> filterdtrans = from x in context.ProductTransactions
                                                              where (int)x.TransactionState == 2
                                                              select x into trans
                                                              where DbFunctions.TruncateTime(trans.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(trans.Date) <= FiltersForm.endtDate
                                                              select trans;
                if (ProductIDs != null && ProductIDs.Count() > 0)
                {
                    filterdtrans = filterdtrans.Where(c => ProductIDs.Contains(c.ProductID));
                }
                if (StoreIDs != null && StoreIDs.Count() > 0)
                {
                    filterdtrans = filterdtrans.Where(c => StoreIDs.Contains(c.StoreID));
                }
                IQueryable<TotalProductTransactionsReportModel> Data = from t in filterdtrans
                                                                       group t by t.ProductID into g
                                                                       join pr in context.Products on g.Key equals pr.ID
                                                                       from cat in context.ProductCategories.Where(pcat => pcat.ID == pr.CategoryID).DefaultIfEmpty()
                                                                       select new TotalProductTransactionsReportModel
                                                                       {
                                                                           Product = pr.Name,
                                                                           Category = cat.Name,
                                                                           SalesQty = g.Where(trans => (int)trans.Type == 4).Sum(x => (double?)(x.Quantity * x.Factor) ?? 0.0),
                                                                           SalesReturnQty = g.Where(trans => (int)trans.Type == 5).Sum(x => (double?)(x.Quantity * x.Factor) ?? 0.0),
                                                                           PurchseQty = g.Where(trans => (int)trans.Type == 2).Sum(x => (double?)(x.Quantity * x.Factor) ?? 0.0),
                                                                           PurchaseRetrnQty = g.Where(trans => (int)trans.Type == 3).Sum(x => (double?)(x.Quantity * x.Factor) ?? 0.0),
                                                                           OpeningQty = (from x in context.ProductTransactions
                                                                                          where (int)x.TransactionState == 2
                                                                                          select x into p
                                                                                          where DbFunctions.TruncateTime(p.Date) < FiltersForm.startDate
                                                                                          where p.ProductID == g.Key
                                                                                          select p into trans
                                                                                          where (int)trans.TransactionType == 0
                                                                                          select trans).Sum(x => (double?)(x.Quantity * x.Factor) ?? 0.0) - (from x in context.ProductTransactions
                                                                                                                                                               where (int)x.TransactionState == 2
                                                                                                                                                               select x into p
                                                                                                                                                               where DbFunctions.TruncateTime(p.Date) < FiltersForm.startDate
                                                                                                                                                               where p.ProductID == g.Key
                                                                                                                                                               select p into trans
                                                                                                                                                               where (int)trans.TransactionType == 1
                                                                                                                                                               select trans).Sum(x => (double?)(x.Quantity * x.Factor) ?? 0.0),
                                                                           IssueTransferQty = (g.Where((ProductTransaction trans) => (int)trans.Type == 11 && (int)trans.TransactionType == 1).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0),
                                                                           ReceiptTransferQty = (g.Where((ProductTransaction trans) => (int)trans.Type == 11 && (int)trans.TransactionType == 0).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0),
                                                                           ExpiredQty = (g.Where((ProductTransaction trans) => (int)trans.Type == 10).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0)
                                                                       };
                return Data.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
    }
}
