﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("AcualMaterialConsumptions")]
    [DisplayColumn("Consommations réelles de matières premières")]
    public class ActualMaterialConsumption : ProductTransaction
    {
        [Display(Name = "Code de l'ordre")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int OrderID
        {
            get
            {
                return this.orderID;
            }
            set
            {
                base.SetProperty<int>(ref this.orderID, value, "OrderID");
            }
        }

        public WorkOrder Order { get; set; }

        private int orderID;
    }
}
