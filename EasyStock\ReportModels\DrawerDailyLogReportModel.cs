﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class DrawerDailyLogReportModel
    {
        public int ID { get; set; }

        [Display(Name = "Énoncé")]
        public string Statment { get; set; }

        [Display(Name = "Source du journal")]
        public SystemProcess ProcessType { get; set; }

        [Display(Name = "Code source")]
        public int ProcessID { get; set; }

        [Display(Name = "Entrée")]
        public double Debit { get; set; }

        [Display(Name = "Sortie")]
        public double Credit { get; set; }

        [Display(Name = "Entrée locale")]
        public double LocalDebit
        {
            get
            {
                return this.Debit * this.CurrencyRate;
            }
        }

        [Display(Name = "Sortie locale")]
        public double LocalCredit
        {
            get
            {
                return this.Credit * this.CurrencyRate;
            }
        }

        [Display(Name = "Devise")]
        public string Currency { get; set; }

        [Display(Name = "Taux de change")]
        public double CurrencyRate { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }
    }
}
