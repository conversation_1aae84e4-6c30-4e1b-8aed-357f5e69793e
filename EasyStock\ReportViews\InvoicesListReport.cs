﻿using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports.PersonalOperations;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{

    public partial class InvoicesListReport : XtraReportForm
    {
        private readonly SystemProcess systemProcess;

        public InvoicesListReport(SystemProcess systemProcess)
            : base(new ReportFilter[1] { ReportFilter.Date })
        {
            this.systemProcess = systemProcess;
            switch (systemProcess)
            {
                case SystemProcess.Sales:
                    Text = "Rapport des factures de ventes";
                    break;
                case SystemProcess.SalesReturn:
                    Text = "Rapport des factures de retour de vente";
                    break;
                case SystemProcess.Purchase:
                    Text = "Rapport des factures d'achat";
                    break;
                case SystemProcess.PurchaseReturn:
                    Text = "Rapport des factures de retour d'achat";
                    break;
            }
        }

        public override void RefreshDataSource()
        {
            if (!ValidateFilters())
            {
                return;
            }
            Reports.Invoices.InvoicesListReport rpt = new Reports.Invoices.InvoicesListReport();
            rpt.objectDataSource1.DataSource = new CompanyInfoReportModel();
            rpt.SetCompanyInfo(rpt.objectDataSource1.DataSource as CompanyInfoReportModel);
            switch (systemProcess)
            {
                case SystemProcess.Sales:
                    {
                        IQueryable<SalesInvoice> sq = db.SalesInvoices.AsQueryable();
                        if (base.FiltersForm.hasStartDate)
                        {
                            sq = sq.Where((SalesInvoice x) => DbFunctions.TruncateTime(x.Date) >= FiltersForm.startDate).AsQueryable();
                        }
                        if (base.FiltersForm.hasEndtDate)
                        {
                            sq = sq.Where((SalesInvoice x) => DbFunctions.TruncateTime(x.Date) <= FiltersForm.endtDate).AsQueryable();
                        }
                        List<int> Sales = sq.Select((SalesInvoice x) => x.ID).ToList();
                        if (Sales.Count > 0)
                        {
                            List<SalesInvoiceReportModel> SalesInvoiceForm = EasyStock.Views.SalesInvoiceForm.GetPrintDataSource(Sales);
                            rpt.xrInvoices.ReportSource = new InvoiceReport(SalesInvoiceForm, detailReport: false);
                        }
                        break;
                    }
                case SystemProcess.SalesReturn:
                    {
                        IQueryable<SalesReturnInvoice> srq = db.SalesReturnInvoices.AsQueryable();
                        if (base.FiltersForm.hasStartDate)
                        {
                            srq = srq.Where((SalesReturnInvoice x) => DbFunctions.TruncateTime(x.Date) >= FiltersForm.startDate).AsQueryable();
                        }
                        if (base.FiltersForm.hasEndtDate)
                        {
                            srq = srq.Where((SalesReturnInvoice x) => DbFunctions.TruncateTime(x.Date) <= FiltersForm.endtDate).AsQueryable();
                        }
                        List<int> SalesReturn = srq.Select((SalesReturnInvoice x) => x.ID).ToList();
                        if (SalesReturn.Count > 0)
                        {
                            List<SalesReturnInvoiceReportModel> SalesReturnInvoiceForm = Views.SalesReturnInvoiceForm.GetPrintDataSource(SalesReturn);
                            rpt.xrInvoices.ReportSource = new InvoiceReport(SalesReturnInvoiceForm, detailReport: false);
                        }
                        break;
                    }
                case SystemProcess.Purchase:
                    {
                        IQueryable<PurchaseInvoice> pq = db.PurchaseInvoices.AsQueryable();
                        if (base.FiltersForm.hasStartDate)
                        {
                            pq = pq.Where((PurchaseInvoice x) => DbFunctions.TruncateTime(x.Date) >= FiltersForm.startDate).AsQueryable();
                        }
                        if (base.FiltersForm.hasEndtDate)
                        {
                            pq = pq.Where((PurchaseInvoice x) => DbFunctions.TruncateTime(x.Date) <= FiltersForm.endtDate).AsQueryable();
                        }
                        List<int> Purchase = pq.Select((PurchaseInvoice x) => x.ID).ToList();
                        if (Purchase.Count > 0)
                        {
                            List<SalesInvoiceReportModel> SalesInvoiceForm = EasyStock.Views.SalesInvoiceForm.GetPrintDataSource(Purchase);
                            rpt.xrInvoices.ReportSource = new InvoiceReport(SalesInvoiceForm, detailReport: false);
                        }
                        break;
                    }
                case SystemProcess.PurchaseReturn:
                    {
                        IQueryable<SalesReturnInvoice> prq = db.SalesReturnInvoices.AsQueryable();
                        if (base.FiltersForm.hasStartDate)
                        {
                            prq = prq.Where((SalesReturnInvoice x) => DbFunctions.TruncateTime(x.Date) >= FiltersForm.startDate).AsQueryable();
                        }
                        if (base.FiltersForm.hasEndtDate)
                        {
                            prq = prq.Where((SalesReturnInvoice x) => DbFunctions.TruncateTime(x.Date) <= FiltersForm.endtDate).AsQueryable();
                        }
                        List<int> PurchaseReturn = prq.Select((SalesReturnInvoice x) => x.ID).ToList();
                        if (PurchaseReturn.Count > 0)
                        {
                            List<SalesReturnInvoiceReportModel> SalesReturnInvoiceForm = EasyStock.Views.SalesReturnInvoiceForm.GetPrintDataSource(PurchaseReturn);
                            rpt.xrInvoices.ReportSource = new InvoiceReport(SalesReturnInvoiceForm, detailReport: false);
                        }
                        break;
                    }
            }
            rpt.Cell_ReportName.Text = Text;
            rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
            documentViewer1.DocumentSource = rpt;
            Task.Run(() =>
            {
                rpt.CreateDocument();
                this.Invoke(new Action(() =>
                {
                    this.documentViewer1.Refresh();
                }));
            });
        }
    }
}
