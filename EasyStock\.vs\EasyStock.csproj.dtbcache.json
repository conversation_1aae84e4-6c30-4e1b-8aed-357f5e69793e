{"RootPath": "C:\\Users\\<USER>\\Desktop\\Hass\\EasyStock", "ProjectFileName": "EasyStock.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AccountType.cs"}, {"SourceFile": "ApplicationSettings.cs"}, {"SourceFile": "BillHelper.cs"}, {"SourceFile": "BillSourceType.cs"}, {"SourceFile": "BillState.cs"}, {"SourceFile": "Charts\\BanksBalances.cs"}, {"SourceFile": "Charts\\BaseChart.cs"}, {"SourceFile": "Charts\\DueInvoices.cs"}, {"SourceFile": "Charts\\GridChart.cs"}, {"SourceFile": "Charts\\LeastProfitableProducts.cs"}, {"SourceFile": "Charts\\LeastSoldProductChart.cs"}, {"SourceFile": "Charts\\Models\\DailySalesSummery.cs"}, {"SourceFile": "Charts\\Models\\IWidget.cs"}, {"SourceFile": "Charts\\Models\\TopSoldProductsChartModel.cs"}, {"SourceFile": "Charts\\MostProfitableProducts.cs"}, {"SourceFile": "Charts\\SalesPerformanceDailyChart.cs"}, {"SourceFile": "Charts\\SalesPerformanceMonthlyChart.cs"}, {"SourceFile": "Charts\\StagentProductsCharts.cs"}, {"SourceFile": "Charts\\TopSoldProductChart.cs"}, {"SourceFile": "Charts\\TotalBanksBalances.cs"}, {"SourceFile": "Charts\\XtraReport2.cs"}, {"SourceFile": "Charts\\XtraReport2.Designer.cs"}, {"SourceFile": "Classes\\AccountHelper.cs"}, {"SourceFile": "Classes\\BaseNotifyPropertyChangedModel.cs"}, {"SourceFile": "Classes\\BillsConverter.cs"}, {"SourceFile": "Classes\\ConvertMoneyFrench.cs"}, {"SourceFile": "Classes\\CurrentSession.cs"}, {"SourceFile": "Classes\\CustomAttributes.cs"}, {"SourceFile": "Classes\\LangResource.cs"}, {"SourceFile": "Classes\\LookAndFeelSettings.cs"}, {"SourceFile": "Classes\\NavigationObjects.cs"}, {"SourceFile": "Classes\\ProductHelper.cs"}, {"SourceFile": "Classes\\ProductRowDetailManger.cs"}, {"SourceFile": "Classes\\RequiredIfAttribute.cs"}, {"SourceFile": "Classes\\UserAuthentication.cs"}, {"SourceFile": "Classes\\Utilities.cs"}, {"SourceFile": "CloseAccountType.cs"}, {"SourceFile": "Controller\\ChangeTrackerItem.cs"}, {"SourceFile": "Controller\\ERPDataContext.cs"}, {"SourceFile": "Controller\\UserLog.cs"}, {"SourceFile": "Controls\\AccountBalance.cs"}, {"SourceFile": "Controls\\ChartsView.cs"}, {"SourceFile": "Controls\\CostDistributionOption.cs"}, {"SourceFile": "Controls\\DashWidgets.cs"}, {"SourceFile": "Controls\\FrequentlyUsedTileControl.cs"}, {"SourceFile": "Controls\\GridPopupContainerControl.cs"}, {"SourceFile": "Controls\\NotficationView.cs"}, {"SourceFile": "Controls\\ProductUnitControl.cs"}, {"SourceFile": "Controls\\ProductUnitsListControl.cs"}, {"SourceFile": "Controls\\ProgressView.cs"}, {"SourceFile": "Controls\\ProgressView.Designer.cs"}, {"SourceFile": "Controls\\ReportPropertiesControl.cs"}, {"SourceFile": "Controls\\SearchView.cs"}, {"SourceFile": "Controls\\Services.cs"}, {"SourceFile": "Controls\\TileNavigationControl.cs"}, {"SourceFile": "Controls\\TwoLevelTileView.cs"}, {"SourceFile": "Controls\\XtraReport1.cs"}, {"SourceFile": "Controls\\XtraReport1.Designer.cs"}, {"SourceFile": "CostCalculationMethod.cs"}, {"SourceFile": "CostCenterRestriction.cs"}, {"SourceFile": "CostDistributionOptions.cs"}, {"SourceFile": "CustomControls\\Models\\Category.cs"}, {"SourceFile": "CustomControls\\Models\\Product.cs"}, {"SourceFile": "Enumerations.cs"}, {"SourceFile": "Exteintions.cs"}, {"SourceFile": "Form1.cs"}, {"SourceFile": "Form1.Designer.cs"}, {"SourceFile": "GUI\\CheckArrowLabel.cs"}, {"SourceFile": "GUI\\SalesWorkFlowUserControl.cs"}, {"SourceFile": "MainViews\\BaseReportForm.cs"}, {"SourceFile": "MainViews\\BaseReportForm.Designer.cs"}, {"SourceFile": "MainViews\\HomeForm.cs"}, {"SourceFile": "MainViews\\HomeForm.Designer.cs"}, {"SourceFile": "MainViews\\HomeForms.cs"}, {"SourceFile": "MainViews\\HomeForms.Designer.cs"}, {"SourceFile": "MainViews\\HomeScreen.cs"}, {"SourceFile": "MainViews\\HomeScreen.Designer.cs"}, {"SourceFile": "MainViews\\LogonForm.cs"}, {"SourceFile": "MainViews\\LogonForm.Designer.cs"}, {"SourceFile": "MainViews\\MasterForm.cs"}, {"SourceFile": "MainViews\\MasterForm.Designer.cs"}, {"SourceFile": "MainViews\\XtraReportForm.cs"}, {"SourceFile": "MainViews\\XtraReportForm.Designer.cs"}, {"SourceFile": "MethodOfPayment.cs"}, {"SourceFile": "Migrations\\202408041526537_2024.cs"}, {"SourceFile": "Migrations\\202408041526537_2024.Designer.cs"}, {"SourceFile": "Migrations\\adCahTrnsferMdl.cs"}, {"SourceFile": "Migrations\\addBranchEntity.cs"}, {"SourceFile": "Migrations\\addCostCenter.cs"}, {"SourceFile": "Migrations\\addCurrentAssetsAndOwnerEqAccountSettings.cs"}, {"SourceFile": "Migrations\\AddDateColumnColumnInPettyCash.cs"}, {"SourceFile": "Migrations\\AddDueDatetoBillAndjournalDetails.cs"}, {"SourceFile": "Migrations\\addFieldsToSalesInvoices2.cs"}, {"SourceFile": "Migrations\\AddProductCodetoProductTransaction.cs"}, {"SourceFile": "Migrations\\AddProductCustomField.cs"}, {"SourceFile": "Migrations\\addReportTemplate.cs"}, {"SourceFile": "Migrations\\addSalesPriceOffer.cs"}, {"SourceFile": "Migrations\\addTaxPercToInvoicesDetails.cs"}, {"SourceFile": "Migrations\\addTwoAccountSettings.cs"}, {"SourceFile": "Migrations\\AddUseIdToJornals.cs"}, {"SourceFile": "Migrations\\AddUserDefualtScreen.cs"}, {"SourceFile": "Migrations\\AddUserIDToBillandjournalscashnoteRevExpEntry.cs"}, {"SourceFile": "Migrations\\AddUserIDToRevExpEntry.cs"}, {"SourceFile": "Migrations\\addUserLogTable.cs"}, {"SourceFile": "Migrations\\addUserLogTable2.cs"}, {"SourceFile": "Migrations\\addusertocashnotes.cs"}, {"SourceFile": "Migrations\\AddVendorInvoice.cs"}, {"SourceFile": "Migrations\\add_Branch_ToDrawerPeriod.cs"}, {"SourceFile": "Migrations\\add_CanChangeBranchToUserSettings.cs"}, {"SourceFile": "Migrations\\add_Tax_To_Revexpences.cs"}, {"SourceFile": "Migrations\\ChangesToPettyCashAccounts.cs"}, {"SourceFile": "Migrations\\Configuration.cs"}, {"SourceFile": "Migrations\\Contractor_Abstract.cs"}, {"SourceFile": "Migrations\\DefualtUserprint.cs"}, {"SourceFile": "Migrations\\DrawerPeriodModels.cs"}, {"SourceFile": "Migrations\\GetNewCodeAbstract.cs"}, {"SourceFile": "Migrations\\GetNewCodeVendorInvoice.cs"}, {"SourceFile": "Migrations\\Initial.cs"}, {"SourceFile": "Migrations\\invoiceSourcesAndCostOfSoldGoods.cs"}, {"SourceFile": "Migrations\\InvoicesSourceMigration.cs"}, {"SourceFile": "Migrations\\NewCode.cs"}, {"SourceFile": "Migrations\\outGoingBillEntity.cs"}, {"SourceFile": "Migrations\\PettyCashCloseOutAmount.cs"}, {"SourceFile": "Migrations\\PettyCashModels.cs"}, {"SourceFile": "Migrations\\printedBarcodeModel.cs"}, {"SourceFile": "Migrations\\productLocationInStore.cs"}, {"SourceFile": "Migrations\\RemoveNameColumnInPettyCash.cs"}, {"SourceFile": "Migrations\\RenameColumnsInCashNotes.cs"}, {"SourceFile": "Migrations\\rename_branchs_to_store.cs"}, {"SourceFile": "Migrations\\SalesOrderAndOffersSates.cs"}, {"SourceFile": "Migrations\\SalesOrderMigration.cs"}, {"SourceFile": "Migrations\\SalesPriceOfferInitialMigration.cs"}, {"SourceFile": "Migrations\\screenFrequency.cs"}, {"SourceFile": "Migrations\\StockCorrectionTables.cs"}, {"SourceFile": "Migrations\\TaxDeclarationMigration.cs"}, {"SourceFile": "Migrations\\test.cs"}, {"SourceFile": "Migrations\\test1.cs"}, {"SourceFile": "Migrations\\TransactionStateAddedToEntities.cs"}, {"SourceFile": "Migrations\\updateContractorAbstract.cs"}, {"SourceFile": "Migrations\\updateOnReportTemplate.cs"}, {"SourceFile": "Migrations\\UpdteOnPrdutCategory.cs"}, {"SourceFile": "Migrations\\workorder.cs"}, {"SourceFile": "Models\\Account.cs"}, {"SourceFile": "Models\\ActualCost.cs"}, {"SourceFile": "Models\\ActualMaterialConsumption.cs"}, {"SourceFile": "Models\\Bank.cs"}, {"SourceFile": "Models\\Bill.cs"}, {"SourceFile": "Models\\BillingDetail.cs"}, {"SourceFile": "Models\\BillOfMaterials.cs"}, {"SourceFile": "Models\\BOMDetails.cs"}, {"SourceFile": "Models\\BOMExpenses.cs"}, {"SourceFile": "Models\\Branch.cs"}, {"SourceFile": "Models\\CashLinkType.cs"}, {"SourceFile": "Models\\CashNote.cs"}, {"SourceFile": "Models\\CashNotePaySourceType.cs"}, {"SourceFile": "Models\\CashNoteType.cs"}, {"SourceFile": "Models\\CashTransfer.cs"}, {"SourceFile": "Models\\CompanyInfo.cs"}, {"SourceFile": "Models\\ContractorAbstract.cs"}, {"SourceFile": "Models\\CostCenter.cs"}, {"SourceFile": "Models\\Currency.cs"}, {"SourceFile": "Models\\Customer.cs"}, {"SourceFile": "Models\\CustomerGroup.cs"}, {"SourceFile": "Models\\DefaultExpenses.cs"}, {"SourceFile": "Models\\DefaultMaterialConsumption.cs"}, {"SourceFile": "Models\\Drawer.cs"}, {"SourceFile": "Models\\DrawerPeriod.cs"}, {"SourceFile": "Models\\DrawerPeriodTransSummeryItem.cs"}, {"SourceFile": "Models\\FrequentlyUsedScreen.cs"}, {"SourceFile": "Models\\GroupOfProductDiscountType.cs"}, {"SourceFile": "Models\\GroupOfProducts.cs"}, {"SourceFile": "Models\\GroupOfProductsDetails.cs"}, {"SourceFile": "Models\\GroupOfProductsExpireType.cs"}, {"SourceFile": "Models\\IBill.cs"}, {"SourceFile": "Models\\InvoiceDetail.cs"}, {"SourceFile": "Models\\InvoiceShortCut.cs"}, {"SourceFile": "Models\\InvoiceShortCutAction.cs"}, {"SourceFile": "Models\\IProductRowDetail.cs"}, {"SourceFile": "Models\\ISalesBill.cs"}, {"SourceFile": "Models\\Journal.cs"}, {"SourceFile": "Models\\JournalDetail.cs"}, {"SourceFile": "Models\\OpenBalanceBill.cs"}, {"SourceFile": "Models\\OutgoingBill.cs"}, {"SourceFile": "Models\\PayCard.cs"}, {"SourceFile": "Models\\PayDetail.cs"}, {"SourceFile": "Models\\PayMethodType.cs"}, {"SourceFile": "Models\\Personal.cs"}, {"SourceFile": "Models\\PersonalGroup.cs"}, {"SourceFile": "Models\\PersonalType.cs"}, {"SourceFile": "Models\\PettyCash.cs"}, {"SourceFile": "Models\\PettyCashCloseOut.cs"}, {"SourceFile": "Models\\PettyCashHolder.cs"}, {"SourceFile": "Models\\PriceList.cs"}, {"SourceFile": "Models\\PrintedBarcode.cs"}, {"SourceFile": "Models\\Product.cs"}, {"SourceFile": "Models\\ProductCategory.cs"}, {"SourceFile": "Models\\ProductColor.cs"}, {"SourceFile": "Models\\ProductCompany.cs"}, {"SourceFile": "Models\\ProductCustomField.cs"}, {"SourceFile": "Models\\ProductDamageBill.cs"}, {"SourceFile": "Models\\ProductSize.cs"}, {"SourceFile": "Models\\ProductStoreLocation.cs"}, {"SourceFile": "Models\\ProductTransaction.cs"}, {"SourceFile": "Models\\ProductType.cs"}, {"SourceFile": "Models\\ProductUnit.cs"}, {"SourceFile": "Models\\ProductUnitBarcode.cs"}, {"SourceFile": "Models\\ProductVendor.cs"}, {"SourceFile": "Models\\PurchaseInvoice.cs"}, {"SourceFile": "Models\\PurchaseReturnInvoice.cs"}, {"SourceFile": "Models\\ReportTemplate.cs"}, {"SourceFile": "Models\\RevExpEntry.cs"}, {"SourceFile": "Models\\RevExpEntryDetail.cs"}, {"SourceFile": "Models\\RevExpEntryType.cs"}, {"SourceFile": "Models\\SalesInvoice.cs"}, {"SourceFile": "Models\\SalesOrder.cs"}, {"SourceFile": "Models\\SalesOrderDetail.cs"}, {"SourceFile": "Models\\SalesPriceOffer.cs"}, {"SourceFile": "Models\\SalesPriceOfferDetail.cs"}, {"SourceFile": "Models\\SalesReturnInvoice.cs"}, {"SourceFile": "Models\\SimilarProduct.cs"}, {"SourceFile": "Models\\StockBalanceAfterCorrectionDetail.cs"}, {"SourceFile": "Models\\StockBalanceBeforeCorrectionDetail.cs"}, {"SourceFile": "Models\\StockBalanceCorrection.cs"}, {"SourceFile": "Models\\StockTransferBill.cs"}, {"SourceFile": "Models\\Store.cs"}, {"SourceFile": "Models\\SystemSettings.cs"}, {"SourceFile": "Models\\TransactionLinkModel.cs"}, {"SourceFile": "Models\\UnitOfMeasurement.cs"}, {"SourceFile": "Models\\User.cs"}, {"SourceFile": "Models\\UserAccessProfile.cs"}, {"SourceFile": "Models\\UserAccessProfileDetail.cs"}, {"SourceFile": "Models\\UserSettingsProfile.cs"}, {"SourceFile": "Models\\UserType.cs"}, {"SourceFile": "Models\\Vendor.cs"}, {"SourceFile": "Models\\VendorGroup.cs"}, {"SourceFile": "Models\\VendorInvoice.cs"}, {"SourceFile": "Models\\WorkOrder.cs"}, {"SourceFile": "Models\\WorkOrderDetails.cs"}, {"SourceFile": "Models\\WorkType.cs"}, {"SourceFile": "MoneyToTextModes.cs"}, {"SourceFile": "PayType.cs"}, {"SourceFile": "PriceListType.cs"}, {"SourceFile": "PriceOfferType.cs"}, {"SourceFile": "PrintMode.cs"}, {"SourceFile": "ProductTransactionType.cs"}, {"SourceFile": "ProductUnitsMode.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "ReadValueMode.cs"}, {"SourceFile": "RedundancyOptions.cs"}, {"SourceFile": "ReportFilter.cs"}, {"SourceFile": "ReportForm.cs"}, {"SourceFile": "ReportForm.Designer.cs"}, {"SourceFile": "ReportModels\\AccountsBalanceReportModel.cs"}, {"SourceFile": "ReportModels\\BalanceSheetReportModel.cs"}, {"SourceFile": "ReportModels\\BarcodeModel.cs"}, {"SourceFile": "ReportModels\\BillOfMaterialsReportModel.cs"}, {"SourceFile": "ReportModels\\BOMDEtails.cs"}, {"SourceFile": "ReportModels\\CashNoteReportModel.cs"}, {"SourceFile": "ReportModels\\CashTransferReportModel.cs"}, {"SourceFile": "ReportModels\\CompanyInfoReportModel.cs"}, {"SourceFile": "ReportModels\\Consumptions.cs"}, {"SourceFile": "ReportModels\\ContractorAbstractReportModel.cs"}, {"SourceFile": "ReportModels\\Details.cs"}, {"SourceFile": "ReportModels\\DrawerDailyLogReportModel.cs"}, {"SourceFile": "ReportModels\\IncomStatment\\IncomeStatmentModel.cs"}, {"SourceFile": "ReportModels\\IncomStatment\\IncomeStatmentModelDetial.cs"}, {"SourceFile": "ReportModels\\InOutModel.cs"}, {"SourceFile": "ReportModels\\InvoiceDetailReportModel.cs"}, {"SourceFile": "ReportModels\\InvoiceReportModel.cs"}, {"SourceFile": "ReportModels\\InvoicesPayStatus.cs"}, {"SourceFile": "ReportModels\\JournalDetailReportModel.cs"}, {"SourceFile": "ReportModels\\JournalReportModel.cs"}, {"SourceFile": "ReportModels\\MerchandisingAccountStatmentModel.cs"}, {"SourceFile": "ReportModels\\PayDetailReportModel.cs"}, {"SourceFile": "ReportModels\\PersonalInfoSummaryModel.cs"}, {"SourceFile": "ReportModels\\PettyCashCloseOutReportModel.cs"}, {"SourceFile": "ReportModels\\PettyCashReportModel.cs"}, {"SourceFile": "ReportModels\\ProductBalanceDetailedInStoreReportModel.cs"}, {"SourceFile": "ReportModels\\ProductBalanceInStore.cs"}, {"SourceFile": "ReportModels\\ProductBalanceInStoreReportModel.cs"}, {"SourceFile": "ReportModels\\ProductContentsInventory.cs"}, {"SourceFile": "ReportModels\\ProductContentsInventoryDetail.cs"}, {"SourceFile": "ReportModels\\ProductInOutBalanceModel.cs"}, {"SourceFile": "ReportModels\\ProductInOutModel.cs"}, {"SourceFile": "ReportModels\\ProductListModel.cs"}, {"SourceFile": "ReportModels\\ProductReachedReorderLevelReportModel.cs"}, {"SourceFile": "ReportModels\\ProductSearchModel.cs"}, {"SourceFile": "ReportModels\\ProductStoreTransactionReportModel.cs"}, {"SourceFile": "ReportModels\\ProductStoreValidityReprtModel.cs"}, {"SourceFile": "ReportModels\\ProductTransactionReportModel.cs"}, {"SourceFile": "ReportModels\\PurchaseInvoiceReportModel.cs"}, {"SourceFile": "ReportModels\\PurchaseReturnInvoiceReportModel.cs"}, {"SourceFile": "ReportModels\\PurchaseTaxesReportModel.cs"}, {"SourceFile": "ReportModels\\PurchaseTaxsReportModel.cs"}, {"SourceFile": "ReportModels\\QuantitySalesAndProduct.cs"}, {"SourceFile": "ReportModels\\RevExpEntryDetailReportModel.cs"}, {"SourceFile": "ReportModels\\RevExpEntryReportModel.cs"}, {"SourceFile": "ReportModels\\SalesCustomerProductsModel.cs"}, {"SourceFile": "ReportModels\\SalesInvoiceReportModel.cs"}, {"SourceFile": "ReportModels\\SalesOrderReportModel.cs"}, {"SourceFile": "ReportModels\\SalesPriceOfferReportModel.cs"}, {"SourceFile": "ReportModels\\SalesProductCategoriesCostModel.cs"}, {"SourceFile": "ReportModels\\SalesProductCategoriesMainModel.cs"}, {"SourceFile": "ReportModels\\SalesProductCategoriesSubModel.cs"}, {"SourceFile": "ReportModels\\SalesProductCostModel.cs"}, {"SourceFile": "ReportModels\\SalesReturnInvoiceReportModel.cs"}, {"SourceFile": "ReportModels\\SalesSummeryOfPurchaseInvoicesReportModel.cs"}, {"SourceFile": "ReportModels\\StatmentOfAccountModel.cs"}, {"SourceFile": "ReportModels\\StockBalanceCorrectionDetailReportModel.cs"}, {"SourceFile": "ReportModels\\StockBalanceCorrectionReportModel.cs"}, {"SourceFile": "ReportModels\\TotalProductTransactionsReportModel.cs"}, {"SourceFile": "ReportModels\\TotalPurchaseInvoicesReportModel.cs"}, {"SourceFile": "ReportModels\\TotalPurchaseReturnReportModel.cs"}, {"SourceFile": "ReportModels\\TotalSalesCustomerProductsModel.cs"}, {"SourceFile": "ReportModels\\TotalSalesInvoicesReportModel.cs"}, {"SourceFile": "ReportModels\\TotalSalesProductCategoriesModel.cs"}, {"SourceFile": "ReportModels\\TotalSalesReturnReportModel.cs"}, {"SourceFile": "ReportModels\\TrialBalanceReportModel.cs"}, {"SourceFile": "ReportModels\\VendorInvoiceReportModel.cs"}, {"SourceFile": "ReportModels\\WorkOrderReportModel.cs"}, {"SourceFile": "Reports\\AccountsBalanceReports.cs"}, {"SourceFile": "Reports\\AccountsBalanceReports.Designer.cs"}, {"SourceFile": "Reports\\BarcodeReport.cs"}, {"SourceFile": "Reports\\BillOfMaterialsReport.cs"}, {"SourceFile": "Reports\\CashNoteReport.cs"}, {"SourceFile": "Reports\\CashTransferReport.cs"}, {"SourceFile": "Reports\\ContractorAbstractReport.cs"}, {"SourceFile": "Reports\\GridReportP.cs"}, {"SourceFile": "Reports\\IncomeStatmentReport.cs"}, {"SourceFile": "Reports\\Invoices\\InvoicesListReport.cs"}, {"SourceFile": "Reports\\JournalReport.cs"}, {"SourceFile": "Reports\\MasterReport.cs"}, {"SourceFile": "Reports\\PersonalOperations\\CashNoteReport.cs"}, {"SourceFile": "Reports\\PersonalOperations\\InvoiceReport.cs"}, {"SourceFile": "Reports\\PersonalOperations\\PersonalInfoReport.cs"}, {"SourceFile": "Reports\\PersonalOperations\\PersonalOperationsReport.cs"}, {"SourceFile": "Reports\\PettyCashCloseOutReport.cs"}, {"SourceFile": "Reports\\PettyCashReport.cs"}, {"SourceFile": "Reports\\ProductContentsInventoryReport.cs"}, {"SourceFile": "Reports\\ProductInOutBalanceReport.cs"}, {"SourceFile": "Reports\\ProductStoreValidityReport.cs"}, {"SourceFile": "Reports\\PurchaseInvoiceReport.cs"}, {"SourceFile": "Reports\\PurchaseReturnInvoiceReport.cs"}, {"SourceFile": "Reports\\PurchaseTaxReport.cs"}, {"SourceFile": "Reports\\QuantitySalesAndProductReport.cs"}, {"SourceFile": "Reports\\RevExpEntryReport.cs"}, {"SourceFile": "Reports\\SalesCustomerProductsReport.cs"}, {"SourceFile": "Reports\\SalesInvoiceReport.cs"}, {"SourceFile": "Reports\\SalesOrderReport.cs"}, {"SourceFile": "Reports\\SalesPriceOfferReport.cs"}, {"SourceFile": "Reports\\SalesProductCategoriesCostReport.cs"}, {"SourceFile": "Reports\\SalesProductCategoriesSubReport.cs"}, {"SourceFile": "Reports\\SalesProductCatergotiesMainReport.cs"}, {"SourceFile": "Reports\\SalesProductsCostReport.cs"}, {"SourceFile": "Reports\\SalesReturnInvoiceReport.cs"}, {"SourceFile": "Reports\\SalesTaxsReport.cs"}, {"SourceFile": "Reports\\TotalProductTransactionsReport.cs"}, {"SourceFile": "Reports\\TotalPurchaseInvoicesReport.cs"}, {"SourceFile": "Reports\\TotalPurchaseReturnReport.cs"}, {"SourceFile": "Reports\\TotalSalesCustomerProductsReport.cs"}, {"SourceFile": "Reports\\TotalSalesInvoicesReport.cs"}, {"SourceFile": "Reports\\TotalSalesProductCategoriesReport.cs"}, {"SourceFile": "Reports\\TotalSalesReturnReport.cs"}, {"SourceFile": "Reports\\VendorInvoiceReport.cs"}, {"SourceFile": "Reports\\WorkOrderReport.cs"}, {"SourceFile": "ReportTypes.cs"}, {"SourceFile": "ReportViews\\AccountsBalanceReportView.cs"}, {"SourceFile": "ReportViews\\AccountsBalanceReportView.Designer.cs"}, {"SourceFile": "ReportViews\\BalanceSheetReportView.cs"}, {"SourceFile": "ReportViews\\BalanceSheetReportView.Designer.cs"}, {"SourceFile": "ReportViews\\FilterForm.cs"}, {"SourceFile": "ReportViews\\FilterForm.Designer.cs"}, {"SourceFile": "ReportViews\\IncomeStatmentReportView.cs"}, {"SourceFile": "ReportViews\\IncomeStatmentReportView.Designer.cs"}, {"SourceFile": "ReportViews\\InvoicesListReport.cs"}, {"SourceFile": "ReportViews\\InvoicesListReport.Designer.cs"}, {"SourceFile": "ReportViews\\MerchandisingAccountStatment.cs"}, {"SourceFile": "ReportViews\\MerchandisingAccountStatment.Designer.cs"}, {"SourceFile": "ReportViews\\PersonalOperations.cs"}, {"SourceFile": "ReportViews\\PersonalOperations.Designer.cs"}, {"SourceFile": "ReportViews\\ProductBalanceDetailedInStores.cs"}, {"SourceFile": "ReportViews\\ProductBalanceDetailedInStores.Designer.cs"}, {"SourceFile": "ReportViews\\ProductBalanceInStores.cs"}, {"SourceFile": "ReportViews\\ProductBalanceInStores.Designer.cs"}, {"SourceFile": "ReportViews\\ProductInOutBalance.cs"}, {"SourceFile": "ReportViews\\ProductInOutBalance.Designer.cs"}, {"SourceFile": "ReportViews\\ProductReachedReorderLevel.cs"}, {"SourceFile": "ReportViews\\ProductReachedReorderLevel.Designer.cs"}, {"SourceFile": "ReportViews\\ProductSalesSummryFromPurchaseInvoicesView.cs"}, {"SourceFile": "ReportViews\\ProductSalesSummryFromPurchaseInvoicesView.Designer.cs"}, {"SourceFile": "ReportViews\\ProductStoreTransaction.cs"}, {"SourceFile": "ReportViews\\ProductStoreTransaction.Designer.cs"}, {"SourceFile": "ReportViews\\ProductStoreValidityReportView.cs"}, {"SourceFile": "ReportViews\\ProductStoreValidityReportView.Designer.cs"}, {"SourceFile": "ReportViews\\PurchaseTaxesReportView.cs"}, {"SourceFile": "ReportViews\\PurchaseTaxesReportView.Designer.cs"}, {"SourceFile": "ReportViews\\SalesCustomerProductView.cs"}, {"SourceFile": "ReportViews\\SalesCustomerProductView.Designer.cs"}, {"SourceFile": "ReportViews\\SalesProductCategoriesCostView.cs"}, {"SourceFile": "ReportViews\\SalesProductCategoriesCostView.Designer.cs"}, {"SourceFile": "ReportViews\\SalesProductCatergoriesReportView.cs"}, {"SourceFile": "ReportViews\\SalesProductCatergoriesReportView.Designer.cs"}, {"SourceFile": "ReportViews\\SalesProductCostView.cs"}, {"SourceFile": "ReportViews\\SalesProductCostView.Designer.cs"}, {"SourceFile": "ReportViews\\SalesTaxsReportView.cs"}, {"SourceFile": "ReportViews\\SalesTaxsReportView.Designer.cs"}, {"SourceFile": "ReportViews\\StatmentOfAccount.cs"}, {"SourceFile": "ReportViews\\StatmentOfAccount.Designer.cs"}, {"SourceFile": "ReportViews\\TotalProductTransactionsReportView.cs"}, {"SourceFile": "ReportViews\\TotalProductTransactionsReportView.Designer.cs"}, {"SourceFile": "ReportViews\\TotalPurchaseInvoicesReportView.cs"}, {"SourceFile": "ReportViews\\TotalPurchaseInvoicesReportView.Designer.cs"}, {"SourceFile": "ReportViews\\TotalPurchaseReturnReportView.cs"}, {"SourceFile": "ReportViews\\TotalPurchaseReturnReportView.Designer.cs"}, {"SourceFile": "ReportViews\\TotalSalesCustomerProductView.cs"}, {"SourceFile": "ReportViews\\TotalSalesCustomerProductView.Designer.cs"}, {"SourceFile": "ReportViews\\TotalSalesInvoicesReportView.cs"}, {"SourceFile": "ReportViews\\TotalSalesInvoicesReportView.Designer.cs"}, {"SourceFile": "ReportViews\\TotalSalesProductCategoriesView.cs"}, {"SourceFile": "ReportViews\\TotalSalesProductCategoriesView.Designer.cs"}, {"SourceFile": "ReportViews\\TotalSalesReturnReportView.cs"}, {"SourceFile": "ReportViews\\TotalSalesReturnReportView.Designer.cs"}, {"SourceFile": "ReportViews\\TrialBalance.cs"}, {"SourceFile": "ReportViews\\TrialBalance.Designer.cs"}, {"SourceFile": "SecracyLevel.cs"}, {"SourceFile": "SystemProcess.cs"}, {"SourceFile": "Texting.cs"}, {"SourceFile": "TransactionType.cs"}, {"SourceFile": "Views\\AccountsFrom.cs"}, {"SourceFile": "Views\\AccountsFrom.Designer.cs"}, {"SourceFile": "Views\\AddProductsView.cs"}, {"SourceFile": "Views\\AddProductsView.Designer.cs"}, {"SourceFile": "Views\\AddQuickVendor.cs"}, {"SourceFile": "Views\\AddQuickVendor.Designer.cs"}, {"SourceFile": "Views\\BanksForm.cs"}, {"SourceFile": "Views\\BanksForm.Designer.cs"}, {"SourceFile": "Views\\BillingDetailsForm.cs"}, {"SourceFile": "Views\\BillingDetailsForm.Designer.cs"}, {"SourceFile": "Views\\BranchesView.cs"}, {"SourceFile": "Views\\BranchesView.Designer.cs"}, {"SourceFile": "Views\\CashNoteInListView.cs"}, {"SourceFile": "Views\\CashNoteInListView.Designer.cs"}, {"SourceFile": "Views\\CashNoteInView.cs"}, {"SourceFile": "Views\\CashNoteInView.Designer.cs"}, {"SourceFile": "Views\\CashNoteListView.cs"}, {"SourceFile": "Views\\CashNoteListView.Designer.cs"}, {"SourceFile": "Views\\CashNoteOutListView.cs"}, {"SourceFile": "Views\\CashNoteOutListView.Designer.cs"}, {"SourceFile": "Views\\CashNoteOutView.cs"}, {"SourceFile": "Views\\CashNoteOutView.Designer.cs"}, {"SourceFile": "Views\\CashNoteView.cs"}, {"SourceFile": "Views\\CashNoteView.Designer.cs"}, {"SourceFile": "Views\\CashTransferListView.cs"}, {"SourceFile": "Views\\CashTransferListView.Designer.cs"}, {"SourceFile": "Views\\CashTransferView.cs"}, {"SourceFile": "Views\\CashTransferView.Designer.cs"}, {"SourceFile": "Views\\CompanyInfoForm.cs"}, {"SourceFile": "Views\\CompanyInfoForm.Designer.cs"}, {"SourceFile": "Views\\ConnectToServerForm.cs"}, {"SourceFile": "Views\\ConnectToServerForm.Designer.cs"}, {"SourceFile": "Views\\ContractorAbstractListView.cs"}, {"SourceFile": "Views\\ContractorAbstractListView.Designer.cs"}, {"SourceFile": "Views\\ContractorAbstractView.cs"}, {"SourceFile": "Views\\ContractorAbstractView.Designer.cs"}, {"SourceFile": "Views\\CostCentersView.cs"}, {"SourceFile": "Views\\CostCentersView.Designer.cs"}, {"SourceFile": "Views\\CurrenciesView.cs"}, {"SourceFile": "Views\\CurrenciesView.Designer.cs"}, {"SourceFile": "Views\\CustomersForm.cs"}, {"SourceFile": "Views\\CustomersForm.Designer.cs"}, {"SourceFile": "Views\\CustomersGroupsForm.cs"}, {"SourceFile": "Views\\CustomersGroupsForm.Designer.cs"}, {"SourceFile": "Views\\DrawerDailyLogView.cs"}, {"SourceFile": "Views\\DrawerDailyLogView.Designer.cs"}, {"SourceFile": "Views\\DrawerPeriodsList.cs"}, {"SourceFile": "Views\\DrawerPeriodsList.Designer.cs"}, {"SourceFile": "Views\\DrawersForm.cs"}, {"SourceFile": "Views\\DrawersForm.Designer.cs"}, {"SourceFile": "Views\\ExpenseEntryView.cs"}, {"SourceFile": "Views\\ExpenseEntryView.Designer.cs"}, {"SourceFile": "Views\\ExpenseListView.cs"}, {"SourceFile": "Views\\ExpenseListView.Designer.cs"}, {"SourceFile": "Views\\Financial\\CloseDrawerPeriodForm.cs"}, {"SourceFile": "Views\\Financial\\CloseDrawerPeriodForm.Designer.cs"}, {"SourceFile": "Views\\Financial\\DrawerPeriodForm.cs"}, {"SourceFile": "Views\\Financial\\DrawerPeriodForm.Designer.cs"}, {"SourceFile": "Views\\Financial\\OpenDrawerPeriodForm.cs"}, {"SourceFile": "Views\\Financial\\OpenDrawerPeriodForm.Designer.cs"}, {"SourceFile": "Views\\Financial\\PettyCashCloseOutListView.cs"}, {"SourceFile": "Views\\Financial\\PettyCashCloseOutListView.Designer.cs"}, {"SourceFile": "Views\\Financial\\PettyCashCloseOutView.cs"}, {"SourceFile": "Views\\Financial\\PettyCashCloseOutView.Designer.cs"}, {"SourceFile": "Views\\Financial\\PettyCashHolderView.cs"}, {"SourceFile": "Views\\Financial\\PettyCashHolderView.Designer.cs"}, {"SourceFile": "Views\\Financial\\PettyCashListView.cs"}, {"SourceFile": "Views\\Financial\\PettyCashListView.Designer.cs"}, {"SourceFile": "Views\\Financial\\PettyCashView.cs"}, {"SourceFile": "Views\\Financial\\PettyCashView.Designer.cs"}, {"SourceFile": "Views\\frm_LogViewer.cs"}, {"SourceFile": "Views\\frm_LogViewer.Designer.cs"}, {"SourceFile": "Views\\GroupOfProductsView.cs"}, {"SourceFile": "Views\\GroupOfProductsView.Designer.cs"}, {"SourceFile": "Views\\GroupsOfProductsListView.cs"}, {"SourceFile": "Views\\GroupsOfProductsListView.Designer.cs"}, {"SourceFile": "Views\\ImportProductFromExcelView.cs"}, {"SourceFile": "Views\\InvoiceShortcutForm.cs"}, {"SourceFile": "Views\\InvoiceShortcutForm.Designer.cs"}, {"SourceFile": "Views\\JournalListView.cs"}, {"SourceFile": "Views\\JournalListView.Designer.cs"}, {"SourceFile": "Views\\JournalView.cs"}, {"SourceFile": "Views\\JournalView.Designer.cs"}, {"SourceFile": "Views\\OpenBalanceBillForm.cs"}, {"SourceFile": "Views\\OpenBalanceBillForm.Designer.cs"}, {"SourceFile": "Views\\OutgoingBillView.cs"}, {"SourceFile": "Views\\OutgoingBillView.Designer.cs"}, {"SourceFile": "Views\\PayCardsForm.cs"}, {"SourceFile": "Views\\PayForm.cs"}, {"SourceFile": "Views\\PayForm.Designer.cs"}, {"SourceFile": "Views\\PersonalsForm.cs"}, {"SourceFile": "Views\\PersonalsForm.Designer.cs"}, {"SourceFile": "Views\\PersonalsGroupsForm.cs"}, {"SourceFile": "Views\\PersonalsGroupsForm.Designer.cs"}, {"SourceFile": "Views\\PrintBarcodeView.cs"}, {"SourceFile": "Views\\PrintBarcodeView.Designer.cs"}, {"SourceFile": "Views\\ProductCategoryForm.cs"}, {"SourceFile": "Views\\ProductCategoryForm.Designer.cs"}, {"SourceFile": "Views\\ProductDamageBillForm.cs"}, {"SourceFile": "Views\\ProductDamageBillForm.Designer.cs"}, {"SourceFile": "Views\\ProductDamageBillsListView.cs"}, {"SourceFile": "Views\\ProductDamageBillsListView.Designer.cs"}, {"SourceFile": "Views\\ProductForm.cs"}, {"SourceFile": "Views\\ProductForm.Designer.cs"}, {"SourceFile": "Views\\Production\\BillOfMaterialsListView.cs"}, {"SourceFile": "Views\\Production\\BillOfMaterialsListView.Designer.cs"}, {"SourceFile": "Views\\Production\\BillOfMaterialsView.cs"}, {"SourceFile": "Views\\Production\\BillOfMaterialsView.Designer.cs"}, {"SourceFile": "Views\\Production\\WorkOrderListView.cs"}, {"SourceFile": "Views\\Production\\WorkOrderListView.Designer.cs"}, {"SourceFile": "Views\\Production\\WorkOrderView.cs"}, {"SourceFile": "Views\\Production\\WorkOrderView.Designer.cs"}, {"SourceFile": "Views\\ProductSearchForm.cs"}, {"SourceFile": "Views\\ProductSearchForm.Designer.cs"}, {"SourceFile": "Views\\ProductsForm.cs"}, {"SourceFile": "Views\\ProductsForm.Designer.cs"}, {"SourceFile": "Views\\PurchaseInvoiceForm.cs"}, {"SourceFile": "Views\\PurchaseInvoiceForm.Designer.cs"}, {"SourceFile": "Views\\PurchaseInvoicesListView.cs"}, {"SourceFile": "Views\\PurchaseInvoicesListView.Designer.cs"}, {"SourceFile": "Views\\PurchaseReturnInvoiceForm.cs"}, {"SourceFile": "Views\\PurchaseReturnInvoiceForm.Designer.cs"}, {"SourceFile": "Views\\PurchaseReturnInvoicesListView.cs"}, {"SourceFile": "Views\\PurchaseReturnInvoicesListView.Designer.cs"}, {"SourceFile": "Views\\QuickAddCustomer.cs"}, {"SourceFile": "Views\\QuickAddCustomer.Designer.cs"}, {"SourceFile": "Views\\QuickAddProduct.cs"}, {"SourceFile": "Views\\QuickAddProduct.Designer.cs"}, {"SourceFile": "Views\\ReportCenterView.cs"}, {"SourceFile": "Views\\ReportCenterView.Designer.cs"}, {"SourceFile": "Views\\ReportDesigner.cs"}, {"SourceFile": "Views\\ReportTemplatesView.cs"}, {"SourceFile": "Views\\ReportTemplatesView.Designer.cs"}, {"SourceFile": "Views\\RevenueEntryView.cs"}, {"SourceFile": "Views\\RevenueEntryView.Designer.cs"}, {"SourceFile": "Views\\RevenueListView.cs"}, {"SourceFile": "Views\\RevenueListView.Designer.cs"}, {"SourceFile": "Views\\RevExpEntryListView.cs"}, {"SourceFile": "Views\\RevExpEntryListView.Designer.cs"}, {"SourceFile": "Views\\RevExpEntryView.cs"}, {"SourceFile": "Views\\RevExpEntryView.Designer.cs"}, {"SourceFile": "Views\\SalesInvoiceForm.cs"}, {"SourceFile": "Views\\SalesInvoiceForm.Designer.cs"}, {"SourceFile": "Views\\SalesInvoicesListView.cs"}, {"SourceFile": "Views\\SalesInvoicesListView.Designer.cs"}, {"SourceFile": "Views\\SalesOrdersListView.cs"}, {"SourceFile": "Views\\SalesOrdersListView.Designer.cs"}, {"SourceFile": "Views\\SalesPriceOffersListView.cs"}, {"SourceFile": "Views\\SalesPriceOffersListView.Designer.cs"}, {"SourceFile": "Views\\SalesReturnInvoiceForm.cs"}, {"SourceFile": "Views\\SalesReturnInvoiceForm.Designer.cs"}, {"SourceFile": "Views\\SalesReturnInvoicesListView.cs"}, {"SourceFile": "Views\\SalesReturnInvoicesListView.Designer.cs"}, {"SourceFile": "Views\\Sales\\LoadFromForm.cs"}, {"SourceFile": "Views\\Sales\\LoadFromForm.Designer.cs"}, {"SourceFile": "Views\\Sales\\POSForm.cs"}, {"SourceFile": "Views\\Sales\\POSForm.Designer.cs"}, {"SourceFile": "Views\\Sales\\SalesOrderView.cs"}, {"SourceFile": "Views\\Sales\\SalesOrderView.Designer.cs"}, {"SourceFile": "Views\\Sales\\SalesPriceOfferView.cs"}, {"SourceFile": "Views\\Sales\\SalesPriceOfferView.Designer.cs"}, {"SourceFile": "Views\\SelectSalesInvoiceLayout.cs"}, {"SourceFile": "Views\\SelectSalesInvoiceLayout.Designer.cs"}, {"SourceFile": "Views\\SimilarProductsSelectView.cs"}, {"SourceFile": "Views\\SimilarProductsSelectView.Designer.cs"}, {"SourceFile": "Views\\StockBalanceCorrectionListView.cs"}, {"SourceFile": "Views\\StockBalanceCorrectionListView.Designer.cs"}, {"SourceFile": "Views\\StockBalanceCorrectionView.cs"}, {"SourceFile": "Views\\StockBalanceCorrectionView.Designer.cs"}, {"SourceFile": "Views\\StockTransferBillForm.cs"}, {"SourceFile": "Views\\StockTransferBillForm.Designer.cs"}, {"SourceFile": "Views\\StockTransferBillsListView.cs"}, {"SourceFile": "Views\\StockTransferBillsListView.Designer.cs"}, {"SourceFile": "Views\\StoresView.cs"}, {"SourceFile": "Views\\StoresView.Designer.cs"}, {"SourceFile": "Views\\SystemSettingsForm.cs"}, {"SourceFile": "Views\\SystemSettingsForm.Designer.cs"}, {"SourceFile": "Views\\UserAccessProfileListView.cs"}, {"SourceFile": "Views\\UserAccessProfileListView.Designer.cs"}, {"SourceFile": "Views\\UserAccessProfileView.cs"}, {"SourceFile": "Views\\UserAccessProfileView.Designer.cs"}, {"SourceFile": "Views\\UserListView.cs"}, {"SourceFile": "Views\\UserListView.Designer.cs"}, {"SourceFile": "Views\\UserLogView.cs"}, {"SourceFile": "Views\\UserLogView.Designer.cs"}, {"SourceFile": "Views\\UserSettingsProfileListView.cs"}, {"SourceFile": "Views\\UserSettingsProfileListView.Designer.cs"}, {"SourceFile": "Views\\UserSettingsProfileView.cs"}, {"SourceFile": "Views\\UserSettingsProfileView.Designer.cs"}, {"SourceFile": "Views\\UserView.cs"}, {"SourceFile": "Views\\UserView.Designer.cs"}, {"SourceFile": "Views\\VendorInvoiceListView.cs"}, {"SourceFile": "Views\\VendorInvoiceListView.Designer.cs"}, {"SourceFile": "Views\\VendorInvoiceView.cs"}, {"SourceFile": "Views\\VendorInvoiceView.Designer.cs"}, {"SourceFile": "Views\\VendorsForm.cs"}, {"SourceFile": "Views\\VendorsForm.Designer.cs"}, {"SourceFile": "Views\\VendorsGroupsForm.cs"}, {"SourceFile": "Views\\VendorsGroupsForm.Designer.cs"}, {"SourceFile": "XtraForm1.cs"}, {"SourceFile": "XtraForm1.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\BCrypt.Net-Next.4.0.3\\lib\\net472\\BCrypt.Net-Next.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.BonusSkins.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Charts.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.CodeParser.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Data.Desktop.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Data.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.DataAccess.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.DataAccess.v23.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Diagram.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Dialogs.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Drawing.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Images.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Office.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Pdf.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Pdf.v23.1.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.PivotGrid.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Printing.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.RichEdit.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.RichEdit.v23.1.Export.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Sparkline.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Utils.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Utils.v23.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.Xpo.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraBars.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v23.1.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v23.1.UI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraCharts.v23.1.Wizard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraDiagram.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraDialogs.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraEditors.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraGauges.v23.1.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraGrid.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraLayout.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraNavBar.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraPivotGrid.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraPrinting.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraReports.v23.1.CodeCompletion.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraReports.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraReports.v23.1.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraRichEdit.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraTreeList.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\DevExpress 23.1\\Components\\Bin\\Framework\\DevExpress.XtraVerticalGrid.v23.1.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\EasyStock.Common\\bin\\Debug\\EasyStock.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\Hass\\EasyStock.Common\\bin\\Debug\\EasyStock.Common.dll"}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\EasyStock.HR\\bin\\Debug\\EasyStock.HR.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\Users\\<USER>\\Desktop\\Hass\\EasyStock.HR\\bin\\Debug\\EasyStock.HR.dll"}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\EntityFramework.6.5.1\\lib\\net45\\EntityFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\EntityFramework.6.5.1\\lib\\net45\\EntityFramework.SqlServer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\protobuf-net.Core.3.2.30\\lib\\net462\\protobuf-net.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\protobuf-net.3.2.30\\lib\\net462\\protobuf-net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\System.Collections.Immutable.7.0.0\\lib\\net462\\System.Collections.Immutable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\System.Memory.4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\System.Numerics.Vectors.4.5.0\\lib\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\System.Runtime.CompilerServices.Unsafe.6.0.0\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Users\\<USER>\\Desktop\\Hass\\packages\\Vip.Notification.1.0.4\\lib\\net45\\Vip.Notification.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\Users\\<USER>\\Desktop\\Hass\\EasyStock\\bin\\Debug\\EasyStock.exe", "OutputItemRelativePath": "EasyStock.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}