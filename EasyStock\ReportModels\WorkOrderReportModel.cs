﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class WorkOrderReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Numéro", GroupName = "Données de l'ordre de travail")]
        public int ID { get; set; }

        [Display(Name = "Code", GroupName = "Données de l'ordre de travail")]
        public int Code { get; set; }

        [Display(Name = "Date de début", GroupName = "Données de l'ordre de travail")]
        public DateTime StartDate { get; set; }

        [Display(Name = "Date de fin", GroupName = "Données de l'ordre de travail")]
        public DateTime? EndDate { get; set; }

        [Display(Name = "Centre de coût", GroupName = "Données de l'ordre de travail")]
        public string CostCenter { get; set; }

        [Display(Name = "Remarques", GroupName = "Données de l'ordre de travail")]
        public string Notes { get; set; }

        [Display(Name = "Quantité demandée", GroupName = "Valeur")]
        public double TotalorderQty { get; set; }

        [Display(Name = "Quantité réelle", GroupName = "Valeur")]
        public double TotalAcualQty { get; set; }

        [Display(Name = "Coût prévu", GroupName = "Valeur")]
        public double TotalDefaultCost { get; set; }

        [Display(Name = "Coût réel", GroupName = "Valeur")]
        public double TotalActualCost { get; set; }

        [Display(Name = "Coût unitaire", GroupName = "Valeur")]
        public double UnitCost { get; set; }

        [Display(Name = "Articles")]
        public ICollection<Details> Details { get; set; }

        public ICollection<Consumptions> Consumptions { get; set; }
    }
}
