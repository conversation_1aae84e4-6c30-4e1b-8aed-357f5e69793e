﻿using System;

namespace EasyStock.ReportModels.IncomStatment
{
	public class IncomeStatmentModelDetial
	{
		public string Account { get; set; }

		public double Debit { get; set; }

		public double Credit { get; set; }

		public void CalculateBalance()
		{
			double sum = this.Debit - this.Credit;
			this.Debit = (this.Credit = 0.0);
			bool flag = sum < 0.0;
			if (flag)
			{
				this.Credit = Math.Abs(sum);
			}
			else
			{
				this.Debit = Math.Abs(sum);
			}
		}
	}
}
