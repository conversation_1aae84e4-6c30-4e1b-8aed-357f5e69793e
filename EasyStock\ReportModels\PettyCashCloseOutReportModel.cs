﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class PettyCashCloseOutReportModel : CompanyInfoReportModel
    {
        [Display(Name = "N°")]
        public int ID { get; set; }

        [Display(Name = "Date")]
        public DateTime CloseDate { get; set; }

        [Display(Name = "Nom de la caisse")]
        public string PettycashName { get; set; }

        [Display(Name = "Type de clôture")]
        public CloseAccountType CloseType { get; set; }

        [Display(Name = "Valeur de la caisse")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double Amount { get; set; }

        [Display(Name = "Dépenses totales")]
        public double AmountSpent { get; set; }

        [Display(Name = "Montant restant")]
        public double RemainigAmount
        {
            get
            {
                return this.Amount - this.AmountSpent;
            }
        }

        [Display(Name = "Nom du compte")]
        public string AccountName { get; set; }
    }
}
