﻿using EasyStock.MainViews;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class ProductInOutBalance : XtraReportForm
    {
        public ProductInOutBalance() : base(new ReportFilter[]
        {
            ReportFilter.Store,
            ReportFilter.Product,
            ReportFilter.Date
        })
        {
            this.InitializeComponent();
            base.FiltersForm.Stores.MultiSelect = true;
            base.FiltersForm.Products.MultiSelect = true;
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                ProductInOutBalanceReport rpt = new ProductInOutBalanceReport();
                rpt.objectDataSource1.DataSource = new CompanyInfoReportModel();
                rpt.SetCompanyInfo(rpt.objectDataSource1.DataSource as CompanyInfoReportModel);
                ProductInOutBalanceModel.Filter filter = new ProductInOutBalanceModel.Filter();
                int[] selectedStores = base.FiltersForm.SelectedStores;
                filter.brancheIDs = ((selectedStores != null) ? selectedStores.ToList<int>() : null);
                int[] selectedProducts = base.FiltersForm.SelectedProducts;
                filter.productIDs = ((selectedProducts != null) ? selectedProducts.ToList<int>() : null);
                filter.fromDate = base.FiltersForm.startDate;
                filter.toDate = base.FiltersForm.endtDate;
                ICollection<ProductInOutBalanceModel> datasource = ProductInOutBalanceModel.GetProductInOut(filter);
                rpt.objectDataSource1.DataSource = datasource;
                rpt.SetCompanyInfo(datasource.FirstOrDefault<ProductInOutBalanceModel>());
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }
    }
}
