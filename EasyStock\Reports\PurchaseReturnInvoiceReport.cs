﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrinting.BarCode;
using DevExpress.XtraPrinting.Drawing;
using DevExpress.XtraReports;
using DevExpress.XtraReports.UI;
using EasyStock.Classes;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x0200039C RID: 924
	public class PurchaseReturnInvoiceReport : MasterReport
	{
		// Token: 0x060015A5 RID: 5541 RVA: 0x0000AF61 File Offset: 0x00009161
		public PurchaseReturnInvoiceReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x060015A6 RID: 5542 RVA: 0x001D8710 File Offset: 0x001D6910
		public static XtraReport GetReport(PurchaseReturnInvoiceReportModel ds)
		{
			PurchaseReturnInvoiceReport rpt = new PurchaseReturnInvoiceReport();
			rpt.LoadLayout();
			rpt.objectDataSource1.DataSource = ds;
			rpt.SetCompanyInfo(ds);
			rpt.CreateDocument();
			return rpt;
		}

		// Token: 0x060015A7 RID: 5543 RVA: 0x001D874C File Offset: 0x001D694C
		public static void Print(PurchaseReturnInvoiceReportModel ds)
		{
			XtraReport rpt = PurchaseReturnInvoiceReport.GetReport(ds);
			switch (CurrentSession.InvoicePrintMode)
			{
			case PrintMode.Direct:
				rpt.Print("");
				break;
			case PrintMode.ShowPreview:
				rpt.ShowPreview();
				break;
			case PrintMode.ShowDialog:
				rpt.PrintDialog();
				break;
			}
		}

		// Token: 0x060015A8 RID: 5544 RVA: 0x001D87A0 File Offset: 0x001D69A0
		public static void Print(ICollection<PurchaseReturnInvoiceReportModel> ds)
		{
			XtraReport report = new XtraReport();
			report.CreateDocument();
			report.Pages.Clear();
			foreach (PurchaseReturnInvoiceReportModel item in ds)
			{
				XtraReport rpt = PurchaseReturnInvoiceReport.GetReport(item);
				report.ModifyDocument(delegate(IDocumentModifier x)
				{
					x.AddPages(rpt.Pages);
				});
			}
			EasyStock.Reports.MasterReport.Print(report);
		}

		// Token: 0x060015A9 RID: 5545 RVA: 0x001D882C File Offset: 0x001D6A2C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060015AA RID: 5546 RVA: 0x001D8864 File Offset: 0x001D6A64
		private void InitializeComponent()
		{
			this.components = new Container();
			ComponentResourceManager resources = new ComponentResourceManager(typeof(PurchaseReturnInvoiceReport));
			Code128Generator code128Generator = new Code128Generator();
			Code128Generator code128Generator2 = new Code128Generator();
			this.Detail = new DetailBand();
			this.vendorTable = new XRTable();
			this.vendorNameRow = new XRTableRow();
			this.vendorName = new XRTableCell();
			this.vendorAddressRow = new XRTableRow();
			this.vendorAddress = new XRTableCell();
			this.vendorCityRow = new XRTableRow();
			this.vendorCity = new XRTableCell();
			this.xrTableRow3 = new XRTableRow();
			this.xrTableCell11 = new XRTableCell();
			this.xrTableRow4 = new XRTableRow();
			this.xrTableCell12 = new XRTableCell();
			this.vendorLogo = new XRPictureBox();
			this.invoiceInfoTable = new XRTable();
			this.invoiceDateRow = new XRTableRow();
			this.invoiceDateCaption = new XRTableCell();
			this.invoiceDate = new XRTableCell();
			this.invoiceNumberRow = new XRTableRow();
			this.invoiceNumberCaption = new XRTableCell();
			this.invoiceNumber = new XRTableCell();
			this.xrTableRow5 = new XRTableRow();
			this.xrTableCell15 = new XRTableCell();
			this.xrTableCell16 = new XRTableCell();
			this.xrTableCell17 = new XRTableCell();
			this.xrBarCode2 = new XRBarCode();
			this.customerTable = new XRTable();
			this.customerNameRow = new XRTableRow();
			this.customerName = new XRTableCell();
			this.invoiceLabel = new XRLabel();
			this.xrBarCode1 = new XRBarCode();
			this.detailTable = new XRTable();
			this.detailTableRow = new XRTableRow();
			this.productName = new XRTableCell();
			this.quantity = new XRTableCell();
			this.unitPrice = new XRTableCell();
			this.xrTableCell4 = new XRTableCell();
			this.xrTableCell3 = new XRTableCell();
			this.lineTotal = new XRTableCell();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.vendorContactsTable = new XRTable();
			this.vendorContactsRow = new XRTableRow();
			this.vendorWebsite = new XRTableCell();
			this.vendorEmail = new XRTableCell();
			this.vendorPhone = new XRTableCell();
			this.thankYouLabel = new XRLabel();
			this.heartLabel = new XRLabel();
			this.headerTable = new XRTable();
			this.headerTableRow = new XRTableRow();
			this.productNameCaption = new XRTableCell();
			this.quantityCaption = new XRTableCell();
			this.unitPriceCaption = new XRTableCell();
			this.xrTableCell2 = new XRTableCell();
			this.xrTableCell1 = new XRTableCell();
			this.lineTotalCaptionCell = new XRTableCell();
			this.baseControlStyle = new XRControlStyle();
			this.objectDataSource1 = new ObjectDataSource(this.components);
			this.DetailReport = new DetailReportBand();
			this.Detail1 = new DetailBand();
			this.GroupHeader3 = new GroupHeaderBand();
			this.GroupFooter2 = new GroupFooterBand();
			this.xrLine1 = new XRLine();
			this.xrControlStyle1 = new XRControlStyle();
			this.xrControlStyle2 = new XRControlStyle();
			this.GroupFooter1 = new GroupFooterBand();
			this.xrTable1 = new XRTable();
			this.xrTableRow1 = new XRTableRow();
			this.xrTableCell5 = new XRTableCell();
			this.xrTableCell6 = new XRTableCell();
			this.xrTableCell7 = new XRTableCell();
			this.xrTableCell13 = new XRTableCell();
			this.xrTableRow2 = new XRTableRow();
			this.xrTableCell8 = new XRTableCell();
			this.xrTableCell9 = new XRTableCell();
			this.xrTableCell10 = new XRTableCell();
			this.xrTableCell14 = new XRTableCell();
			this.summariesTable = new XRTable();
			this.totalCaptionRow = new XRTableRow();
			this.invoiceDueDateCaption = new XRTableCell();
			this.totalCaption = new XRTableCell();
			this.totalRow = new XRTableRow();
			this.invoiceDueDate = new XRTableCell();
			this.total = new XRTableCell();
			((ISupportInitialize)this.vendorTable).BeginInit();
			((ISupportInitialize)this.invoiceInfoTable).BeginInit();
			((ISupportInitialize)this.customerTable).BeginInit();
			((ISupportInitialize)this.detailTable).BeginInit();
			((ISupportInitialize)this.vendorContactsTable).BeginInit();
			((ISupportInitialize)this.headerTable).BeginInit();
			((ISupportInitialize)this.objectDataSource1).BeginInit();
			((ISupportInitialize)this.xrTable1).BeginInit();
			((ISupportInitialize)this.summariesTable).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.vendorTable,
				this.vendorLogo,
				this.invoiceInfoTable,
				this.customerTable,
				this.invoiceLabel
			});
			this.Detail.HeightF = 215f;
			this.Detail.KeepTogether = true;
			this.Detail.KeepTogetherWithDetailReports = true;
			this.Detail.Name = "Detail";
			this.Detail.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.Detail.StyleName = "baseControlStyle";
			this.Detail.TextAlignment = TextAlignment.TopLeft;
			this.vendorTable.LocationFloat = new PointFloat(417.8606f, 98.95827f);
			this.vendorTable.Name = "vendorTable";
			this.vendorTable.Rows.AddRange(new XRTableRow[]
			{
				this.vendorNameRow,
				this.vendorAddressRow,
				this.vendorCityRow,
				this.xrTableRow3,
				this.xrTableRow4
			});
			this.vendorTable.SizeF = new SizeF(209.1394f, 115.4168f);
			this.vendorTable.StylePriority.UseTextAlignment = false;
			this.vendorTable.TextAlignment = TextAlignment.TopCenter;
			this.vendorNameRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorName
			});
			this.vendorNameRow.Name = "vendorNameRow";
			this.vendorNameRow.Weight = 1.0;
			this.vendorName.CanShrink = true;
			this.vendorName.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyName]")
			});
			this.vendorName.Font = new Font("Arial", 14.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.vendorName.Name = "vendorName";
			this.vendorName.StylePriority.UseFont = false;
			this.vendorName.StylePriority.UsePadding = false;
			this.vendorName.Text = "VendorName";
			this.vendorName.Weight = 1.0;
			this.vendorAddressRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorAddress
			});
			this.vendorAddressRow.Name = "vendorAddressRow";
			this.vendorAddressRow.Weight = 1.0;
			this.vendorAddress.CanShrink = true;
			this.vendorAddress.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyAddress]")
			});
			this.vendorAddress.Name = "vendorAddress";
			this.vendorAddress.StylePriority.UseFont = false;
			this.vendorAddress.Text = "VendorAddress";
			this.vendorAddress.Weight = 1.0;
			this.vendorCityRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorCity
			});
			this.vendorCityRow.Name = "vendorCityRow";
			this.vendorCityRow.Weight = 1.0;
			this.vendorCity.CanShrink = true;
			this.vendorCity.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyCity]")
			});
			this.vendorCity.Name = "vendorCity";
			this.vendorCity.StylePriority.UseFont = false;
			this.vendorCity.Text = "VendorCity";
			this.vendorCity.Weight = 1.0;
			this.xrTableRow3.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell11
			});
			this.xrTableRow3.Name = "xrTableRow3";
			this.xrTableRow3.Weight = 1.0;
			this.xrTableCell11.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CommercialBook]")
			});
			this.xrTableCell11.Multiline = true;
			this.xrTableCell11.Name = "xrTableCell11";
			this.xrTableCell11.StylePriority.UseFont = false;
			this.xrTableCell11.Text = "xrTableCell11";
			this.xrTableCell11.Weight = 1.0;
			this.xrTableRow4.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell12
			});
			this.xrTableRow4.Name = "xrTableRow4";
			this.xrTableRow4.Weight = 1.0;
			this.xrTableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyTaxCard]")
			});
			this.xrTableCell12.Multiline = true;
			this.xrTableCell12.Name = "xrTableCell12";
			this.xrTableCell12.StylePriority.UseFont = false;
			this.xrTableCell12.Text = "xrTableCell12";
			this.xrTableCell12.Weight = 1.0;
			this.vendorLogo.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "ImageSource", "[CompanyLogo]")
			});
			this.vendorLogo.ImageAlignment = ImageAlignment.TopLeft;
			this.vendorLogo.ImageSource = new ImageSource("img", resources.GetString("vendorLogo.ImageSource"));
			this.vendorLogo.LocationFloat = new PointFloat(447.2501f, 0f);
			this.vendorLogo.Name = "vendorLogo";
			this.vendorLogo.SizeF = new SizeF(150.3605f, 98.95827f);
			this.vendorLogo.Sizing = ImageSizeMode.Squeeze;
			this.vendorLogo.StylePriority.UseBorders = false;
			this.vendorLogo.StylePriority.UsePadding = false;
			this.invoiceInfoTable.LocationFloat = new PointFloat(2.917542f, 129.1666f);
			this.invoiceInfoTable.Name = "invoiceInfoTable";
			this.invoiceInfoTable.Rows.AddRange(new XRTableRow[]
			{
				this.invoiceDateRow,
				this.invoiceNumberRow,
				this.xrTableRow5
			});
			this.invoiceInfoTable.SizeF = new SizeF(315.0421f, 75f);
			this.invoiceDateRow.Cells.AddRange(new XRTableCell[]
			{
				this.invoiceDateCaption,
				this.invoiceDate
			});
			this.invoiceDateRow.Name = "invoiceDateRow";
			this.invoiceDateRow.Weight = 1.0;
			this.invoiceDateCaption.CanShrink = true;
			this.invoiceDateCaption.Name = "invoiceDateCaption";
			this.invoiceDateCaption.StylePriority.UseFont = false;
			this.invoiceDateCaption.StylePriority.UsePadding = false;
			this.invoiceDateCaption.StylePriority.UseTextAlignment = false;
			this.invoiceDateCaption.Text = "Date de rédaction";
			this.invoiceDateCaption.TextAlignment = TextAlignment.TopLeft;
			this.invoiceDateCaption.Weight = 0.4965592917127513;
			this.invoiceDate.CanShrink = true;
			this.invoiceDate.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Date]")
			});
			this.invoiceDate.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.invoiceDate.Name = "invoiceDate";
			this.invoiceDate.StylePriority.UseFont = false;
			this.invoiceDate.TextFormatString = "{0:d MMMM yyyy}";
			this.invoiceDate.Weight = 1.3680312174875988;
			this.invoiceNumberRow.Cells.AddRange(new XRTableCell[]
			{
				this.invoiceNumberCaption,
				this.invoiceNumber
			});
			this.invoiceNumberRow.Name = "invoiceNumberRow";
			this.invoiceNumberRow.Weight = 1.0;
			this.invoiceNumberCaption.CanShrink = true;
			this.invoiceNumberCaption.Name = "invoiceNumberCaption";
			this.invoiceNumberCaption.StylePriority.UseFont = false;
			this.invoiceNumberCaption.StylePriority.UsePadding = false;
			this.invoiceNumberCaption.StylePriority.UseTextAlignment = false;
			this.invoiceNumberCaption.Text = "Numéro de retour";
			this.invoiceNumberCaption.TextAlignment = TextAlignment.TopLeft;
			this.invoiceNumberCaption.Weight = 0.4965592917127513;
			this.invoiceNumber.CanShrink = true;
			this.invoiceNumber.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Code]")
			});
			this.invoiceNumber.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.invoiceNumber.Name = "invoiceNumber";
			this.invoiceNumber.StylePriority.UseFont = false;
			this.invoiceNumber.Weight = 1.3680312174875988;
			this.xrTableRow5.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell15,
				this.xrTableCell16,
				this.xrTableCell17
			});
			this.xrTableRow5.Name = "xrTableRow5";
			this.xrTableRow5.Weight = 1.0;
			this.xrTableCell15.Multiline = true;
			this.xrTableCell15.Name = "xrTableCell15";
			this.xrTableCell15.StylePriority.UseFont = false;
			this.xrTableCell15.StylePriority.UsePadding = false;
			this.xrTableCell15.StylePriority.UseTextAlignment = false;
			this.xrTableCell15.Text = "Numéro de source";
			this.xrTableCell15.TextAlignment = TextAlignment.TopLeft;
			this.xrTableCell15.Weight = 0.4965592917127513;
			this.xrTableCell16.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[SourceInvoiceCode]")
			});
			this.xrTableCell16.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.xrTableCell16.Multiline = true;
			this.xrTableCell16.Name = "xrTableCell16";
			this.xrTableCell16.StylePriority.UseFont = false;
			this.xrTableCell16.Text = "xrTableCell16";
			this.xrTableCell16.Weight = 0.5145274868822254;
			this.xrTableCell17.Controls.AddRange(new XRControl[]
			{
				this.xrBarCode2
			});
			this.xrTableCell17.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.xrTableCell17.Multiline = true;
			this.xrTableCell17.Name = "xrTableCell17";
			this.xrTableCell17.StylePriority.UseFont = false;
			this.xrTableCell17.Text = "xrTableCell17";
			this.xrTableCell17.Weight = 0.8535037306053734;
			this.xrBarCode2.AutoModule = true;
			this.xrBarCode2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[SourceInvoiceID]")
			});
			this.xrBarCode2.LocationFloat = new PointFloat(3.051758E-05f, 0f);
			this.xrBarCode2.Name = "xrBarCode2";
			this.xrBarCode2.Padding = new PaddingInfo(10, 10, 0, 0, 96f);
			this.xrBarCode2.ShowText = false;
			this.xrBarCode2.SizeF = new SizeF(142.2359f, 25.00002f);
			this.xrBarCode2.Symbology = code128Generator;
			this.customerTable.LocationFloat = new PointFloat(2.917542f, 90.00002f);
			this.customerTable.Name = "customerTable";
			this.customerTable.Rows.AddRange(new XRTableRow[]
			{
				this.customerNameRow
			});
			this.customerTable.SizeF = new SizeF(201.0514f, 25f);
			this.customerNameRow.Cells.AddRange(new XRTableCell[]
			{
				this.customerName
			});
			this.customerNameRow.Name = "customerNameRow";
			this.customerNameRow.Weight = 1.0;
			this.customerName.CanShrink = true;
			this.customerName.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[VendorName]")
			});
			this.customerName.Font = new Font("Segoe UI", 14f);
			this.customerName.Name = "customerName";
			this.customerName.StylePriority.UseFont = false;
			this.customerName.StylePriority.UsePadding = false;
			this.customerName.Text = "VendorName";
			this.customerName.Weight = 1.191547728468558;
			this.invoiceLabel.Font = new Font("Segoe UI", 26.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.invoiceLabel.LocationFloat = new PointFloat(2.91803f, 9.999974f);
			this.invoiceLabel.Name = "invoiceLabel";
			this.invoiceLabel.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.invoiceLabel.SizeF = new SizeF(289.0834f, 50f);
			this.invoiceLabel.StylePriority.UseFont = false;
			this.invoiceLabel.Text = "Facture de Retour d'Achat";
			this.xrBarCode1.AutoModule = true;
			this.xrBarCode1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ID]")
			});
			this.xrBarCode1.LocationFloat = new PointFloat(398.7736f, 0f);
			this.xrBarCode1.Name = "xrBarCode1";
			this.xrBarCode1.Padding = new PaddingInfo(10, 10, 0, 0, 100f);
			this.xrBarCode1.SizeF = new SizeF(231.1432f, 35.83344f);
			this.xrBarCode1.Symbology = code128Generator2;
			this.detailTable.EvenStyleName = "xrControlStyle2";
			this.detailTable.Font = new Font("Segoe UI", 9.75f, FontStyle.Bold);
			this.detailTable.LocationFloat = new PointFloat(0f, 0f);
			this.detailTable.Name = "detailTable";
			this.detailTable.OddStyleName = "xrControlStyle1";
			this.detailTable.Padding = new PaddingInfo(2, 2, 5, 0, 100f);
			this.detailTable.Rows.AddRange(new XRTableRow[]
			{
				this.detailTableRow
			});
			this.detailTable.SizeF = new SizeF(626.9167f, 35f);
			this.detailTable.StylePriority.UseFont = false;
			this.detailTable.StylePriority.UsePadding = false;
			this.detailTableRow.Cells.AddRange(new XRTableCell[]
			{
				this.productName,
				this.quantity,
				this.unitPrice,
				this.xrTableCell4,
				this.xrTableCell3,
				this.lineTotal
			});
			this.detailTableRow.Name = "detailTableRow";
			this.detailTableRow.Weight = 12.343333333333334;
			this.productName.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ProductName]")
			});
			this.productName.Name = "productName";
			this.productName.StylePriority.UsePadding = false;
			this.productName.Text = "ProductName";
			this.productName.Weight = 1.1251227066148979;
			this.quantity.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Details].[Quantity]")
			});
			this.quantity.Name = "quantity";
			this.quantity.StylePriority.UsePadding = false;
			this.quantity.StylePriority.UseTextAlignment = false;
			this.quantity.Text = "1";
			this.quantity.TextAlignment = TextAlignment.TopCenter;
			this.quantity.Weight = 0.36192910244760756;
			this.unitPrice.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Details].[Price]")
			});
			this.unitPrice.Name = "unitPrice";
			this.unitPrice.StylePriority.UsePadding = false;
			this.unitPrice.StylePriority.UseTextAlignment = false;
			this.unitPrice.Text = "$0.00";
			this.unitPrice.TextAlignment = TextAlignment.TopCenter;
			this.unitPrice.TextFormatString = "{0:0.##}";
			this.unitPrice.Weight = 0.43550145953603214;
			this.xrTableCell4.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Details].[Tax]")
			});
			this.xrTableCell4.ForeColor = Color.DarkGray;
			this.xrTableCell4.Multiline = true;
			this.xrTableCell4.Name = "xrTableCell4";
			this.xrTableCell4.StylePriority.UseFont = false;
			this.xrTableCell4.StylePriority.UseForeColor = false;
			this.xrTableCell4.StylePriority.UsePadding = false;
			this.xrTableCell4.StylePriority.UseTextAlignment = false;
			this.xrTableCell4.Text = "xrTableCell4";
			this.xrTableCell4.TextAlignment = TextAlignment.TopCenter;
			this.xrTableCell4.TextFormatString = "{0:0.##}";
			this.xrTableCell4.Weight = 0.43035328838674686;
			this.xrTableCell3.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Details].[Discount]")
			});
			this.xrTableCell3.ForeColor = Color.DarkGray;
			this.xrTableCell3.Multiline = true;
			this.xrTableCell3.Name = "xrTableCell3";
			this.xrTableCell3.StylePriority.UseFont = false;
			this.xrTableCell3.StylePriority.UseForeColor = false;
			this.xrTableCell3.StylePriority.UsePadding = false;
			this.xrTableCell3.StylePriority.UseTextAlignment = false;
			this.xrTableCell3.Text = "xrTableCell3";
			this.xrTableCell3.TextAlignment = TextAlignment.TopCenter;
			this.xrTableCell3.TextFormatString = "{0:0.##}";
			this.xrTableCell3.Weight = 0.43854565364965686;
			this.lineTotal.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Details].[Net]")
			});
			this.lineTotal.ForeColor = Color.FromArgb(253, 102, 78);
			this.lineTotal.Name = "lineTotal";
			this.lineTotal.StylePriority.UseFont = false;
			this.lineTotal.StylePriority.UseForeColor = false;
			this.lineTotal.StylePriority.UsePadding = false;
			this.lineTotal.StylePriority.UseTextAlignment = false;
			this.lineTotal.Text = "$0.00";
			this.lineTotal.TextAlignment = TextAlignment.TopRight;
			this.lineTotal.TextFormatString = "{0:0.##}";
			this.lineTotal.Weight = 0.6308478107597254;
			this.TopMargin.Name = "TopMargin";
			this.TopMargin.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.TopMargin.StylePriority.UseBackColor = false;
			this.TopMargin.TextAlignment = TextAlignment.TopLeft;
			this.BottomMargin.Controls.AddRange(new XRControl[]
			{
				this.xrBarCode1,
				this.vendorContactsTable,
				this.thankYouLabel,
				this.heartLabel
			});
			this.BottomMargin.Name = "BottomMargin";
			this.BottomMargin.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.BottomMargin.TextAlignment = TextAlignment.TopLeft;
			this.vendorContactsTable.BorderColor = Color.FromArgb(213, 211, 205);
			this.vendorContactsTable.Font = new Font("Segoe UI", 7.75f);
			this.vendorContactsTable.LocationFloat = new PointFloat(248.0009f, 35.83344f);
			this.vendorContactsTable.Name = "vendorContactsTable";
			this.vendorContactsTable.Rows.AddRange(new XRTableRow[]
			{
				this.vendorContactsRow
			});
			this.vendorContactsTable.SizeF = new SizeF(378.9991f, 15f);
			this.vendorContactsTable.StylePriority.UseBorderColor = false;
			this.vendorContactsTable.StylePriority.UseFont = false;
			this.vendorContactsRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorWebsite,
				this.vendorEmail,
				this.vendorPhone
			});
			this.vendorContactsRow.Name = "vendorContactsRow";
			this.vendorContactsRow.Weight = 1.0;
			this.vendorWebsite.CanGrow = false;
			this.vendorWebsite.CanShrink = true;
			this.vendorWebsite.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Phone]")
			});
			this.vendorWebsite.Name = "vendorWebsite";
			this.vendorWebsite.StylePriority.UseTextAlignment = false;
			this.vendorWebsite.Text = "VendorWebsite";
			this.vendorWebsite.TextAlignment = TextAlignment.MiddleLeft;
			this.vendorWebsite.Weight = 1.0;
			this.vendorEmail.CanGrow = false;
			this.vendorEmail.CanShrink = true;
			this.vendorEmail.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[City]")
			});
			this.vendorEmail.Name = "vendorEmail";
			this.vendorEmail.StylePriority.UseBorders = false;
			this.vendorEmail.StylePriority.UseTextAlignment = false;
			this.vendorEmail.Text = "VendorEmail";
			this.vendorEmail.TextAlignment = TextAlignment.MiddleCenter;
			this.vendorEmail.Weight = 1.0;
			this.vendorPhone.CanGrow = false;
			this.vendorPhone.CanShrink = true;
			this.vendorPhone.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Address]")
			});
			this.vendorPhone.Name = "vendorPhone";
			this.vendorPhone.StylePriority.UseBorders = false;
			this.vendorPhone.StylePriority.UseTextAlignment = false;
			this.vendorPhone.Text = "VendorPhone";
			this.vendorPhone.TextAlignment = TextAlignment.MiddleRight;
			this.vendorPhone.Weight = 1.0;
			this.thankYouLabel.CanGrow = false;
			this.thankYouLabel.Font = new Font("Segoe UI", 14f);
			this.thankYouLabel.LocationFloat = new PointFloat(39.54291f, 9.999974f);
			this.thankYouLabel.Name = "thankYouLabel";
			this.thankYouLabel.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.thankYouLabel.SizeF = new SizeF(134.2917f, 40f);
			this.thankYouLabel.StylePriority.UseFont = false;
			this.thankYouLabel.StylePriority.UseTextAlignment = false;
			this.thankYouLabel.Text = "Merci de votre visite";
			this.thankYouLabel.TextAlignment = TextAlignment.MiddleLeft;
			this.heartLabel.CanGrow = false;
			this.heartLabel.Font = new Font("Segoe UI", 24f);
			this.heartLabel.ForeColor = Color.FromArgb(253, 102, 78);
			this.heartLabel.LocationFloat = new PointFloat(0f, 9.999974f);
			this.heartLabel.Name = "heartLabel";
			this.heartLabel.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.heartLabel.SizeF = new SizeF(39.54286f, 40f);
			this.heartLabel.StylePriority.UseFont = false;
			this.heartLabel.StylePriority.UseForeColor = false;
			this.heartLabel.StylePriority.UseTextAlignment = false;
			this.heartLabel.Text = "♥";
			this.heartLabel.TextAlignment = TextAlignment.MiddleLeft;
			this.headerTable.BackColor = Color.LightGray;
			this.headerTable.BorderColor = Color.FromArgb(203, 201, 194);
			this.headerTable.Borders = BorderSide.Bottom;
			this.headerTable.Font = new Font("Segoe UI", 10f, FontStyle.Bold);
			this.headerTable.ForeColor = Color.Black;
			this.headerTable.LocationFloat = new PointFloat(0.08422852f, 9.999974f);
			this.headerTable.Name = "headerTable";
			this.headerTable.Padding = new PaddingInfo(2, 2, 5, 0, 100f);
			this.headerTable.Rows.AddRange(new XRTableRow[]
			{
				this.headerTableRow
			});
			this.headerTable.SizeF = new SizeF(626.9158f, 32f);
			this.headerTable.StylePriority.UseBackColor = false;
			this.headerTable.StylePriority.UseBorderColor = false;
			this.headerTable.StylePriority.UseBorders = false;
			this.headerTable.StylePriority.UseFont = false;
			this.headerTable.StylePriority.UseForeColor = false;
			this.headerTable.StylePriority.UsePadding = false;
			this.headerTableRow.Cells.AddRange(new XRTableCell[]
			{
				this.productNameCaption,
				this.quantityCaption,
				this.unitPriceCaption,
				this.xrTableCell2,
				this.xrTableCell1,
				this.lineTotalCaptionCell
			});
			this.headerTableRow.Name = "headerTableRow";
			this.headerTableRow.Weight = 11.5;
			this.productNameCaption.Name = "productNameCaption";
			this.productNameCaption.StylePriority.UsePadding = false;
			this.productNameCaption.Text = "Nom de l'article";
			this.productNameCaption.Weight = 1.1092725257288043;
			this.quantityCaption.Name = "quantityCaption";
			this.quantityCaption.StylePriority.UseTextAlignment = false;
			this.quantityCaption.Text = "Quantité";
			this.quantityCaption.TextAlignment = TextAlignment.TopCenter;
			this.quantityCaption.Weight = 0.3568074608582966;
			this.unitPriceCaption.Name = "unitPriceCaption";
			this.unitPriceCaption.StylePriority.UseTextAlignment = false;
			this.unitPriceCaption.Text = "Prix";
			this.unitPriceCaption.TextAlignment = TextAlignment.TopCenter;
			this.unitPriceCaption.Weight = 0.429381446953317;
			this.xrTableCell2.Multiline = true;
			this.xrTableCell2.Name = "xrTableCell2";
			this.xrTableCell2.StylePriority.UseTextAlignment = false;
			this.xrTableCell2.Text = "TVA";
			this.xrTableCell2.TextAlignment = TextAlignment.TopCenter;
			this.xrTableCell2.Weight = 0.42527373186330036;
			this.xrTableCell1.Multiline = true;
			this.xrTableCell1.Name = "xrTableCell1";
			this.xrTableCell1.StylePriority.UseTextAlignment = false;
			this.xrTableCell1.Text = "Remise";
			this.xrTableCell1.TextAlignment = TextAlignment.TopCenter;
			this.xrTableCell1.Weight = 0.4320170969959557;
			this.lineTotalCaptionCell.Name = "lineTotalCaptionCell";
			this.lineTotalCaptionCell.StylePriority.UseTextAlignment = false;
			this.lineTotalCaptionCell.Text = "Total TTC";
			this.lineTotalCaptionCell.TextAlignment = TextAlignment.TopRight;
			this.lineTotalCaptionCell.Weight = 0.6220973707789514;
			this.baseControlStyle.Font = new Font("Segoe UI", 9.75f);
			this.baseControlStyle.Name = "baseControlStyle";
			this.baseControlStyle.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.objectDataSource1.DataSource = typeof(PurchaseReturnInvoiceReportModel);
			this.objectDataSource1.Name = "objectDataSource1";
			this.DetailReport.Bands.AddRange(new Band[]
			{
				this.Detail1,
				this.GroupHeader3,
				this.GroupFooter2
			});
			this.DetailReport.DataMember = "Details";
			this.DetailReport.DataSource = this.objectDataSource1;
			this.DetailReport.Level = 0;
			this.DetailReport.Name = "DetailReport";
			this.DetailReport.ReportPrintOptions.DetailCountAtDesignTime = 5;
			this.Detail1.Controls.AddRange(new XRControl[]
			{
				this.detailTable
			});
			this.Detail1.HeightF = 35f;
			this.Detail1.Name = "Detail1";
			this.GroupHeader3.Controls.AddRange(new XRControl[]
			{
				this.headerTable
			});
			this.GroupHeader3.HeightF = 41.99997f;
			this.GroupHeader3.Name = "GroupHeader3";
			this.GroupFooter2.Controls.AddRange(new XRControl[]
			{
				this.xrLine1
			});
			this.GroupFooter2.HeightF = 26.12686f;
			this.GroupFooter2.Name = "GroupFooter2";
			this.xrLine1.LocationFloat = new PointFloat(4.083308f, 4.875056f);
			this.xrLine1.Name = "xrLine1";
			this.xrLine1.SizeF = new SizeF(619.7917f, 9.375f);
			this.xrControlStyle1.BackColor = Color.LightYellow;
			this.xrControlStyle1.Name = "xrControlStyle1";
			this.xrControlStyle1.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.xrControlStyle2.Name = "xrControlStyle2";
			this.xrControlStyle2.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.GroupFooter1.Controls.AddRange(new XRControl[]
			{
				this.xrTable1,
				this.summariesTable
			});
			this.GroupFooter1.GroupUnion = GroupFooterUnion.WithLastDetail;
			this.GroupFooter1.HeightF = 198.0434f;
			this.GroupFooter1.KeepTogether = true;
			this.GroupFooter1.Name = "GroupFooter1";
			this.GroupFooter1.PageBreak = PageBreak.AfterBand;
			this.GroupFooter1.PrintAtBottom = true;
			this.xrTable1.Borders = BorderSide.All;
			this.xrTable1.LocationFloat = new PointFloat(2.834213f, 0f);
			this.xrTable1.Name = "xrTable1";
			this.xrTable1.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable1.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow1,
				this.xrTableRow2
			});
			this.xrTable1.SizeF = new SizeF(626.4727f, 50f);
			this.xrTable1.StylePriority.UseBorders = false;
			this.xrTable1.StylePriority.UseTextAlignment = false;
			this.xrTable1.TextAlignment = TextAlignment.TopCenter;
			this.xrTableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell5,
				this.xrTableCell6,
				this.xrTableCell7,
				this.xrTableCell13
			});
			this.xrTableRow1.Name = "xrTableRow1";
			this.xrTableRow1.Weight = 1.0;
			this.xrTableCell5.BackColor = Color.LightGray;
			this.xrTableCell5.Multiline = true;
			this.xrTableCell5.Name = "xrTableCell5";
			this.xrTableCell5.StylePriority.UseBackColor = false;
			this.xrTableCell5.Text = "Autres frais";
			this.xrTableCell5.Weight = 1.0;
			this.xrTableCell6.BackColor = Color.LightGray;
			this.xrTableCell6.Multiline = true;
			this.xrTableCell6.Name = "xrTableCell6";
			this.xrTableCell6.StylePriority.UseBackColor = false;
			this.xrTableCell6.Text = "Remise";
			this.xrTableCell6.Weight = 1.0;
			this.xrTableCell7.BackColor = Color.LightGray;
			this.xrTableCell7.Multiline = true;
			this.xrTableCell7.Name = "xrTableCell7";
			this.xrTableCell7.StylePriority.UseBackColor = false;
			this.xrTableCell7.Text = "TVA";
			this.xrTableCell7.Weight = 1.0;
			this.xrTableCell13.BackColor = Color.LightGray;
			this.xrTableCell13.Multiline = true;
			this.xrTableCell13.Name = "xrTableCell13";
			this.xrTableCell13.StylePriority.UseBackColor = false;
			this.xrTableCell13.Text = "Payé";
			this.xrTableCell13.Weight = 1.0;
			this.xrTableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell8,
				this.xrTableCell9,
				this.xrTableCell10,
				this.xrTableCell14
			});
			this.xrTableRow2.Name = "xrTableRow2";
			this.xrTableRow2.Weight = 1.0;
			this.xrTableCell8.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[OtherExpenses]")
			});
			this.xrTableCell8.Multiline = true;
			this.xrTableCell8.Name = "xrTableCell8";
			this.xrTableCell8.Text = "xrTableCell8";
			this.xrTableCell8.TextFormatString = "{0:0.##}";
			this.xrTableCell8.Weight = 1.0;
			this.xrTableCell9.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Discount]")
			});
			this.xrTableCell9.Multiline = true;
			this.xrTableCell9.Name = "xrTableCell9";
			this.xrTableCell9.Text = "xrTableCell9";
			this.xrTableCell9.TextFormatString = "{0:0.##}";
			this.xrTableCell9.Weight = 1.0;
			this.xrTableCell10.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Tax]")
			});
			this.xrTableCell10.Multiline = true;
			this.xrTableCell10.Name = "xrTableCell10";
			this.xrTableCell10.Text = "xrTableCell10";
			this.xrTableCell10.TextFormatString = "{0:0.##}";
			this.xrTableCell10.Weight = 1.0;
			this.xrTableCell14.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Paid]")
			});
			this.xrTableCell14.Multiline = true;
			this.xrTableCell14.Name = "xrTableCell14";
			this.xrTableCell14.Text = "xrTableCell14";
			this.xrTableCell14.TextFormatString = "{0:0.##}";
			this.xrTableCell14.Weight = 1.0;
			this.summariesTable.BorderColor = Color.FromArgb(203, 201, 194);
			this.summariesTable.Borders = BorderSide.Bottom;
			this.summariesTable.ForeColor = Color.FromArgb(90, 86, 85);
			this.summariesTable.LocationFloat = new PointFloat(2.47435f, 53.04337f);
			this.summariesTable.Name = "summariesTable";
			this.summariesTable.Rows.AddRange(new XRTableRow[]
			{
				this.totalCaptionRow,
				this.totalRow
			});
			this.summariesTable.SizeF = new SizeF(626.8325f, 145f);
			this.summariesTable.StylePriority.UseBorderColor = false;
			this.summariesTable.StylePriority.UseBorders = false;
			this.summariesTable.StylePriority.UseForeColor = false;
			this.totalCaptionRow.Cells.AddRange(new XRTableCell[]
			{
				this.invoiceDueDateCaption,
				this.totalCaption
			});
			this.totalCaptionRow.Name = "totalCaptionRow";
			this.totalCaptionRow.Weight = 1.0;
			this.invoiceDueDateCaption.BackColor = Color.LightGray;
			this.invoiceDueDateCaption.Font = new Font("Segoe UI", 10f, FontStyle.Bold);
			this.invoiceDueDateCaption.Name = "invoiceDueDateCaption";
			this.invoiceDueDateCaption.StylePriority.UseBackColor = false;
			this.invoiceDueDateCaption.StylePriority.UseFont = false;
			this.invoiceDueDateCaption.StylePriority.UseForeColor = false;
			this.invoiceDueDateCaption.StylePriority.UseTextAlignment = false;
			this.invoiceDueDateCaption.TextAlignment = TextAlignment.MiddleCenter;
			this.invoiceDueDateCaption.Weight = 1.4499949651285395;
			this.totalCaption.BackColor = Color.LightGray;
			this.totalCaption.Font = new Font("Segoe UI", 10f, FontStyle.Bold);
			this.totalCaption.Name = "totalCaption";
			this.totalCaption.StylePriority.UseBackColor = false;
			this.totalCaption.StylePriority.UseFont = false;
			this.totalCaption.StylePriority.UseForeColor = false;
			this.totalCaption.StylePriority.UseTextAlignment = false;
			this.totalCaption.Text = "Total TTC";
			this.totalCaption.TextAlignment = TextAlignment.MiddleCenter;
			this.totalCaption.TextFormatString = "{0:$0.00}";
			this.totalCaption.Weight = 0.8639557572333815;
			this.totalRow.Cells.AddRange(new XRTableCell[]
			{
				this.invoiceDueDate,
				this.total
			});
			this.totalRow.Name = "totalRow";
			this.totalRow.Weight = 3.600000058492028;
			this.invoiceDueDate.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[NetText]")
			});
			this.invoiceDueDate.Font = new Font("Segoe UI", 26f);
			this.invoiceDueDate.ForeColor = Color.Black;
			this.invoiceDueDate.Name = "invoiceDueDate";
			this.invoiceDueDate.StylePriority.UseFont = false;
			this.invoiceDueDate.StylePriority.UseForeColor = false;
			this.invoiceDueDate.StylePriority.UseTextAlignment = false;
			this.invoiceDueDate.TextAlignment = TextAlignment.MiddleLeft;
			this.invoiceDueDate.TextFormatString = "{0:d MMMM, yyyy}";
			this.invoiceDueDate.Weight = 1.4496870211403086;
			this.total.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Net]")
			});
			this.total.Font = new Font("Segoe UI", 26f, FontStyle.Bold);
			this.total.ForeColor = Color.FromArgb(253, 102, 78);
			this.total.Name = "total";
			this.total.StylePriority.UseFont = false;
			this.total.StylePriority.UseForeColor = false;
			this.total.StylePriority.UseTextAlignment = false;
			this.total.Text = "$0.00";
			this.total.TextAlignment = TextAlignment.MiddleRight;
			this.total.TextFormatString = "{0:0.##}";
			this.total.Weight = 0.8642637012216126;
			base.Bands.AddRange(new Band[]
			{
				this.Detail,
				this.TopMargin,
				this.BottomMargin,
				this.DetailReport,
				this.GroupFooter1
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource1
			});
			base.DataSource = this.objectDataSource1;
			this.Font = new Font("Arial", 9.75f);
			base.Margins = new Margins(97, 100, 100, 100);
			base.PageHeight = 1169;
			base.PageWidth = 827;
			base.PaperKind = DevExpress.Drawing.Printing.DXPaperKind.A4;
			this.RightToLeft = RightToLeft.Yes;
			base.RightToLeftLayout = RightToLeftLayout.Yes;
			base.StyleSheet.AddRange(new XRControlStyle[]
			{
				this.baseControlStyle,
				this.xrControlStyle1,
				this.xrControlStyle2
			});
			base.Version = "20.1";
			((ISupportInitialize)this.vendorTable).EndInit();
			((ISupportInitialize)this.invoiceInfoTable).EndInit();
			((ISupportInitialize)this.customerTable).EndInit();
			((ISupportInitialize)this.detailTable).EndInit();
			((ISupportInitialize)this.vendorContactsTable).EndInit();
			((ISupportInitialize)this.headerTable).EndInit();
			((ISupportInitialize)this.objectDataSource1).EndInit();
			((ISupportInitialize)this.xrTable1).EndInit();
			((ISupportInitialize)this.summariesTable).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x0400238A RID: 9098
		private IContainer components = null;

		// Token: 0x0400238B RID: 9099
		private DetailBand Detail;

		// Token: 0x0400238C RID: 9100
		private XRTable detailTable;

		// Token: 0x0400238D RID: 9101
		private XRTableRow detailTableRow;

		// Token: 0x0400238E RID: 9102
		private XRTableCell productName;

		// Token: 0x0400238F RID: 9103
		private XRTableCell quantity;

		// Token: 0x04002390 RID: 9104
		private XRTableCell unitPrice;

		// Token: 0x04002391 RID: 9105
		private XRTableCell lineTotal;

		// Token: 0x04002392 RID: 9106
		private TopMarginBand TopMargin;

		// Token: 0x04002393 RID: 9107
		private BottomMarginBand BottomMargin;

		// Token: 0x04002394 RID: 9108
		private XRTable vendorContactsTable;

		// Token: 0x04002395 RID: 9109
		private XRTableRow vendorContactsRow;

		// Token: 0x04002396 RID: 9110
		private XRTableCell vendorWebsite;

		// Token: 0x04002397 RID: 9111
		private XRTableCell vendorEmail;

		// Token: 0x04002398 RID: 9112
		private XRTableCell vendorPhone;

		// Token: 0x04002399 RID: 9113
		private XRLabel thankYouLabel;

		// Token: 0x0400239A RID: 9114
		private XRLabel heartLabel;

		// Token: 0x0400239B RID: 9115
		private XRTable invoiceInfoTable;

		// Token: 0x0400239C RID: 9116
		private XRTableRow invoiceDateRow;

		// Token: 0x0400239D RID: 9117
		private XRTableCell invoiceDateCaption;

		// Token: 0x0400239E RID: 9118
		private XRTableCell invoiceDate;

		// Token: 0x0400239F RID: 9119
		private XRTableRow invoiceNumberRow;

		// Token: 0x040023A0 RID: 9120
		private XRTableCell invoiceNumberCaption;

		// Token: 0x040023A1 RID: 9121
		private XRTableCell invoiceNumber;

		// Token: 0x040023A2 RID: 9122
		private XRTable customerTable;

		// Token: 0x040023A3 RID: 9123
		private XRTableRow customerNameRow;

		// Token: 0x040023A4 RID: 9124
		private XRTableCell customerName;

		// Token: 0x040023A5 RID: 9125
		private XRLabel invoiceLabel;

		// Token: 0x040023A6 RID: 9126
		private XRTable headerTable;

		// Token: 0x040023A7 RID: 9127
		private XRTableRow headerTableRow;

		// Token: 0x040023A8 RID: 9128
		private XRTableCell productNameCaption;

		// Token: 0x040023A9 RID: 9129
		private XRTableCell quantityCaption;

		// Token: 0x040023AA RID: 9130
		private XRTableCell unitPriceCaption;

		// Token: 0x040023AB RID: 9131
		private XRTableCell lineTotalCaptionCell;

		// Token: 0x040023AC RID: 9132
		private XRControlStyle baseControlStyle;

		// Token: 0x040023AD RID: 9133
		private ObjectDataSource objectDataSource1;

		// Token: 0x040023AE RID: 9134
		private XRTableCell xrTableCell4;

		// Token: 0x040023AF RID: 9135
		private XRTableCell xrTableCell3;

		// Token: 0x040023B0 RID: 9136
		private XRTableCell xrTableCell2;

		// Token: 0x040023B1 RID: 9137
		private XRTableCell xrTableCell1;

		// Token: 0x040023B2 RID: 9138
		private DetailReportBand DetailReport;

		// Token: 0x040023B3 RID: 9139
		private DetailBand Detail1;

		// Token: 0x040023B4 RID: 9140
		private GroupHeaderBand GroupHeader3;

		// Token: 0x040023B5 RID: 9141
		private GroupFooterBand GroupFooter2;

		// Token: 0x040023B6 RID: 9142
		private XRControlStyle xrControlStyle1;

		// Token: 0x040023B7 RID: 9143
		private XRControlStyle xrControlStyle2;

		// Token: 0x040023B8 RID: 9144
		private XRLine xrLine1;

		// Token: 0x040023B9 RID: 9145
		private GroupFooterBand GroupFooter1;

		// Token: 0x040023BA RID: 9146
		private XRTable xrTable1;

		// Token: 0x040023BB RID: 9147
		private XRTableRow xrTableRow1;

		// Token: 0x040023BC RID: 9148
		private XRTableCell xrTableCell5;

		// Token: 0x040023BD RID: 9149
		private XRTableCell xrTableCell6;

		// Token: 0x040023BE RID: 9150
		private XRTableCell xrTableCell7;

		// Token: 0x040023BF RID: 9151
		private XRTableCell xrTableCell13;

		// Token: 0x040023C0 RID: 9152
		private XRTableRow xrTableRow2;

		// Token: 0x040023C1 RID: 9153
		private XRTableCell xrTableCell8;

		// Token: 0x040023C2 RID: 9154
		private XRTableCell xrTableCell9;

		// Token: 0x040023C3 RID: 9155
		private XRTableCell xrTableCell10;

		// Token: 0x040023C4 RID: 9156
		private XRTableCell xrTableCell14;

		// Token: 0x040023C5 RID: 9157
		private XRTable summariesTable;

		// Token: 0x040023C6 RID: 9158
		private XRTableRow totalCaptionRow;

		// Token: 0x040023C7 RID: 9159
		private XRTableCell invoiceDueDateCaption;

		// Token: 0x040023C8 RID: 9160
		private XRTableCell totalCaption;

		// Token: 0x040023C9 RID: 9161
		private XRTableRow totalRow;

		// Token: 0x040023CA RID: 9162
		private XRTableCell invoiceDueDate;

		// Token: 0x040023CB RID: 9163
		private XRTableCell total;

		// Token: 0x040023CC RID: 9164
		private XRBarCode xrBarCode1;

		// Token: 0x040023CD RID: 9165
		private XRTable vendorTable;

		// Token: 0x040023CE RID: 9166
		private XRTableRow vendorNameRow;

		// Token: 0x040023CF RID: 9167
		private XRTableCell vendorName;

		// Token: 0x040023D0 RID: 9168
		private XRTableRow vendorAddressRow;

		// Token: 0x040023D1 RID: 9169
		private XRTableCell vendorAddress;

		// Token: 0x040023D2 RID: 9170
		private XRTableRow vendorCityRow;

		// Token: 0x040023D3 RID: 9171
		private XRTableCell vendorCity;

		// Token: 0x040023D4 RID: 9172
		private XRTableRow xrTableRow3;

		// Token: 0x040023D5 RID: 9173
		private XRTableCell xrTableCell11;

		// Token: 0x040023D6 RID: 9174
		private XRTableRow xrTableRow4;

		// Token: 0x040023D7 RID: 9175
		private XRTableCell xrTableCell12;

		// Token: 0x040023D8 RID: 9176
		private XRPictureBox vendorLogo;

		// Token: 0x040023D9 RID: 9177
		private XRTableRow xrTableRow5;

		// Token: 0x040023DA RID: 9178
		private XRTableCell xrTableCell15;

		// Token: 0x040023DB RID: 9179
		private XRTableCell xrTableCell16;

		// Token: 0x040023DC RID: 9180
		private XRTableCell xrTableCell17;

		// Token: 0x040023DD RID: 9181
		private XRBarCode xrBarCode2;
	}
}
