﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data.Entity;

namespace EasyStock.Controller
{
	// Token: 0x0200052F RID: 1327
	public class ChangeTrackerItem
	{
		[DatabaseGenerated(DatabaseGeneratedOption.Identity)]
		public int ID { get; set; }

		[Display(Name = "������")]
		public string EntityName { get; set; }
		[Display(Name = "��� ������")]
		public string EntityDisplayName { get; set; }

		[Display(Name = "��� �������")]
		public EntityState State { get; set; }

		[Display(Name = "��������")]
		public string Properties { get; set; }
		[Display(Name = "���������")]
		public string ChangedProperties { get; set; }
	}
}
