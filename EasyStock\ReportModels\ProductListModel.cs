﻿using EasyStock.Models;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    internal class ProductListModel
    {
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Display(Name = "N° pièce")]
        public string Code { get; set; }

        [Display(Name = "Code-barres")]
        public string BarcodeCode { get; set; }

        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Type de produit")]
        public ProductType Type { get; set; }

        [Display(Name = "Description")]
        public string Descreption { get; set; }

        [Display(Name = "Code de catégorie")]
        public int? CategoryID { get; set; }

        [Display(Name = "Code de société")]
        public int? CompanyID { get; set; }

        [Display(Name = "Suspendu")]
        public bool Suspended { get; set; }

        [Display(Name = "Date d'expiration")]
        public bool HasExpier { get; set; }

        [Display(Name = "Numéro de série")]
        public bool HasSerial { get; set; }

        [Display(Name = "Garantie")]
        public bool HasWarranty { get; set; }

        [Display(Name = "Durée de conservation")]
        public int ShelfLife { get; set; }

        [Display(Name = "Durée de garantie en mois")]
        public int WarntyDuration { get; set; }

        [Display(Name = "Couleur")]
        public bool HasColor { get; set; }

        [Display(Name = "Taille")]
        public bool HasSize { get; set; }

        [Display(Name = "#")]
        public string CustomField1 { get; set; }

        [Display(Name = "#")]
        public string CustomField2 { get; set; }

        [Display(Name = "#")]
        public string CustomField3 { get; set; }

        [Display(Name = "#")]
        public string CustomField4 { get; set; }
    }
}
