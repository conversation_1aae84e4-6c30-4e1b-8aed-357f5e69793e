﻿<?xml version='1.0' encoding='UTF-8'?>
<svg x="0px" y="0px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" id="Layer_1" style="enable-background:new 0 0 32 32">
  <style type="text/css">
	.Blue{fill:#1177D7;}
	.Yellow{fill:#FFB115;}
	.Red{fill:#D11C1C;}
	.Green{fill:#039C23;}
	.Black{fill:#727272;}
	.White{fill:#FFFFFF;}
	.st0{opacity:0.5;}
	.st1{opacity:0.75;}
	.st2{display:none;}
	.st3{display:inline;fill:#FFB115;}
	.st4{display:inline;}
	.st5{display:inline;opacity:0.75;}
	.st6{display:inline;opacity:0.5;}
	.st7{display:inline;fill:#039C23;}
	.st8{display:inline;fill:#D11C1C;}
	.st9{display:inline;fill:#1177D7;}
	.st10{display:inline;fill:#FFFFFF;}
</style>
  <g id="Product_x5F_Group">
    <path d="M16,7.4l4-4V10h-4V7.4z M26,17.4V30l4-4V13.4L26,17.4z" class="Yellow" />
    <g class="st0">
      <path d="M14.6,6H2l4-4h12.6L14.6,6z M16,12l-4,4h12.6l4-4H16z" class="Yellow" />
    </g>
    <g class="st1">
      <path d="M14,11.2l-4,4V18v2H2V8h12V11.2z M12,30h12V18H12V30z" class="Yellow" />
    </g>
  </g>
</svg>