﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Windows.Forms;

namespace EasyStock.Models
{
    public class InvoiceShortCut
    {
        [Display(Name = "Événement")]
        public InvoiceShortCutAction Action { get; set; }

        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Display(Name = "Touche de raccourci")]
        public Keys Key { get; set; }

        [Display(Name = "Touche secondaire")]
        public Keys Modifier { get; set; }
    }
}
