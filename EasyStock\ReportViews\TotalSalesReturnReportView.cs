﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;

namespace EasyStock.ReportViews
{
	// Token: 0x02000353 RID: 851
	public partial class TotalSalesReturnReportView : XtraReportForm
	{
		// Token: 0x06001442 RID: 5186 RVA: 0x0000A7B0 File Offset: 0x000089B0
		public TotalSalesReturnReportView() : base(new ReportFilter[]
		{
			ReportFilter.Date,
			ReportFilter.Customer,
			ReportFilter.Store
		})
		{
			this.InitializeComponent();
		}

		// Token: 0x06001443 RID: 5187 RVA: 0x0014E4F4 File Offset: 0x0014C6F4
		internal override bool ValidateFilters()
		{
			bool flag = !base.FiltersForm.hasStartDate;
			bool result;
			if (flag)
			{
				base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
				result = false;
			}
			else
			{
				bool flag2 = !base.FiltersForm.hasEndtDate;
				if (flag2)
				{
					base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
					result = false;
				}
				else
				{
					result = true;
				}
			}
			return result;
		}

		// Token: 0x06001444 RID: 5188 RVA: 0x00169E74 File Offset: 0x00168074
		public override void RefreshDataSource()
		{
			bool flag = !this.ValidateFilters();
			if (!flag)
			{
				TotalSalesInvoicesReport rpt = new TotalSalesInvoicesReport();
				ICollection<TotalSalesReturnReportModel> dataSource = this.GetData();
				rpt.DataSource = dataSource;
				rpt.SetCompanyInfo(dataSource.FirstOrDefault<TotalSalesReturnReportModel>());
				rpt.Cell_ReportName.Text = this.Text;
				rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
				this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

		// Token: 0x06001445 RID: 5189 RVA: 0x00169EF0 File Offset: 0x001680F0
		private ICollection<TotalSalesReturnReportModel> GetData()
		{
			int[] StoresIDs = base.FiltersForm.Stores.EditValue?.ToArray();
			int[] CustomersIDs = base.FiltersForm.Customers.EditValue?.ToArray();
			ERPDataContext context = new ERPDataContext();
			try
			{
				IQueryable<SalesReturnInvoice> filterdInvoices = context.SalesReturnInvoices.Where((SalesReturnInvoice inv) => DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate);
				if (CustomersIDs != null && CustomersIDs.Count() > 0)
				{
					filterdInvoices = filterdInvoices.Where((SalesReturnInvoice c) => CustomersIDs.Contains(c.CustomerID));
				}
				if (StoresIDs != null && StoresIDs.Count() > 0)
				{
					filterdInvoices = filterdInvoices.Where((SalesReturnInvoice c) => StoresIDs.Contains(c.StoreID));
				}
				IQueryable<TotalSalesReturnReportModel> filterdInv = from inv in filterdInvoices
																	 join Bran in context.Stores on inv.StoreID equals Bran.ID
																	 join cust in context.Customers on inv.CustomerID equals cust.ID
																	 from customerCat in context.CustomersGroups.Where((CustomerGroup customerCat) => (int?)customerCat.ID == cust.GroupID).DefaultIfEmpty()
																	 select new TotalSalesReturnReportModel
																	 {
																		 InvStore = Bran.Name,
																		 CustomerCat = customerCat.Name,
																		 Customer = cust.Name,
																		 ReturnID = inv.ID,
																		 ReturnDate = inv.Date,
																		 TotalAmount = inv.Total,
																		 Expences = inv.OtherExpenses,
																		 Discount = inv.Discount,
																		 Taxes = inv.Tax,
																		 NetAmount = inv.Total + inv.OtherExpenses + inv.Tax - inv.Discount,
																		 PaidAmount = (context.PayDetails.Where((PayDetail d) => d.SourceID == inv.ID && (int)d.SourceType == 4).Sum(x => (double?)(x.Amount * x.CurrancyRate) ?? 0.0))
																	 };
				return filterdInv.ToList();
			}
			finally
			{
				if (context != null)
				{
					((IDisposable)context).Dispose();
				}
			}
		}
	}
}
