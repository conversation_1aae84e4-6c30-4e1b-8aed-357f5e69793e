﻿using EasyStock.Classes;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Comptes")]
    [Table("Accounts")]
    public class Account : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        public int ID { get; set; }

        [StringLength(50)]
        [Display(Name = "Numéro")]
        public string Number { get; set; }

        [Required(ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        [StringLength(400)]
        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Compte Principal")]
        public Account Parent
        {
            get { return this._parent; }
            set
            {
                this._parent = value;
                if (this._parent != null)
                {
                    this.Secrecy = this._parent.Secrecy;
                    this.AccountType = this._parent.AccountType;
                }
            }
        }

        [Display(Name = "Code du Compte Principal")]
        public int? ParentID { get; set; }

        [Display(Name = "Notes")]
        public string Note { get; set; }

        [Display(Name = "Niveau de Sécurité")]
        public SecracyLevel Secrecy { get; set; }

        public bool CanEdit { get; set; }

        [Display(Name = "Type de Compte")]
        public AccountType AccountType { get; set; }

        public CostCenter CostCenter { get; set; }

        [Display(Name = "Centre de Coût par Défaut")]
        public int? CostCenterID
        {
            get { return this.costCenterID; }
            set { base.SetProperty<int?>(ref this.costCenterID, value, "CostCenterID"); }
        }

        [Display(Name = "Restriction du Centre de Coût")]
        public CostCenterRestriction CostCenterRestriction { get; set; } = CostCenterRestriction.Optional;

        private Account _parent;
        private int? costCenterID;
    }
}
