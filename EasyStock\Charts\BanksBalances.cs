﻿using DevExpress.Data;
using EasyStock.Controller;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.Charts
{

    public class BanksBalances : GridChart
    {
        public class BanksBalancesModel
        {
            [Display(Name = "Banque")]
            public string Name { get; set; }

            [Display(Name = "Solde")]
            public double? Balance { get; set; }
        }

        public override string Caption => "Soldes des banques";

        public override Color Color => ColorTranslator.FromHtml("#424242");

        public BanksBalances()
        {
            view.OptionsView.ShowFooter = true;
            if (view.Columns.Count == 0)
            {
                view.PopulateColumns(typeof(BanksBalancesModel));
            }
            view.Columns["Balance"].SummaryItem.SetSummary(SummaryItemType.Sum, "{0}");
        }

        public override async Task<object> QueryData()
        {
            using (var db = new ERPDataContext())
            {
                // Query for Banks and their balances
                var dataSource = await (from bank in db.Banks
                                        select new BanksBalancesModel
                                        {
                                            Name = bank.Name,
                                            Balance = (db.JournalDetails
                                                       .Where(jd => jd.AccountID == bank.AccountID)
                                                       .Sum(jd => (double?)jd.Debit * jd.CurrencyRate) ?? 0.0) -
                                                      (db.JournalDetails
                                                       .Where(jd => jd.AccountID == bank.AccountID)
                                                       .Sum(jd => (double?)jd.Credit * jd.CurrencyRate) ?? 0.0)
                                        } into x
                                        orderby x.Balance descending
                                        select x).ToListAsync();

                return dataSource;
            }
        }

    }
}
