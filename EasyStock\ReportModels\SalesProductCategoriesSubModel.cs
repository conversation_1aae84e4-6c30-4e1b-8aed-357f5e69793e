﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class SalesProductCategoriesSubModel
    {
        [Display(Name = "Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Article")]
        public string ProductName { get; set; }

        [Display(Name = "Quantité")]
        public double Qty { get; set; }

        [Display(Name = "Coût total")]
        public double TotalCost { get; set; }

        [Display(Name = "Total des ventes")]
        public double TotalSales { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Total après remise")]
        public double TotalAfterDiscount
        {
            get
            {
                return this.TotalSales - this.Discount;
            }
        }

        [Display(Name = "Taxe ajoutée")]
        public double Taxes { get; set; }

        [Display(Name = "Total après taxe")]
        public double NetAmount
        {
            get
            {
                return this.TotalAfterDiscount + this.Taxes;
            }
        }

        [Display(Name = "Marge bénéficiaire")]
        public double ProfitMargin
        {
            get
            {
                return this.TotalSales - this.TotalCost;
            }
        }

        [Display(Name = "Pourcentage de marge bénéficiaire")]
        public double ProfitMarginPer
        {
            get
            {
                return (this.TotalSales - this.TotalCost) / this.TotalCost;
            }
        }
    }
}
