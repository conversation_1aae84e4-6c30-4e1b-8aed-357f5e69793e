﻿<?xml version='1.0' encoding='UTF-8'?>
<svg x="0px" y="0px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" id="Layer_1" style="enable-background:new 0 0 32 32">
  <style type="text/css">
	.Blue{fill:#1177D7;}
	.Yellow{fill:#FFB115;}
	.Red{fill:#D11C1C;}
	.Green{fill:#039C23;}
	.Black{fill:#727272;}
	.White{fill:#FFFFFF;}
	.st0{opacity:0.5;}
	.st1{opacity:0.75;}
	.st2{display:none;}
	.st3{display:inline;fill:#FFB115;}
	.st4{display:inline;}
	.st5{display:inline;opacity:0.75;}
	.st6{display:inline;opacity:0.5;}
	.st7{display:inline;fill:#039C23;}
	.st8{display:inline;fill:#D11C1C;}
	.st9{display:inline;fill:#1177D7;}
	.st10{display:inline;fill:#FFFFFF;}
</style>
  <g id="Address">
    <polygon points="8,28 8,18 2,18 16,4 30,18 24,18 24,28 18,28 18,18 14,18 14,28  " class="Blue" />
  </g>
</svg>