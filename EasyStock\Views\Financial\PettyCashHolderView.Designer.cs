﻿namespace EasyStock.Views.Financial
{
	// Token: 0x020002DE RID: 734
	public partial class PettyCashHolderView : global::EasyStock.MainViews.MasterForm
	{
		// Token: 0x06001273 RID: 4723 RVA: 0x00141D88 File Offset: 0x0013FF88
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001274 RID: 4724 RVA: 0x00141DC0 File Offset: 0x0013FFC0
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.pettyCashHolderBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colMaxCash = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDisable = new DevExpress.XtraGrid.Columns.GridColumn();
            this.LockupAccount = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.accountBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.NameTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.MaxCashTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.DisableCheckEdit = new DevExpress.XtraEditors.CheckEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForName = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForMaxCash = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.ItemForDisable = new DevExpress.XtraLayout.LayoutControlItem();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashHolderBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.LockupAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.accountBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.MaxCashTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DisableCheckEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMaxCash)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDisable)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.NameTextEdit);
            this.dataLayoutControl1.Controls.Add(this.MaxCashTextEdit);
            this.dataLayoutControl1.Controls.Add(this.DisableCheckEdit);
            this.dataLayoutControl1.DataSource = this.pettyCashHolderBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(891, 407);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.pettyCashHolderBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(298, 44);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.LockupAccount});
            this.gridControl1.Size = new System.Drawing.Size(569, 339);
            this.gridControl1.TabIndex = 5;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // pettyCashHolderBindingSource
            // 
            this.pettyCashHolderBindingSource.DataSource = typeof(EasyStock.Models.PettyCashHolder);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colName,
            this.colMaxCash,
            this.colDisable});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            // 
            // colID
            // 
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.OptionsColumn.ReadOnly = true;
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            // 
            // colName
            // 
            this.colName.FieldName = "Name";
            this.colName.Name = "colName";
            this.colName.Visible = true;
            this.colName.VisibleIndex = 1;
            // 
            // colMaxCash
            // 
            this.colMaxCash.FieldName = "MaxCash";
            this.colMaxCash.Name = "colMaxCash";
            this.colMaxCash.Visible = true;
            this.colMaxCash.VisibleIndex = 2;
            // 
            // colDisable
            // 
            this.colDisable.FieldName = "Disable";
            this.colDisable.Name = "colDisable";
            this.colDisable.Visible = true;
            this.colDisable.VisibleIndex = 3;
            // 
            // LockupAccount
            // 
            this.LockupAccount.AutoHeight = false;
            this.LockupAccount.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.LockupAccount.DataSource = this.accountBindingSource;
            this.LockupAccount.DisplayMember = "Name";
            this.LockupAccount.Name = "LockupAccount";
            this.LockupAccount.ValueMember = "ID";
            // 
            // accountBindingSource
            // 
            this.accountBindingSource.DataSource = typeof(EasyStock.Models.Account);
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashHolderBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(115, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.EditMask = "N0";
            this.IDTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(81, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 0;
            // 
            // NameTextEdit
            // 
            this.NameTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashHolderBindingSource, "Name", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NameTextEdit.Location = new System.Drawing.Point(115, 67);
            this.NameTextEdit.Name = "NameTextEdit";
            this.NameTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.NameTextEdit.Size = new System.Drawing.Size(155, 20);
            this.NameTextEdit.StyleController = this.dataLayoutControl1;
            this.NameTextEdit.TabIndex = 3;
            // 
            // MaxCashTextEdit
            // 
            this.MaxCashTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashHolderBindingSource, "MaxCash", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.MaxCashTextEdit.Location = new System.Drawing.Point(115, 91);
            this.MaxCashTextEdit.Name = "MaxCashTextEdit";
            this.MaxCashTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.MaxCashTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.MaxCashTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.MaxCashTextEdit.Properties.Mask.EditMask = "F";
            this.MaxCashTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.MaxCashTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.MaxCashTextEdit.Size = new System.Drawing.Size(155, 20);
            this.MaxCashTextEdit.StyleController = this.dataLayoutControl1;
            this.MaxCashTextEdit.TabIndex = 4;
            // 
            // DisableCheckEdit
            // 
            this.DisableCheckEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashHolderBindingSource, "Disable", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DisableCheckEdit.Location = new System.Drawing.Point(200, 44);
            this.DisableCheckEdit.Name = "DisableCheckEdit";
            this.DisableCheckEdit.Properties.Caption = "Désactivé";
            this.DisableCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.DisableCheckEdit.Size = new System.Drawing.Size(70, 19);
            this.DisableCheckEdit.StyleController = this.dataLayoutControl1;
            this.DisableCheckEdit.TabIndex = 2;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(891, 407);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup3,
            this.layoutControlGroup2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(871, 387);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.AppearanceGroup.BorderColor = System.Drawing.Color.Green;
            this.layoutControlGroup2.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForID,
            this.ItemForName,
            this.ItemForMaxCash,
            this.emptySpaceItem1,
            this.ItemForDisable});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(274, 387);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MinSize = new System.Drawing.Size(121, 22);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(176, 23);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(87, 13);
            // 
            // ItemForName
            // 
            this.ItemForName.Control = this.NameTextEdit;
            this.ItemForName.Location = new System.Drawing.Point(0, 23);
            this.ItemForName.Name = "ItemForName";
            this.ItemForName.Size = new System.Drawing.Size(250, 24);
            this.ItemForName.TextSize = new System.Drawing.Size(87, 13);
            // 
            // ItemForMaxCash
            // 
            this.ItemForMaxCash.Control = this.MaxCashTextEdit;
            this.ItemForMaxCash.Location = new System.Drawing.Point(0, 47);
            this.ItemForMaxCash.Name = "ItemForMaxCash";
            this.ItemForMaxCash.Size = new System.Drawing.Size(250, 24);
            this.ItemForMaxCash.TextSize = new System.Drawing.Size(87, 13);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 71);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(250, 272);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // ItemForDisable
            // 
            this.ItemForDisable.Control = this.DisableCheckEdit;
            this.ItemForDisable.Location = new System.Drawing.Point(176, 0);
            this.ItemForDisable.Name = "ItemForDisable";
            this.ItemForDisable.Size = new System.Drawing.Size(74, 23);
            this.ItemForDisable.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForDisable.TextVisible = false;
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.AppearanceGroup.BackColor = System.Drawing.Color.White;
            this.layoutControlGroup3.AppearanceGroup.BackColor2 = System.Drawing.Color.White;
            this.layoutControlGroup3.AppearanceGroup.BorderColor = System.Drawing.Color.DarkBlue;
            this.layoutControlGroup3.AppearanceGroup.Options.UseBackColor = true;
            this.layoutControlGroup3.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup3.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1});
            this.layoutControlGroup3.Location = new System.Drawing.Point(274, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(597, 387);
            this.layoutControlGroup3.Text = "Liste des détenteurs des avances financières.";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(573, 343);
            this.layoutControlItem1.Text = "Responsables de la caisse";
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // PettyCashHolderView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(891, 457);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "PettyCashHolderView";
            this.RightToLeftLayout = true;
            this.Text = "Détenteurs des avances financières.";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashHolderBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.LockupAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.accountBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NameTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.MaxCashTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DisableCheckEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForMaxCash)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDisable)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x0400184B RID: 6219
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x0400184C RID: 6220
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x0400184D RID: 6221
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x0400184E RID: 6222
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x0400184F RID: 6223
		private global::System.Windows.Forms.BindingSource pettyCashHolderBindingSource;

		// Token: 0x04001850 RID: 6224
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x04001851 RID: 6225
		private global::DevExpress.XtraGrid.Columns.GridColumn colID;

		// Token: 0x04001852 RID: 6226
		private global::DevExpress.XtraGrid.Columns.GridColumn colName;

		// Token: 0x04001853 RID: 6227
		private global::DevExpress.XtraGrid.Columns.GridColumn colMaxCash;

		// Token: 0x04001854 RID: 6228
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit LockupAccount;

		// Token: 0x04001855 RID: 6229
		private global::System.Windows.Forms.BindingSource accountBindingSource;

		// Token: 0x04001856 RID: 6230
		private global::DevExpress.XtraGrid.Columns.GridColumn colDisable;

		// Token: 0x04001857 RID: 6231
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x04001858 RID: 6232
		private global::DevExpress.XtraEditors.TextEdit NameTextEdit;

		// Token: 0x04001859 RID: 6233
		private global::DevExpress.XtraEditors.TextEdit MaxCashTextEdit;

		// Token: 0x0400185A RID: 6234
		private global::DevExpress.XtraEditors.CheckEdit DisableCheckEdit;

		// Token: 0x0400185B RID: 6235
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x0400185C RID: 6236
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x0400185D RID: 6237
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x0400185E RID: 6238
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x0400185F RID: 6239
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForName;

		// Token: 0x04001860 RID: 6240
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForMaxCash;

		// Token: 0x04001861 RID: 6241
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDisable;

		// Token: 0x04001862 RID: 6242
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		// Token: 0x04001863 RID: 6243
		private global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;
	}
}
