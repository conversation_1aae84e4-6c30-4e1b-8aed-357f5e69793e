﻿namespace EasyStock.ReportViews
{
	// Token: 0x0200032D RID: 813
	public partial class ProductStoreValidityReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x060013E0 RID: 5088 RVA: 0x00160B30 File Offset: 0x0015ED30
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060013E1 RID: 5089 RVA: 0x00160B68 File Offset: 0x0015ED68
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 59);
            this.documentViewer1.Size = new System.Drawing.Size(800, 367);
            // 
            // ProductStoreValidityReportView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Name = "ProductStoreValidityReportView";
            this.Text = "Dates de péremption des articles en dépôt";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x0400198F RID: 6543
		private global::System.ComponentModel.IContainer components = null;
	}
}
