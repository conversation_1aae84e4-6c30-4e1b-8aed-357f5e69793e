﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraPrinting.BarCode;
using DevExpress.XtraPrinting.Drawing;
using DevExpress.XtraReports;
using DevExpress.XtraReports.UI;
using EasyStock.Classes;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x0200039A RID: 922
	public class JournalReport : MasterReport
	{
		// Token: 0x0600159C RID: 5532 RVA: 0x0000AF35 File Offset: 0x00009135
		public JournalReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x0600159D RID: 5533 RVA: 0x001D62BC File Offset: 0x001D44BC
		public static XtraReport GetReport(JournalReportModel ds)
		{
			JournalReport rpt = new JournalReport();
			rpt.LoadLayout();
			rpt.objectDataSource1.DataSource = ds;
			rpt.SetCompanyInfo(ds);
			rpt.CreateDocument();
			return rpt;
		}

		// Token: 0x0600159E RID: 5534 RVA: 0x001D62F8 File Offset: 0x001D44F8
		public static void Print(JournalReportModel ds)
		{
			XtraReport rpt = JournalReport.GetReport(ds);
			switch (CurrentSession.InvoicePrintMode)
			{
			case PrintMode.Direct:
				rpt.Print("");
				break;
			case PrintMode.ShowPreview:
				rpt.ShowPreview();
				break;
			case PrintMode.ShowDialog:
				rpt.PrintDialog();
				break;
			}
		}

		// Token: 0x0600159F RID: 5535 RVA: 0x001D634C File Offset: 0x001D454C
		public static void Print(ICollection<JournalReportModel> ds)
		{
			XtraReport report = new XtraReport();
			report.CreateDocument();
			report.Pages.Clear();
			foreach (JournalReportModel item in ds)
			{
				XtraReport rpt = JournalReport.GetReport(item);
				report.ModifyDocument(delegate(IDocumentModifier x)
				{
					x.AddPages(rpt.Pages);
				});
			}
			EasyStock.Reports.MasterReport.Print(report);
		}

		// Token: 0x060015A0 RID: 5536 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void xrLine1_BeforePrint(object sender, PrintEventArgs e)
		{
		}

		// Token: 0x060015A1 RID: 5537 RVA: 0x001D63D8 File Offset: 0x001D45D8
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060015A2 RID: 5538 RVA: 0x001D6410 File Offset: 0x001D4610
		private void InitializeComponent()
		{
			this.components = new Container();
			Code128Generator code128Generator = new Code128Generator();
			ComponentResourceManager resources = new ComponentResourceManager(typeof(JournalReport));
			XRSummary xrSummary = new XRSummary();
			XRSummary xrSummary2 = new XRSummary();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.xrPageInfo2 = new XRPageInfo();
			this.xrPageInfo1 = new XRPageInfo();
			this.xrBarCode1 = new XRBarCode();
			this.Detail = new DetailBand();
			this.xrLabel1 = new XRLabel();
			this.xrLabel10 = new XRLabel();
			this.vendorLogo = new XRPictureBox();
			this.vendorTable = new XRTable();
			this.vendorNameRow = new XRTableRow();
			this.vendorName = new XRTableCell();
			this.vendorAddressRow = new XRTableRow();
			this.vendorAddress = new XRTableCell();
			this.vendorCityRow = new XRTableRow();
			this.vendorCity = new XRTableCell();
			this.xrTableRow3 = new XRTableRow();
			this.xrTableCell11 = new XRTableCell();
			this.xrTableRow4 = new XRTableRow();
			this.xrTableCell12 = new XRTableCell();
			this.invoiceInfoTable = new XRTable();
			this.invoiceDateRow = new XRTableRow();
			this.invoiceDateCaption = new XRTableCell();
			this.invoiceDate = new XRTableCell();
			this.invoiceNumberRow = new XRTableRow();
			this.invoiceNumberCaption = new XRTableCell();
			this.invoiceNumber = new XRTableCell();
			this.DetailReport = new DetailReportBand();
			this.Detail1 = new DetailBand();
			this.xrTable2 = new XRTable();
			this.xrTableRow2 = new XRTableRow();
			this.xrTableCell8 = new XRTableCell();
			this.xrTableCell9 = new XRTableCell();
			this.xrTableCell13 = new XRTableCell();
			this.xrTableCell14 = new XRTableCell();
			this.xrTableCell15 = new XRTableCell();
			this.xrTableCell16 = new XRTableCell();
			this.GroupHeader1 = new GroupHeaderBand();
			this.xrTable1 = new XRTable();
			this.xrTableRow1 = new XRTableRow();
			this.xrTableCell1 = new XRTableCell();
			this.xrTableCell2 = new XRTableCell();
			this.xrTableCell3 = new XRTableCell();
			this.xrTableCell4 = new XRTableCell();
			this.xrTableCell5 = new XRTableCell();
			this.xrTableCell6 = new XRTableCell();
			this.GroupFooter1 = new GroupFooterBand();
			this.xrLine2 = new XRLine();
			this.xrLine1 = new XRLine();
			this.xrLabel2 = new XRLabel();
			this.xrLabel3 = new XRLabel();
			this.objectDataSource1 = new ObjectDataSource(this.components);
			this.xrTableRow5 = new XRTableRow();
			this.xrTableCell7 = new XRTableCell();
			this.xrTableCell10 = new XRTableCell();
			((ISupportInitialize)this.vendorTable).BeginInit();
			((ISupportInitialize)this.invoiceInfoTable).BeginInit();
			((ISupportInitialize)this.xrTable2).BeginInit();
			((ISupportInitialize)this.xrTable1).BeginInit();
			((ISupportInitialize)this.objectDataSource1).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.TopMargin.Name = "TopMargin";
			this.BottomMargin.Controls.AddRange(new XRControl[]
			{
				this.xrPageInfo2,
				this.xrPageInfo1,
				this.xrBarCode1
			});
			this.BottomMargin.HeightF = 69f;
			this.BottomMargin.Name = "BottomMargin";
			this.xrPageInfo2.LocationFloat = new PointFloat(0f, 0f);
			this.xrPageInfo2.Name = "xrPageInfo2";
			this.xrPageInfo2.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrPageInfo2.PageInfo = PageInfo.DateTime;
			this.xrPageInfo2.SizeF = new SizeF(234.2061f, 31.70834f);
			this.xrPageInfo1.LocationFloat = new PointFloat(263.4583f, 0f);
			this.xrPageInfo1.Name = "xrPageInfo1";
			this.xrPageInfo1.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrPageInfo1.SizeF = new SizeF(100f, 31.70834f);
			this.xrPageInfo1.StylePriority.UseTextAlignment = false;
			this.xrPageInfo1.TextAlignment = TextAlignment.TopCenter;
			this.xrBarCode1.AutoModule = true;
			this.xrBarCode1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ID]")
			});
			this.xrBarCode1.LocationFloat = new PointFloat(392.7106f, 0f);
			this.xrBarCode1.Name = "xrBarCode1";
			this.xrBarCode1.Padding = new PaddingInfo(10, 10, 0, 0, 100f);
			this.xrBarCode1.ShowText = false;
			this.xrBarCode1.SizeF = new SizeF(234.2061f, 31.70834f);
			this.xrBarCode1.Symbology = code128Generator;
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.xrLabel1,
				this.xrLabel10,
				this.vendorLogo,
				this.vendorTable,
				this.invoiceInfoTable
			});
			this.Detail.HeightF = 214.3751f;
			this.Detail.Name = "Detail";
			this.xrLabel1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Note]")
			});
			this.xrLabel1.LocationFloat = new PointFloat(83.81561f, 173.9583f);
			this.xrLabel1.Multiline = true;
			this.xrLabel1.Name = "xrLabel1";
			this.xrLabel1.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrLabel1.SizeF = new SizeF(231.1432f, 40.41681f);
			this.xrLabel1.Text = "xrLabel1";
			this.xrLabel10.BorderColor = SystemColors.HotTrack;
			this.xrLabel10.Font = new Font("Arial", 26f, FontStyle.Bold);
			this.xrLabel10.LocationFloat = new PointFloat(0f, 0f);
			this.xrLabel10.Multiline = true;
			this.xrLabel10.Name = "xrLabel10";
			this.xrLabel10.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrLabel10.SizeF = new SizeF(254.2009f, 98.95827f);
			this.xrLabel10.StylePriority.UseBorderColor = false;
			this.xrLabel10.StylePriority.UseFont = false;
			this.xrLabel10.StylePriority.UseTextAlignment = false;
			this.xrLabel10.Text = "قيد يومية";
			this.xrLabel10.TextAlignment = TextAlignment.MiddleCenter;
			this.vendorLogo.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "ImageSource", "[CompanyLogo]")
			});
			this.vendorLogo.ImageAlignment = ImageAlignment.TopLeft;
			this.vendorLogo.ImageSource = new ImageSource("img", resources.GetString("vendorLogo.ImageSource"));
			this.vendorLogo.LocationFloat = new PointFloat(447.2501f, 0f);
			this.vendorLogo.Name = "vendorLogo";
			this.vendorLogo.SizeF = new SizeF(150.3605f, 98.95827f);
			this.vendorLogo.Sizing = ImageSizeMode.Squeeze;
			this.vendorLogo.StylePriority.UseBorders = false;
			this.vendorLogo.StylePriority.UsePadding = false;
			this.vendorTable.LocationFloat = new PointFloat(417.8606f, 98.95827f);
			this.vendorTable.Name = "vendorTable";
			this.vendorTable.Rows.AddRange(new XRTableRow[]
			{
				this.vendorNameRow,
				this.vendorAddressRow,
				this.vendorCityRow,
				this.xrTableRow3,
				this.xrTableRow4
			});
			this.vendorTable.SizeF = new SizeF(209.1394f, 115.4168f);
			this.vendorTable.StylePriority.UseTextAlignment = false;
			this.vendorTable.TextAlignment = TextAlignment.TopCenter;
			this.vendorNameRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorName
			});
			this.vendorNameRow.Name = "vendorNameRow";
			this.vendorNameRow.Weight = 1.0;
			this.vendorName.CanShrink = true;
			this.vendorName.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyName]")
			});
			this.vendorName.Font = new Font("Arial", 14.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.vendorName.Name = "vendorName";
			this.vendorName.StylePriority.UseFont = false;
			this.vendorName.StylePriority.UsePadding = false;
			this.vendorName.Text = "VendorName";
			this.vendorName.Weight = 1.0;
			this.vendorAddressRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorAddress
			});
			this.vendorAddressRow.Name = "vendorAddressRow";
			this.vendorAddressRow.Weight = 1.0;
			this.vendorAddress.CanShrink = true;
			this.vendorAddress.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyAddress]")
			});
			this.vendorAddress.Name = "vendorAddress";
			this.vendorAddress.StylePriority.UseFont = false;
			this.vendorAddress.Text = "VendorAddress";
			this.vendorAddress.Weight = 1.0;
			this.vendorCityRow.Cells.AddRange(new XRTableCell[]
			{
				this.vendorCity
			});
			this.vendorCityRow.Name = "vendorCityRow";
			this.vendorCityRow.Weight = 1.0;
			this.vendorCity.CanShrink = true;
			this.vendorCity.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyCity]")
			});
			this.vendorCity.Name = "vendorCity";
			this.vendorCity.StylePriority.UseFont = false;
			this.vendorCity.Text = "VendorCity";
			this.vendorCity.Weight = 1.0;
			this.xrTableRow3.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell11
			});
			this.xrTableRow3.Name = "xrTableRow3";
			this.xrTableRow3.Weight = 1.0;
			this.xrTableCell11.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CommercialBook]")
			});
			this.xrTableCell11.Multiline = true;
			this.xrTableCell11.Name = "xrTableCell11";
			this.xrTableCell11.StylePriority.UseFont = false;
			this.xrTableCell11.Text = "xrTableCell11";
			this.xrTableCell11.Weight = 1.0;
			this.xrTableRow4.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell12
			});
			this.xrTableRow4.Name = "xrTableRow4";
			this.xrTableRow4.Weight = 1.0;
			this.xrTableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyTaxCard]")
			});
			this.xrTableCell12.Multiline = true;
			this.xrTableCell12.Name = "xrTableCell12";
			this.xrTableCell12.StylePriority.UseFont = false;
			this.xrTableCell12.Text = "xrTableCell12";
			this.xrTableCell12.Weight = 1.0;
			this.invoiceInfoTable.LocationFloat = new PointFloat(0f, 98.95827f);
			this.invoiceInfoTable.Name = "invoiceInfoTable";
			this.invoiceInfoTable.Rows.AddRange(new XRTableRow[]
			{
				this.invoiceDateRow,
				this.invoiceNumberRow,
				this.xrTableRow5
			});
			this.invoiceInfoTable.SizeF = new SizeF(315.0421f, 75f);
			this.invoiceDateRow.Cells.AddRange(new XRTableCell[]
			{
				this.invoiceDateCaption,
				this.invoiceDate
			});
			this.invoiceDateRow.Name = "invoiceDateRow";
			this.invoiceDateRow.Weight = 1.0;
			this.invoiceDateCaption.CanShrink = true;
			this.invoiceDateCaption.Name = "invoiceDateCaption";
			this.invoiceDateCaption.StylePriority.UseFont = false;
			this.invoiceDateCaption.StylePriority.UsePadding = false;
			this.invoiceDateCaption.StylePriority.UseTextAlignment = false;
			this.invoiceDateCaption.Text = "تاريخ التحرير";
			this.invoiceDateCaption.TextAlignment = TextAlignment.TopLeft;
			this.invoiceDateCaption.Weight = 0.4965592917127513;
			this.invoiceDate.CanShrink = true;
			this.invoiceDate.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Date]")
			});
			this.invoiceDate.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.invoiceDate.Name = "invoiceDate";
			this.invoiceDate.StylePriority.UseFont = false;
			this.invoiceDate.TextFormatString = "{0:d MMMM yyyy}";
			this.invoiceDate.Weight = 1.3680312174875988;
			this.invoiceNumberRow.Cells.AddRange(new XRTableCell[]
			{
				this.invoiceNumberCaption,
				this.invoiceNumber
			});
			this.invoiceNumberRow.Name = "invoiceNumberRow";
			this.invoiceNumberRow.Weight = 1.0;
			this.invoiceNumberCaption.CanShrink = true;
			this.invoiceNumberCaption.Name = "invoiceNumberCaption";
			this.invoiceNumberCaption.StylePriority.UseFont = false;
			this.invoiceNumberCaption.StylePriority.UsePadding = false;
			this.invoiceNumberCaption.StylePriority.UseTextAlignment = false;
			this.invoiceNumberCaption.Text = "كود القيد";
			this.invoiceNumberCaption.TextAlignment = TextAlignment.TopLeft;
			this.invoiceNumberCaption.Weight = 0.4965592917127513;
			this.invoiceNumber.CanShrink = true;
			this.invoiceNumber.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Code]")
			});
			this.invoiceNumber.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.invoiceNumber.Name = "invoiceNumber";
			this.invoiceNumber.StylePriority.UseFont = false;
			this.invoiceNumber.Weight = 1.3680312174875988;
			this.DetailReport.Bands.AddRange(new Band[]
			{
				this.Detail1,
				this.GroupHeader1,
				this.GroupFooter1
			});
			this.DetailReport.DataMember = "Details";
			this.DetailReport.DataSource = this.objectDataSource1;
			this.DetailReport.Level = 0;
			this.DetailReport.Name = "DetailReport";
			this.Detail1.Controls.AddRange(new XRControl[]
			{
				this.xrTable2
			});
			this.Detail1.HeightF = 25f;
			this.Detail1.Name = "Detail1";
			this.xrTable2.BorderColor = Color.FromArgb(64, 64, 64);
			this.xrTable2.Borders = (BorderSide.Left | BorderSide.Right | BorderSide.Bottom);
			this.xrTable2.BorderWidth = 0.8f;
			this.xrTable2.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable2.Name = "xrTable2";
			this.xrTable2.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable2.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow2
			});
			this.xrTable2.SizeF = new SizeF(626.8325f, 25f);
			this.xrTable2.StylePriority.UseBorderColor = false;
			this.xrTable2.StylePriority.UseBorders = false;
			this.xrTable2.StylePriority.UseBorderWidth = false;
			this.xrTableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell8,
				this.xrTableCell9,
				this.xrTableCell13,
				this.xrTableCell14,
				this.xrTableCell15,
				this.xrTableCell16
			});
			this.xrTableRow2.Name = "xrTableRow2";
			this.xrTableRow2.Weight = 11.5;
			this.xrTableCell8.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Statement]")
			});
			this.xrTableCell8.Font = new Font("Arial", 11f, FontStyle.Bold);
			this.xrTableCell8.Multiline = true;
			this.xrTableCell8.Name = "xrTableCell8";
			this.xrTableCell8.Padding = new PaddingInfo(3, 5, 3, 3, 100f);
			this.xrTableCell8.StylePriority.UseFont = false;
			this.xrTableCell8.StylePriority.UsePadding = false;
			this.xrTableCell8.StylePriority.UseTextAlignment = false;
			this.xrTableCell8.Text = "xrTableCell8";
			this.xrTableCell8.TextAlignment = TextAlignment.MiddleJustify;
			this.xrTableCell8.Weight = 0.*****************;
			this.xrTableCell9.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Account]")
			});
			this.xrTableCell9.Font = new Font("Arial", 11f, FontStyle.Bold);
			this.xrTableCell9.Multiline = true;
			this.xrTableCell9.Name = "xrTableCell9";
			this.xrTableCell9.Padding = new PaddingInfo(3, 5, 3, 3, 100f);
			this.xrTableCell9.StylePriority.UseFont = false;
			this.xrTableCell9.StylePriority.UsePadding = false;
			this.xrTableCell9.StylePriority.UseTextAlignment = false;
			this.xrTableCell9.Text = "xrTableCell9";
			this.xrTableCell9.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell9.Weight = 0.*****************;
			this.xrTableCell13.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Iif([Credit] > 0 ,[Credit], '')")
			});
			this.xrTableCell13.Font = new Font("Arial", 11f);
			this.xrTableCell13.Multiline = true;
			this.xrTableCell13.Name = "xrTableCell13";
			this.xrTableCell13.StylePriority.UseFont = false;
			this.xrTableCell13.StylePriority.UseTextAlignment = false;
			this.xrTableCell13.Text = "xrTableCell13";
			this.xrTableCell13.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell13.Weight = 0.18604604204423913;
			this.xrTableCell14.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Iif([Debit] > 0 ,[Debit], '')")
			});
			this.xrTableCell14.Font = new Font("Arial", 11f);
			this.xrTableCell14.Multiline = true;
			this.xrTableCell14.Name = "xrTableCell14";
			this.xrTableCell14.StylePriority.UseFont = false;
			this.xrTableCell14.StylePriority.UseTextAlignment = false;
			this.xrTableCell14.Text = "xrTableCell14";
			this.xrTableCell14.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell14.Weight = 0.1893357083069067;
			this.xrTableCell15.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Currancy]")
			});
			this.xrTableCell15.Font = new Font("Arial", 11f);
			this.xrTableCell15.Multiline = true;
			this.xrTableCell15.Name = "xrTableCell15";
			this.xrTableCell15.StylePriority.UseFont = false;
			this.xrTableCell15.StylePriority.UseTextAlignment = false;
			this.xrTableCell15.Text = "xrTableCell15";
			this.xrTableCell15.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell15.Weight = 0.1062907210515375;
			this.xrTableCell16.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CurrancyRate]")
			});
			this.xrTableCell16.Font = new Font("Arial", 11f);
			this.xrTableCell16.Multiline = true;
			this.xrTableCell16.Name = "xrTableCell16";
			this.xrTableCell16.StylePriority.UseFont = false;
			this.xrTableCell16.StylePriority.UseTextAlignment = false;
			this.xrTableCell16.Text = "xrTableCell16";
			this.xrTableCell16.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell16.Weight = 0.07676283303952383;
			this.GroupHeader1.Controls.AddRange(new XRControl[]
			{
				this.xrTable1
			});
			this.GroupHeader1.HeightF = 25f;
			this.GroupHeader1.Name = "GroupHeader1";
			this.xrTable1.Borders = BorderSide.All;
			this.xrTable1.BorderWidth = 2f;
			this.xrTable1.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable1.Name = "xrTable1";
			this.xrTable1.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable1.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow1
			});
			this.xrTable1.SizeF = new SizeF(626.9159f, 25f);
			this.xrTable1.StylePriority.UseBorders = false;
			this.xrTable1.StylePriority.UseBorderWidth = false;
			this.xrTableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell1,
				this.xrTableCell2,
				this.xrTableCell3,
				this.xrTableCell4,
				this.xrTableCell5,
				this.xrTableCell6
			});
			this.xrTableRow1.Name = "xrTableRow1";
			this.xrTableRow1.Weight = 11.5;
			this.xrTableCell1.BackColor = Color.LightGray;
			this.xrTableCell1.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell1.Multiline = true;
			this.xrTableCell1.Name = "xrTableCell1";
			this.xrTableCell1.Padding = new PaddingInfo(3, 5, 3, 3, 100f);
			this.xrTableCell1.StylePriority.UseBackColor = false;
			this.xrTableCell1.StylePriority.UseFont = false;
			this.xrTableCell1.StylePriority.UsePadding = false;
			this.xrTableCell1.StylePriority.UseTextAlignment = false;
			this.xrTableCell1.Text = "البيان";
			this.xrTableCell1.TextAlignment = TextAlignment.MiddleJustify;
			this.xrTableCell1.Weight = 3.993619704830602;
			this.xrTableCell2.BackColor = Color.LightGray;
			this.xrTableCell2.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell2.Multiline = true;
			this.xrTableCell2.Name = "xrTableCell2";
			this.xrTableCell2.StylePriority.UseBackColor = false;
			this.xrTableCell2.StylePriority.UseFont = false;
			this.xrTableCell2.StylePriority.UseTextAlignment = false;
			this.xrTableCell2.Text = "الحساب";
			this.xrTableCell2.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell2.Weight = 2.003191924364667;
			this.xrTableCell3.BackColor = Color.LightGray;
			this.xrTableCell3.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell3.Multiline = true;
			this.xrTableCell3.Name = "xrTableCell3";
			this.xrTableCell3.StylePriority.UseBackColor = false;
			this.xrTableCell3.StylePriority.UseFont = false;
			this.xrTableCell3.StylePriority.UseTextAlignment = false;
			this.xrTableCell3.Text = "دائن";
			this.xrTableCell3.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell3.Weight = 1.9999981015227732;
			this.xrTableCell4.BackColor = Color.LightGray;
			this.xrTableCell4.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell4.Multiline = true;
			this.xrTableCell4.Name = "xrTableCell4";
			this.xrTableCell4.StylePriority.UseBackColor = false;
			this.xrTableCell4.StylePriority.UseFont = false;
			this.xrTableCell4.StylePriority.UseTextAlignment = false;
			this.xrTableCell4.Text = "مدين";
			this.xrTableCell4.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell4.Weight = 2.0353611828584057;
			this.xrTableCell5.BackColor = Color.LightGray;
			this.xrTableCell5.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell5.Multiline = true;
			this.xrTableCell5.Name = "xrTableCell5";
			this.xrTableCell5.StylePriority.UseBackColor = false;
			this.xrTableCell5.StylePriority.UseFont = false;
			this.xrTableCell5.StylePriority.UseTextAlignment = false;
			this.xrTableCell5.Text = "العمله";
			this.xrTableCell5.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell5.Weight = 1.142627604443021;
			this.xrTableCell6.BackColor = Color.LightGray;
			this.xrTableCell6.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell6.Multiline = true;
			this.xrTableCell6.Name = "xrTableCell6";
			this.xrTableCell6.StylePriority.UseBackColor = false;
			this.xrTableCell6.StylePriority.UseFont = false;
			this.xrTableCell6.StylePriority.UseTextAlignment = false;
			this.xrTableCell6.Text = "المعامل";
			this.xrTableCell6.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell6.Weight = 0.8252014819805302;
			this.GroupFooter1.Controls.AddRange(new XRControl[]
			{
				this.xrLine2,
				this.xrLine1,
				this.xrLabel2,
				this.xrLabel3
			});
			this.GroupFooter1.HeightF = 29f;
			this.GroupFooter1.Name = "GroupFooter1";
			this.xrLine2.LocationFloat = new PointFloat(0.0833737f, 0f);
			this.xrLine2.Name = "xrLine2";
			this.xrLine2.SizeF = new SizeF(626.9166f, 3f);
			this.xrLine1.LocationFloat = new PointFloat(8.646399E-05f, 26f);
			this.xrLine1.Name = "xrLine1";
			this.xrLine1.SizeF = new SizeF(626.9166f, 3f);
			this.xrLine1.BeforePrint += this.xrLine1_BeforePrint;
			this.xrLabel2.Borders = (BorderSide.Left | BorderSide.Right);
			this.xrLabel2.BorderWidth = 1f;
			this.xrLabel2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "sumSum([Debit])")
			});
			this.xrLabel2.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel2.LocationFloat = new PointFloat(417.7774f, 3.000005f);
			this.xrLabel2.Multiline = true;
			this.xrLabel2.Name = "xrLabel2";
			this.xrLabel2.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrLabel2.SizeF = new SizeF(106.2499f, 23f);
			this.xrLabel2.StylePriority.UseBorders = false;
			this.xrLabel2.StylePriority.UseBorderWidth = false;
			this.xrLabel2.StylePriority.UseFont = false;
			this.xrLabel2.StylePriority.UseTextAlignment = false;
			xrSummary.Running = SummaryRunning.Report;
			this.xrLabel2.Summary = xrSummary;
			this.xrLabel2.Text = "xrLabel2";
			this.xrLabel2.TextAlignment = TextAlignment.TopCenter;
			this.xrLabel3.Borders = BorderSide.Left;
			this.xrLabel3.BorderWidth = 1f;
			this.xrLabel3.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "sumSum([Credit])")
			});
			this.xrLabel3.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel3.LocationFloat = new PointFloat(313.2081f, 3.000005f);
			this.xrLabel3.Multiline = true;
			this.xrLabel3.Name = "xrLabel3";
			this.xrLabel3.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrLabel3.SizeF = new SizeF(104.4859f, 23f);
			this.xrLabel3.StylePriority.UseBorders = false;
			this.xrLabel3.StylePriority.UseBorderWidth = false;
			this.xrLabel3.StylePriority.UseFont = false;
			this.xrLabel3.StylePriority.UseTextAlignment = false;
			xrSummary2.Running = SummaryRunning.Report;
			this.xrLabel3.Summary = xrSummary2;
			this.xrLabel3.Text = "xrLabel3";
			this.xrLabel3.TextAlignment = TextAlignment.TopCenter;
			this.objectDataSource1.DataSource = typeof(JournalReportModel);
			this.objectDataSource1.Name = "objectDataSource1";
			this.xrTableRow5.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell7,
				this.xrTableCell10
			});
			this.xrTableRow5.Name = "xrTableRow5";
			this.xrTableRow5.Weight = 1.0;
			this.xrTableCell7.Multiline = true;
			this.xrTableCell7.Name = "xrTableCell7";
			this.xrTableCell7.StylePriority.UseFont = false;
			this.xrTableCell7.StylePriority.UsePadding = false;
			this.xrTableCell7.StylePriority.UseTextAlignment = false;
			this.xrTableCell7.Text = "المستخدم";
			this.xrTableCell7.TextAlignment = TextAlignment.TopLeft;
			this.xrTableCell7.Weight = 0.4965592917127513;
			this.xrTableCell10.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[UserID]")
			});
			this.xrTableCell10.Font = new Font("Segoe UI", 9f, FontStyle.Bold);
			this.xrTableCell10.Multiline = true;
			this.xrTableCell10.Name = "xrTableCell10";
			this.xrTableCell10.StylePriority.UseFont = false;
			this.xrTableCell10.Text = "xrTableCell10";
			this.xrTableCell10.Weight = 1.3680312174875988;
			base.Bands.AddRange(new Band[]
			{
				this.TopMargin,
				this.BottomMargin,
				this.Detail,
				this.DetailReport
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource1
			});
			base.DataSource = this.objectDataSource1;
			this.Font = new Font("Arial", 9.75f);
			base.Margins = new Margins(100, 100, 100, 69);
			base.PageHeight = 1169;
			base.PageWidth = 827;
			base.PaperKind = PaperKind.A4;
			this.RightToLeft = RightToLeft.Yes;
			base.RightToLeftLayout = RightToLeftLayout.Yes;
			base.Version = "20.2";
			((ISupportInitialize)this.vendorTable).EndInit();
			((ISupportInitialize)this.invoiceInfoTable).EndInit();
			((ISupportInitialize)this.xrTable2).EndInit();
			((ISupportInitialize)this.xrTable1).EndInit();
			((ISupportInitialize)this.objectDataSource1).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x04002351 RID: 9041
		private IContainer components = null;

		// Token: 0x04002352 RID: 9042
		private TopMarginBand TopMargin;

		// Token: 0x04002353 RID: 9043
		private BottomMarginBand BottomMargin;

		// Token: 0x04002354 RID: 9044
		private DetailBand Detail;

		// Token: 0x04002355 RID: 9045
		private ObjectDataSource objectDataSource1;

		// Token: 0x04002356 RID: 9046
		private DetailReportBand DetailReport;

		// Token: 0x04002357 RID: 9047
		private DetailBand Detail1;

		// Token: 0x04002358 RID: 9048
		private GroupHeaderBand GroupHeader1;

		// Token: 0x04002359 RID: 9049
		private XRTable xrTable1;

		// Token: 0x0400235A RID: 9050
		private XRTableRow xrTableRow1;

		// Token: 0x0400235B RID: 9051
		private XRTableCell xrTableCell1;

		// Token: 0x0400235C RID: 9052
		private XRTableCell xrTableCell2;

		// Token: 0x0400235D RID: 9053
		private XRTableCell xrTableCell3;

		// Token: 0x0400235E RID: 9054
		private XRTableCell xrTableCell4;

		// Token: 0x0400235F RID: 9055
		private XRTableCell xrTableCell5;

		// Token: 0x04002360 RID: 9056
		private XRTableCell xrTableCell6;

		// Token: 0x04002361 RID: 9057
		private XRLabel xrLabel10;

		// Token: 0x04002362 RID: 9058
		private XRPictureBox vendorLogo;

		// Token: 0x04002363 RID: 9059
		private XRTable vendorTable;

		// Token: 0x04002364 RID: 9060
		private XRTableRow vendorNameRow;

		// Token: 0x04002365 RID: 9061
		private XRTableCell vendorName;

		// Token: 0x04002366 RID: 9062
		private XRTableRow vendorAddressRow;

		// Token: 0x04002367 RID: 9063
		private XRTableCell vendorAddress;

		// Token: 0x04002368 RID: 9064
		private XRTableRow vendorCityRow;

		// Token: 0x04002369 RID: 9065
		private XRTableCell vendorCity;

		// Token: 0x0400236A RID: 9066
		private XRTableRow xrTableRow3;

		// Token: 0x0400236B RID: 9067
		private XRTableCell xrTableCell11;

		// Token: 0x0400236C RID: 9068
		private XRTableRow xrTableRow4;

		// Token: 0x0400236D RID: 9069
		private XRTableCell xrTableCell12;

		// Token: 0x0400236E RID: 9070
		private XRTable invoiceInfoTable;

		// Token: 0x0400236F RID: 9071
		private XRTableRow invoiceDateRow;

		// Token: 0x04002370 RID: 9072
		private XRTableCell invoiceDateCaption;

		// Token: 0x04002371 RID: 9073
		private XRTableCell invoiceDate;

		// Token: 0x04002372 RID: 9074
		private XRTableRow invoiceNumberRow;

		// Token: 0x04002373 RID: 9075
		private XRTableCell invoiceNumberCaption;

		// Token: 0x04002374 RID: 9076
		private XRTableCell invoiceNumber;

		// Token: 0x04002375 RID: 9077
		private XRBarCode xrBarCode1;

		// Token: 0x04002376 RID: 9078
		private XRTable xrTable2;

		// Token: 0x04002377 RID: 9079
		private XRTableRow xrTableRow2;

		// Token: 0x04002378 RID: 9080
		private XRTableCell xrTableCell8;

		// Token: 0x04002379 RID: 9081
		private XRTableCell xrTableCell9;

		// Token: 0x0400237A RID: 9082
		private XRTableCell xrTableCell13;

		// Token: 0x0400237B RID: 9083
		private XRTableCell xrTableCell14;

		// Token: 0x0400237C RID: 9084
		private XRTableCell xrTableCell15;

		// Token: 0x0400237D RID: 9085
		private XRTableCell xrTableCell16;

		// Token: 0x0400237E RID: 9086
		private XRPageInfo xrPageInfo2;

		// Token: 0x0400237F RID: 9087
		private XRPageInfo xrPageInfo1;

		// Token: 0x04002380 RID: 9088
		private XRLabel xrLabel1;

		// Token: 0x04002381 RID: 9089
		private XRLabel xrLabel3;

		// Token: 0x04002382 RID: 9090
		private XRLabel xrLabel2;

		// Token: 0x04002383 RID: 9091
		private GroupFooterBand GroupFooter1;

		// Token: 0x04002384 RID: 9092
		private XRLine xrLine1;

		// Token: 0x04002385 RID: 9093
		private XRLine xrLine2;

		// Token: 0x04002386 RID: 9094
		private XRTableRow xrTableRow5;

		// Token: 0x04002387 RID: 9095
		private XRTableCell xrTableCell7;

		// Token: 0x04002388 RID: 9096
		private XRTableCell xrTableCell10;
	}
}
