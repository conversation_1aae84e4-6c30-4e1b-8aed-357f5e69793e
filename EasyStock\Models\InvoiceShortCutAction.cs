﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.Models
{
    public enum InvoiceShortCutAction
    {
        [Display(Name = "Nouveau")]
        New,
        [Display(Name = "Sauvegarder")]
        Save,
        [Display(Name = "Sauvegarder et Nouveau")]
        SaveAndNew,
        [Display(Name = "Sauvegarder et Imprimer")]
        SaveAndPrint,
        [Display(Name = "Sauvegarder, Imprimer, et Nouveau")]
        SaveNewAndPrint,
        [Display(Name = "Imprimer")]
        Print,
        [Display(Name = "Supprimer")]
        Delete,
        [Display(Name = "Ajouter Code-barres")]
        FocusToBarcode,
        [Display(Name = "Sélectionner Code Client")]
        SelectCustomerID,
        [Display(Name = "Sélectionner Nom Client")]
        SelectCustomerName,
        [Display(Name = "Sélectionner Code Dépôt")]
        SelectStoreID,
        [Display(Name = "Sélectionner Nom Dépôt")]
        SelectStoreName,
        [Display(Name = "Modifier Valeur Remise")]
        SetDiscountValue,
        [Display(Name = "Modifier Pourcentage Remise")]
        SetDiscountPercentage,
        [Display(Name = "Modifier Autres Dépenses")]
        SetOtherExpenses,
        [Display(Name = "Paiement Crédit")]
        PayCredit,
        [Display(Name = "Paiement Espèce")]
        PayCash,
        [Display(Name = "Paiement Carte")]
        PayCard,
        [Display(Name = "Afficher Écran de Paiement")]
        ShowPayScreen,
        [Display(Name = "Activer/Désactiver Capture Code-barres")]
        ToggleBarcodeCatch,
        [Display(Name = "Modifier Code Facture")]
        SelectCode
    }
}
