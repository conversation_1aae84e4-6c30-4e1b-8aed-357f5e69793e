﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class ProductBalanceInStoreReportModel
    {
        [Display(Name = "Numéro")]
        public string ID { get; set; }

        [Display(Name = "Code Article")]
        public int ProductID { get; set; }

        [Display(Name = "Nom de l'article")]
        public string ProductName { get; set; }

        [Display(Name = "Code Dépôt")]
        public int StoreID { get; set; }

        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Quantité")]
        public double Quantity { get; set; }

        [Display(Name = "Solde Détail")]
        public string DetailedBalance { get; set; }

        [Display(Name = "Prix de Vente")]
        public double SellPrice { get; set; }

        [Display(Name = "Total Vente")]
        public double TotalSell
        {
            get
            {
                return this.SellPrice * this.Quantity;
            }
        }

        [Display(Name = "Prix d'Achat")]
        public double BuyPrice { get; set; }

        [Display(Name = "Total Achat")]
        public double TotalBuy
        {
            get
            {
                return this.BuyPrice * this.Quantity;
            }
        }
    }
}
