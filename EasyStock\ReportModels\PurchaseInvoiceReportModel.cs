﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
	public class PurchaseInvoiceReportModel : InvoiceReportModel
	{
		[Display(Name = "Code Fournisseur")]
		public int VendorID { get; set; }

		[StringLength(400)]
		[Display(Name = "Fournisseur")]
		public string VendorName { get; set; }

		[Display(Name = "Ville")]
		public string VendorCity { get; set; }

		[StringLength(250)]
		[Display(Name = "Adresse")]
		public string VendorAddress { get; set; }

		[StringLength(50)]
		[Display(Name = "Téléphone")]
		public string VendorPhone { get; set; }

		[Display(Name = "Mobile")]
		public string VendorMobile { get; set; }

		[Display(Name = "Utilisateur")]
		public new string UserID { get; set; }
	}
}
