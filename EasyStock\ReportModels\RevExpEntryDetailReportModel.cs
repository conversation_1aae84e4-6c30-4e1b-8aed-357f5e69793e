﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
	public class RevExpEntryDetailReportModel
	{
		[Display(Name = "N° du Compte")]
		public int AccountID { get; set; }

		[Display(Name = "Compte")]
		public string AccountName { get; set; }

		[Display(Name = "Montant")]
		public double Amount { get; set; }

		[Display(Name = "Devise")]
		public string Currancy { get; set; }

		[Display(Name = "Taux de Change")]
		public double CurrancyRate { get; set; }

		[Display(Name = "Montant Local")]
		public double LocalAmount { get; set; }

		[Display(Name = "Notes")]
		public string Notes { get; set; }
	}
}
