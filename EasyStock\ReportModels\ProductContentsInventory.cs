﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class ProductContentsInventory : CompanyInfoReportModel
    {
        [Display(Name = "Nom de l'article")]
        public int ProductName { get; set; }

        [Display(Name = "Solde Initial")]
        public double FirstQuantity { get; set; }

        [Display(Name = "Entrées")]
        public double InQuantity { get; set; }

        [Display(Name = "Sorties")]
        public double OutQuantity { get; set; }

        [Display(Name = "Solde Final")]
        public double LastQuantity { get; set; }

        [Display(Name = "Entrées Détails")]
        public ICollection<ProductContentsInventoryDetail> ProductContentsInventoryDetailsIn { get; set; }

        [Display(Name = "Sorties Détails")]
        public ICollection<ProductContentsInventoryDetail> ProductContentsInventoryDetailsOut { get; set; }
    }
}
