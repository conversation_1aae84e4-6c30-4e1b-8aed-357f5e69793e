﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("BOMCost")]
    [DisplayColumn("Afficher les coûts des matières premières")]
    public class BOMExpenses : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Numéro de la liste des matières premières")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int BOMID
        {
            get
            {
                return this.bomID;
            }
            set
            {
                base.SetProperty<int>(ref this.bomID, value, "BOMID");
            }
        }

        public BillOfMaterials BOM { get; set; }

        [Display(Name = "N° de compte")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int AccountID
        {
            get
            {
                return this.accountID;
            }
            set
            {
                base.SetProperty<int>(ref this.accountID, value, "AccountID");
            }
        }

        [Display(Name = "Coût")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double Cost
        {
            get
            {
                return this.cost;
            }
            set
            {
                base.SetProperty<double>(ref this.cost, value, "Cost");
            }
        }

        private int id;

        private int bomID;

        private int accountID;

        private double cost;
    }
}
