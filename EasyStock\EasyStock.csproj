﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DF493385-AF09-4DF2-8E13-783CA1A2ED49}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>EasyStock</RootNamespace>
    <AssemblyName>EasyStock</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <LangVersion>9.0</LangVersion>
    <ApplicationIcon>EasyStock.ico</ApplicationIcon>
    <StartupObject>
    </StartupObject>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup />
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountType.cs" />
    <Compile Include="ApplicationSettings.cs" />
    <Compile Include="BillHelper.cs" />
    <Compile Include="BillSourceType.cs" />
    <Compile Include="BillState.cs" />
    <Compile Include="Charts\BanksBalances.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\BaseChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\DueInvoices.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\GridChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\LeastProfitableProducts.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\LeastSoldProductChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\Models\DailySalesSummery.cs" />
    <Compile Include="Charts\Models\IWidget.cs" />
    <Compile Include="Charts\Models\TopSoldProductsChartModel.cs" />
    <Compile Include="Charts\MostProfitableProducts.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\SalesPerformanceDailyChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\SalesPerformanceMonthlyChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\StagentProductsCharts.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\TopSoldProductChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\TotalBanksBalances.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\XtraReport2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Charts\XtraReport2.Designer.cs">
      <DependentUpon>XtraReport2.cs</DependentUpon>
    </Compile>
    <Compile Include="Classes\AccountHelper.cs" />
    <Compile Include="Classes\BaseNotifyPropertyChangedModel.cs" />
    <Compile Include="Classes\BillsConverter.cs" />
    <Compile Include="Classes\ConvertMoneyFrench.cs" />
    <Compile Include="Classes\CurrentSession.cs" />
    <Compile Include="Classes\CustomAttributes.cs" />
    <Compile Include="Classes\LangResource.cs" />
    <Compile Include="Classes\LookAndFeelSettings.cs" />
    <Compile Include="Classes\NavigationObjects.cs" />
    <Compile Include="Classes\ProductHelper.cs" />
    <Compile Include="Classes\ProductRowDetailManger.cs" />
    <Compile Include="Classes\RequiredIfAttribute.cs" />
    <Compile Include="Classes\UserAuthentication.cs" />
    <Compile Include="Classes\Utilities.cs" />
    <Compile Include="CloseAccountType.cs" />
    <Compile Include="Controller\ChangeTrackerItem.cs" />
    <Compile Include="Controller\ERPDataContext.cs" />
    <Compile Include="Controller\UserLog.cs" />
    <Compile Include="Controls\AccountBalance.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ChartsView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\CostDistributionOption.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\DashWidgets.cs" />
    <Compile Include="Controls\FrequentlyUsedTileControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\GridPopupContainerControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\NotficationView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductUnitControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProductUnitsListControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProgressView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Controls\ProgressView.Designer.cs">
      <DependentUpon>ProgressView.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\ReportPropertiesControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\SearchView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Services.cs" />
    <Compile Include="Controls\TileNavigationControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TwoLevelTileView.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\XtraReport1.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\XtraReport1.Designer.cs">
      <DependentUpon>XtraReport1.cs</DependentUpon>
    </Compile>
    <Compile Include="CostCalculationMethod.cs" />
    <Compile Include="CostCenterRestriction.cs" />
    <Compile Include="CostDistributionOptions.cs" />
    <Compile Include="CustomControls\Models\Category.cs" />
    <Compile Include="CustomControls\Models\Product.cs" />
    <Compile Include="Enumerations.cs" />
    <Compile Include="Exteintions.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="GUI\CheckArrowLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="GUI\SalesWorkFlowUserControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="MainViews\BaseReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\BaseReportForm.Designer.cs">
      <DependentUpon>BaseReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainViews\HomeForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\HomeForm.Designer.cs">
      <DependentUpon>HomeForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainViews\HomeForms.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\HomeForms.Designer.cs">
      <DependentUpon>HomeForms.cs</DependentUpon>
    </Compile>
    <Compile Include="MainViews\HomeScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\HomeScreen.Designer.cs">
      <DependentUpon>HomeScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="MainViews\LogonForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\LogonForm.Designer.cs">
      <DependentUpon>LogonForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainViews\MasterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\MasterForm.Designer.cs">
      <DependentUpon>MasterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainViews\XtraReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\XtraReportForm.Designer.cs">
      <DependentUpon>XtraReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MethodOfPayment.cs" />
    <Compile Include="Migrations\202408041526537_2024.cs" />
    <Compile Include="Migrations\202408041526537_2024.Designer.cs">
      <DependentUpon>202408041526537_2024.cs</DependentUpon>
    </Compile>
    <Compile Include="Migrations\adCahTrnsferMdl.cs" />
    <Compile Include="Migrations\addBranchEntity.cs" />
    <Compile Include="Migrations\addCostCenter.cs" />
    <Compile Include="Migrations\addCurrentAssetsAndOwnerEqAccountSettings.cs" />
    <Compile Include="Migrations\AddDateColumnColumnInPettyCash.cs" />
    <Compile Include="Migrations\AddDueDatetoBillAndjournalDetails.cs" />
    <Compile Include="Migrations\addFieldsToSalesInvoices2.cs" />
    <Compile Include="Migrations\AddProductCodetoProductTransaction.cs" />
    <Compile Include="Migrations\AddProductCustomField.cs" />
    <Compile Include="Migrations\addReportTemplate.cs" />
    <Compile Include="Migrations\addSalesPriceOffer.cs" />
    <Compile Include="Migrations\addTaxPercToInvoicesDetails.cs" />
    <Compile Include="Migrations\addTwoAccountSettings.cs" />
    <Compile Include="Migrations\AddUseIdToJornals.cs" />
    <Compile Include="Migrations\AddUserDefualtScreen.cs" />
    <Compile Include="Migrations\AddUserIDToBillandjournalscashnoteRevExpEntry.cs" />
    <Compile Include="Migrations\AddUserIDToRevExpEntry.cs" />
    <Compile Include="Migrations\addUserLogTable.cs" />
    <Compile Include="Migrations\addUserLogTable2.cs" />
    <Compile Include="Migrations\addusertocashnotes.cs" />
    <Compile Include="Migrations\AddVendorInvoice.cs" />
    <Compile Include="Migrations\add_Branch_ToDrawerPeriod.cs" />
    <Compile Include="Migrations\add_CanChangeBranchToUserSettings.cs" />
    <Compile Include="Migrations\add_Tax_To_Revexpences.cs" />
    <Compile Include="Migrations\ChangesToPettyCashAccounts.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Migrations\Contractor_Abstract.cs" />
    <Compile Include="Migrations\DefualtUserprint.cs" />
    <Compile Include="Migrations\DrawerPeriodModels.cs" />
    <Compile Include="Migrations\GetNewCodeAbstract.cs" />
    <Compile Include="Migrations\GetNewCodeVendorInvoice.cs" />
    <Compile Include="Migrations\Initial.cs" />
    <Compile Include="Migrations\invoiceSourcesAndCostOfSoldGoods.cs" />
    <Compile Include="Migrations\InvoicesSourceMigration.cs" />
    <Compile Include="Migrations\NewCode.cs" />
    <Compile Include="Migrations\outGoingBillEntity.cs" />
    <Compile Include="Migrations\PettyCashCloseOutAmount.cs" />
    <Compile Include="Migrations\PettyCashModels.cs" />
    <Compile Include="Migrations\printedBarcodeModel.cs" />
    <Compile Include="Migrations\productLocationInStore.cs" />
    <Compile Include="Migrations\RemoveNameColumnInPettyCash.cs" />
    <Compile Include="Migrations\RenameColumnsInCashNotes.cs" />
    <Compile Include="Migrations\rename_branchs_to_store.cs" />
    <Compile Include="Migrations\SalesOrderAndOffersSates.cs" />
    <Compile Include="Migrations\SalesOrderMigration.cs" />
    <Compile Include="Migrations\SalesPriceOfferInitialMigration.cs" />
    <Compile Include="Migrations\screenFrequency.cs" />
    <Compile Include="Migrations\StockCorrectionTables.cs" />
    <Compile Include="Migrations\TaxDeclarationMigration.cs" />
    <Compile Include="Migrations\test.cs" />
    <Compile Include="Migrations\test1.cs" />
    <Compile Include="Migrations\TransactionStateAddedToEntities.cs" />
    <Compile Include="Migrations\updateContractorAbstract.cs" />
    <Compile Include="Migrations\updateOnReportTemplate.cs" />
    <Compile Include="Migrations\UpdteOnPrdutCategory.cs" />
    <Compile Include="Migrations\workorder.cs" />
    <Compile Include="Models\Account.cs" />
    <Compile Include="Models\ActualCost.cs" />
    <Compile Include="Models\ActualMaterialConsumption.cs" />
    <Compile Include="Models\Bank.cs" />
    <Compile Include="Models\Bill.cs" />
    <Compile Include="Models\BillingDetail.cs" />
    <Compile Include="Models\BillOfMaterials.cs" />
    <Compile Include="Models\BOMDetails.cs" />
    <Compile Include="Models\BOMExpenses.cs" />
    <Compile Include="Models\Branch.cs" />
    <Compile Include="Models\CashLinkType.cs" />
    <Compile Include="Models\CashNote.cs" />
    <Compile Include="Models\CashNotePaySourceType.cs" />
    <Compile Include="Models\CashNoteType.cs" />
    <Compile Include="Models\CashTransfer.cs" />
    <Compile Include="Models\CompanyInfo.cs" />
    <Compile Include="Models\ContractorAbstract.cs" />
    <Compile Include="Models\CostCenter.cs" />
    <Compile Include="Models\Currency.cs" />
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\CustomerGroup.cs" />
    <Compile Include="Models\DefaultExpenses.cs" />
    <Compile Include="Models\DefaultMaterialConsumption.cs" />
    <Compile Include="Models\Drawer.cs" />
    <Compile Include="Models\DrawerPeriod.cs" />
    <Compile Include="Models\DrawerPeriodTransSummeryItem.cs" />
    <Compile Include="Models\FrequentlyUsedScreen.cs" />
    <Compile Include="Models\GroupOfProductDiscountType.cs" />
    <Compile Include="Models\GroupOfProducts.cs" />
    <Compile Include="Models\GroupOfProductsDetails.cs" />
    <Compile Include="Models\GroupOfProductsExpireType.cs" />
    <Compile Include="Models\IBill.cs" />
    <Compile Include="Models\InvoiceDetail.cs" />
    <Compile Include="Models\InvoiceShortCut.cs" />
    <Compile Include="Models\InvoiceShortCutAction.cs" />
    <Compile Include="Models\IProductRowDetail.cs" />
    <Compile Include="Models\ISalesBill.cs" />
    <Compile Include="Models\Journal.cs" />
    <Compile Include="Models\JournalDetail.cs" />
    <Compile Include="Models\OpenBalanceBill.cs" />
    <Compile Include="Models\OutgoingBill.cs" />
    <Compile Include="Models\PayCard.cs" />
    <Compile Include="Models\PayDetail.cs" />
    <Compile Include="Models\PayMethodType.cs" />
    <Compile Include="Models\Personal.cs" />
    <Compile Include="Models\PersonalGroup.cs" />
    <Compile Include="Models\PersonalType.cs" />
    <Compile Include="Models\PettyCash.cs" />
    <Compile Include="Models\PettyCashCloseOut.cs" />
    <Compile Include="Models\PettyCashHolder.cs" />
    <Compile Include="Models\PriceList.cs" />
    <Compile Include="Models\PrintedBarcode.cs" />
    <Compile Include="Models\Product.cs" />
    <Compile Include="Models\ProductCategory.cs" />
    <Compile Include="Models\ProductColor.cs" />
    <Compile Include="Models\ProductCompany.cs" />
    <Compile Include="Models\ProductCustomField.cs" />
    <Compile Include="Models\ProductDamageBill.cs" />
    <Compile Include="Models\ProductSize.cs" />
    <Compile Include="Models\ProductStoreLocation.cs" />
    <Compile Include="Models\ProductTransaction.cs" />
    <Compile Include="Models\ProductType.cs" />
    <Compile Include="Models\ProductUnit.cs" />
    <Compile Include="Models\ProductUnitBarcode.cs" />
    <Compile Include="Models\ProductVendor.cs" />
    <Compile Include="Models\PurchaseInvoice.cs" />
    <Compile Include="Models\PurchaseReturnInvoice.cs" />
    <Compile Include="Models\ReportTemplate.cs" />
    <Compile Include="Models\RevExpEntry.cs" />
    <Compile Include="Models\RevExpEntryDetail.cs" />
    <Compile Include="Models\RevExpEntryType.cs" />
    <Compile Include="Models\SalesInvoice.cs" />
    <Compile Include="Models\SalesOrder.cs" />
    <Compile Include="Models\SalesOrderDetail.cs" />
    <Compile Include="Models\SalesPriceOffer.cs" />
    <Compile Include="Models\SalesPriceOfferDetail.cs" />
    <Compile Include="Models\SalesReturnInvoice.cs" />
    <Compile Include="Models\SimilarProduct.cs" />
    <Compile Include="Models\StockBalanceAfterCorrectionDetail.cs" />
    <Compile Include="Models\StockBalanceBeforeCorrectionDetail.cs" />
    <Compile Include="Models\StockBalanceCorrection.cs" />
    <Compile Include="Models\StockTransferBill.cs" />
    <Compile Include="Models\Store.cs" />
    <Compile Include="Models\SystemSettings.cs" />
    <Compile Include="Models\TransactionLinkModel.cs" />
    <Compile Include="Models\UnitOfMeasurement.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\UserAccessProfile.cs" />
    <Compile Include="Models\UserAccessProfileDetail.cs" />
    <Compile Include="Models\UserSettingsProfile.cs" />
    <Compile Include="Models\UserType.cs" />
    <Compile Include="Models\Vendor.cs" />
    <Compile Include="Models\VendorGroup.cs" />
    <Compile Include="Models\VendorInvoice.cs" />
    <Compile Include="Models\WorkOrder.cs" />
    <Compile Include="Models\WorkOrderDetails.cs" />
    <Compile Include="Models\WorkType.cs" />
    <Compile Include="MoneyToTextModes.cs" />
    <Compile Include="PayType.cs" />
    <Compile Include="PriceListType.cs" />
    <Compile Include="PriceOfferType.cs" />
    <Compile Include="PrintMode.cs" />
    <Compile Include="ProductTransactionType.cs" />
    <Compile Include="ProductUnitsMode.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="ReadValueMode.cs" />
    <Compile Include="RedundancyOptions.cs" />
    <Compile Include="ReportFilter.cs" />
    <Compile Include="ReportForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportForm.Designer.cs">
      <DependentUpon>ReportForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportModels\AccountsBalanceReportModel.cs" />
    <Compile Include="ReportModels\BalanceSheetReportModel.cs" />
    <Compile Include="ReportModels\BarcodeModel.cs" />
    <Compile Include="ReportModels\BillOfMaterialsReportModel.cs" />
    <Compile Include="ReportModels\BOMDEtails.cs" />
    <Compile Include="ReportModels\CashNoteReportModel.cs" />
    <Compile Include="ReportModels\CashTransferReportModel.cs" />
    <Compile Include="ReportModels\CompanyInfoReportModel.cs" />
    <Compile Include="ReportModels\Consumptions.cs" />
    <Compile Include="ReportModels\ContractorAbstractReportModel.cs" />
    <Compile Include="ReportModels\Details.cs" />
    <Compile Include="ReportModels\DrawerDailyLogReportModel.cs" />
    <Compile Include="ReportModels\IncomStatment\IncomeStatmentModel.cs" />
    <Compile Include="ReportModels\IncomStatment\IncomeStatmentModelDetial.cs" />
    <Compile Include="ReportModels\InOutModel.cs" />
    <Compile Include="ReportModels\InvoiceDetailReportModel.cs" />
    <Compile Include="ReportModels\InvoiceReportModel.cs" />
    <Compile Include="ReportModels\InvoicesPayStatus.cs" />
    <Compile Include="ReportModels\JournalDetailReportModel.cs" />
    <Compile Include="ReportModels\JournalReportModel.cs" />
    <Compile Include="ReportModels\MerchandisingAccountStatmentModel.cs" />
    <Compile Include="ReportModels\PayDetailReportModel.cs" />
    <Compile Include="ReportModels\PersonalInfoSummaryModel.cs" />
    <Compile Include="ReportModels\PettyCashCloseOutReportModel.cs" />
    <Compile Include="ReportModels\PettyCashReportModel.cs" />
    <Compile Include="ReportModels\ProductBalanceDetailedInStoreReportModel.cs" />
    <Compile Include="ReportModels\ProductBalanceInStore.cs" />
    <Compile Include="ReportModels\ProductBalanceInStoreReportModel.cs" />
    <Compile Include="ReportModels\ProductContentsInventory.cs" />
    <Compile Include="ReportModels\ProductContentsInventoryDetail.cs" />
    <Compile Include="ReportModels\ProductInOutBalanceModel.cs" />
    <Compile Include="ReportModels\ProductInOutModel.cs" />
    <Compile Include="ReportModels\ProductListModel.cs" />
    <Compile Include="ReportModels\ProductReachedReorderLevelReportModel.cs" />
    <Compile Include="ReportModels\ProductSearchModel.cs" />
    <Compile Include="ReportModels\ProductStoreTransactionReportModel.cs" />
    <Compile Include="ReportModels\ProductStoreValidityReprtModel.cs" />
    <Compile Include="ReportModels\ProductTransactionReportModel.cs" />
    <Compile Include="ReportModels\PurchaseInvoiceReportModel.cs" />
    <Compile Include="ReportModels\PurchaseReturnInvoiceReportModel.cs" />
    <Compile Include="ReportModels\PurchaseTaxesReportModel.cs" />
    <Compile Include="ReportModels\PurchaseTaxsReportModel.cs" />
    <Compile Include="ReportModels\QuantitySalesAndProduct.cs" />
    <Compile Include="ReportModels\RevExpEntryDetailReportModel.cs" />
    <Compile Include="ReportModels\RevExpEntryReportModel.cs" />
    <Compile Include="ReportModels\SalesCustomerProductsModel.cs" />
    <Compile Include="ReportModels\SalesInvoiceReportModel.cs" />
    <Compile Include="ReportModels\SalesOrderReportModel.cs" />
    <Compile Include="ReportModels\SalesPriceOfferReportModel.cs" />
    <Compile Include="ReportModels\SalesProductCategoriesCostModel.cs" />
    <Compile Include="ReportModels\SalesProductCategoriesMainModel.cs" />
    <Compile Include="ReportModels\SalesProductCategoriesSubModel.cs" />
    <Compile Include="ReportModels\SalesProductCostModel.cs" />
    <Compile Include="ReportModels\SalesReturnInvoiceReportModel.cs" />
    <Compile Include="ReportModels\SalesSummeryOfPurchaseInvoicesReportModel.cs" />
    <Compile Include="ReportModels\StatmentOfAccountModel.cs" />
    <Compile Include="ReportModels\StockBalanceCorrectionDetailReportModel.cs" />
    <Compile Include="ReportModels\StockBalanceCorrectionReportModel.cs" />
    <Compile Include="ReportModels\TotalProductTransactionsReportModel.cs" />
    <Compile Include="ReportModels\TotalPurchaseInvoicesReportModel.cs" />
    <Compile Include="ReportModels\TotalPurchaseReturnReportModel.cs" />
    <Compile Include="ReportModels\TotalSalesCustomerProductsModel.cs" />
    <Compile Include="ReportModels\TotalSalesInvoicesReportModel.cs" />
    <Compile Include="ReportModels\TotalSalesProductCategoriesModel.cs" />
    <Compile Include="ReportModels\TotalSalesReturnReportModel.cs" />
    <Compile Include="ReportModels\TrialBalanceReportModel.cs" />
    <Compile Include="ReportModels\VendorInvoiceReportModel.cs" />
    <Compile Include="ReportModels\WorkOrderReportModel.cs" />
    <Compile Include="Reports\AccountsBalanceReports.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\AccountsBalanceReports.Designer.cs">
      <DependentUpon>AccountsBalanceReports.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\BarcodeReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\BillOfMaterialsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\CashNoteReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\CashTransferReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\ContractorAbstractReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\GridReportP.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\IncomeStatmentReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Invoices\InvoicesListReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\JournalReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MasterReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PersonalOperations\CashNoteReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PersonalOperations\InvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PersonalOperations\PersonalInfoReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PersonalOperations\PersonalOperationsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PettyCashCloseOutReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PettyCashReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\ProductContentsInventoryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\ProductInOutBalanceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\ProductStoreValidityReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PurchaseInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PurchaseReturnInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PurchaseTaxReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\QuantitySalesAndProductReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\RevExpEntryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesCustomerProductsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesOrderReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesPriceOfferReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesProductCategoriesCostReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesProductCategoriesSubReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesProductCatergotiesMainReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesProductsCostReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesReturnInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SalesTaxsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalProductTransactionsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalPurchaseInvoicesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalPurchaseReturnReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalSalesCustomerProductsReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalSalesInvoicesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalSalesProductCategoriesReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\TotalSalesReturnReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\VendorInvoiceReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\WorkOrderReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ReportTypes.cs" />
    <Compile Include="ReportViews\AccountsBalanceReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\AccountsBalanceReportView.Designer.cs">
      <DependentUpon>AccountsBalanceReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\BalanceSheetReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\BalanceSheetReportView.Designer.cs">
      <DependentUpon>BalanceSheetReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\FilterForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\FilterForm.Designer.cs">
      <DependentUpon>FilterForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\IncomeStatmentReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\IncomeStatmentReportView.Designer.cs">
      <DependentUpon>IncomeStatmentReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\InvoicesListReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\InvoicesListReport.Designer.cs">
      <DependentUpon>InvoicesListReport.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\MerchandisingAccountStatment.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\MerchandisingAccountStatment.Designer.cs">
      <DependentUpon>MerchandisingAccountStatment.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\PersonalOperations.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\PersonalOperations.Designer.cs">
      <DependentUpon>PersonalOperations.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductBalanceDetailedInStores.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductBalanceDetailedInStores.Designer.cs">
      <DependentUpon>ProductBalanceDetailedInStores.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductBalanceInStores.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductBalanceInStores.Designer.cs">
      <DependentUpon>ProductBalanceInStores.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductInOutBalance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductInOutBalance.Designer.cs">
      <DependentUpon>ProductInOutBalance.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductReachedReorderLevel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductReachedReorderLevel.Designer.cs">
      <DependentUpon>ProductReachedReorderLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductSalesSummryFromPurchaseInvoicesView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductSalesSummryFromPurchaseInvoicesView.Designer.cs">
      <DependentUpon>ProductSalesSummryFromPurchaseInvoicesView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductStoreTransaction.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductStoreTransaction.Designer.cs">
      <DependentUpon>ProductStoreTransaction.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\ProductStoreValidityReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\ProductStoreValidityReportView.Designer.cs">
      <DependentUpon>ProductStoreValidityReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\PurchaseTaxesReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\PurchaseTaxesReportView.Designer.cs">
      <DependentUpon>PurchaseTaxesReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\SalesCustomerProductView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\SalesCustomerProductView.Designer.cs">
      <DependentUpon>SalesCustomerProductView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\SalesProductCategoriesCostView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\SalesProductCategoriesCostView.Designer.cs">
      <DependentUpon>SalesProductCategoriesCostView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\SalesProductCatergoriesReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\SalesProductCatergoriesReportView.Designer.cs">
      <DependentUpon>SalesProductCatergoriesReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\SalesProductCostView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\SalesProductCostView.Designer.cs">
      <DependentUpon>SalesProductCostView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\SalesTaxsReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\SalesTaxsReportView.Designer.cs">
      <DependentUpon>SalesTaxsReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\StatmentOfAccount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\StatmentOfAccount.Designer.cs">
      <DependentUpon>StatmentOfAccount.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalProductTransactionsReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalProductTransactionsReportView.Designer.cs">
      <DependentUpon>TotalProductTransactionsReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalPurchaseInvoicesReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalPurchaseInvoicesReportView.Designer.cs">
      <DependentUpon>TotalPurchaseInvoicesReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalPurchaseReturnReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalPurchaseReturnReportView.Designer.cs">
      <DependentUpon>TotalPurchaseReturnReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalSalesCustomerProductView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalSalesCustomerProductView.Designer.cs">
      <DependentUpon>TotalSalesCustomerProductView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalSalesInvoicesReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalSalesInvoicesReportView.Designer.cs">
      <DependentUpon>TotalSalesInvoicesReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalSalesProductCategoriesView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalSalesProductCategoriesView.Designer.cs">
      <DependentUpon>TotalSalesProductCategoriesView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TotalSalesReturnReportView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TotalSalesReturnReportView.Designer.cs">
      <DependentUpon>TotalSalesReturnReportView.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportViews\TrialBalance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ReportViews\TrialBalance.Designer.cs">
      <DependentUpon>TrialBalance.cs</DependentUpon>
    </Compile>
    <Compile Include="SecracyLevel.cs" />
    <Compile Include="SystemProcess.cs" />
    <Compile Include="Texting.cs" />
    <Compile Include="TransactionType.cs" />
    <Compile Include="Views\AccountsFrom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\AccountsFrom.Designer.cs">
      <DependentUpon>AccountsFrom.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\AddProductsView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\AddProductsView.Designer.cs">
      <DependentUpon>AddProductsView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\AddQuickVendor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\AddQuickVendor.Designer.cs">
      <DependentUpon>AddQuickVendor.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\BanksForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\BanksForm.Designer.cs">
      <DependentUpon>BanksForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\BillingDetailsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\BillingDetailsForm.Designer.cs">
      <DependentUpon>BillingDetailsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\BranchesView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\BranchesView.Designer.cs">
      <DependentUpon>BranchesView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashNoteInListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashNoteInListView.Designer.cs">
      <DependentUpon>CashNoteInListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashNoteInView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashNoteInView.Designer.cs">
      <DependentUpon>CashNoteInView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashNoteListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashNoteListView.Designer.cs">
      <DependentUpon>CashNoteListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashNoteOutListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashNoteOutListView.Designer.cs">
      <DependentUpon>CashNoteOutListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashNoteOutView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashNoteOutView.Designer.cs">
      <DependentUpon>CashNoteOutView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashNoteView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashNoteView.Designer.cs">
      <DependentUpon>CashNoteView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashTransferListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashTransferListView.Designer.cs">
      <DependentUpon>CashTransferListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CashTransferView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CashTransferView.Designer.cs">
      <DependentUpon>CashTransferView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CompanyInfoForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CompanyInfoForm.Designer.cs">
      <DependentUpon>CompanyInfoForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ConnectToServerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ConnectToServerForm.Designer.cs">
      <DependentUpon>ConnectToServerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ContractorAbstractListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ContractorAbstractListView.Designer.cs">
      <DependentUpon>ContractorAbstractListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ContractorAbstractView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ContractorAbstractView.Designer.cs">
      <DependentUpon>ContractorAbstractView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CostCentersView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CostCentersView.Designer.cs">
      <DependentUpon>CostCentersView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CurrenciesView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CurrenciesView.Designer.cs">
      <DependentUpon>CurrenciesView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CustomersForm.Designer.cs">
      <DependentUpon>CustomersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomersGroupsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\CustomersGroupsForm.Designer.cs">
      <DependentUpon>CustomersGroupsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\DrawerDailyLogView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\DrawerDailyLogView.Designer.cs">
      <DependentUpon>DrawerDailyLogView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\DrawerPeriodsList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\DrawerPeriodsList.Designer.cs">
      <DependentUpon>DrawerPeriodsList.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\DrawersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\DrawersForm.Designer.cs">
      <DependentUpon>DrawersForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ExpenseEntryView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ExpenseEntryView.Designer.cs">
      <DependentUpon>ExpenseEntryView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ExpenseListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ExpenseListView.Designer.cs">
      <DependentUpon>ExpenseListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\CloseDrawerPeriodForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\CloseDrawerPeriodForm.Designer.cs">
      <DependentUpon>CloseDrawerPeriodForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\DrawerPeriodForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\DrawerPeriodForm.Designer.cs">
      <DependentUpon>DrawerPeriodForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\OpenDrawerPeriodForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\OpenDrawerPeriodForm.Designer.cs">
      <DependentUpon>OpenDrawerPeriodForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\PettyCashCloseOutListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\PettyCashCloseOutListView.Designer.cs">
      <DependentUpon>PettyCashCloseOutListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\PettyCashCloseOutView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\PettyCashCloseOutView.Designer.cs">
      <DependentUpon>PettyCashCloseOutView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\PettyCashHolderView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\PettyCashHolderView.Designer.cs">
      <DependentUpon>PettyCashHolderView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\PettyCashListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\PettyCashListView.Designer.cs">
      <DependentUpon>PettyCashListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Financial\PettyCashView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Financial\PettyCashView.Designer.cs">
      <DependentUpon>PettyCashView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\frm_LogViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\frm_LogViewer.Designer.cs">
      <DependentUpon>frm_LogViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\GroupOfProductsView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\GroupOfProductsView.Designer.cs">
      <DependentUpon>GroupOfProductsView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\GroupsOfProductsListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\GroupsOfProductsListView.Designer.cs">
      <DependentUpon>GroupsOfProductsListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ImportProductFromExcelView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\InvoiceShortcutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\InvoiceShortcutForm.Designer.cs">
      <DependentUpon>InvoiceShortcutForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\JournalListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\JournalListView.Designer.cs">
      <DependentUpon>JournalListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\JournalView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\JournalView.Designer.cs">
      <DependentUpon>JournalView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\OpenBalanceBillForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\OpenBalanceBillForm.Designer.cs">
      <DependentUpon>OpenBalanceBillForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\OutgoingBillView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\OutgoingBillView.Designer.cs">
      <DependentUpon>OutgoingBillView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PayCardsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PayForm.Designer.cs">
      <DependentUpon>PayForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PersonalsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PersonalsForm.Designer.cs">
      <DependentUpon>PersonalsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PersonalsGroupsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PersonalsGroupsForm.Designer.cs">
      <DependentUpon>PersonalsGroupsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PrintBarcodeView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PrintBarcodeView.Designer.cs">
      <DependentUpon>PrintBarcodeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ProductCategoryForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ProductCategoryForm.Designer.cs">
      <DependentUpon>ProductCategoryForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ProductDamageBillForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ProductDamageBillForm.Designer.cs">
      <DependentUpon>ProductDamageBillForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ProductDamageBillsListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ProductDamageBillsListView.Designer.cs">
      <DependentUpon>ProductDamageBillsListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ProductForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ProductForm.Designer.cs">
      <DependentUpon>ProductForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Production\BillOfMaterialsListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Production\BillOfMaterialsListView.Designer.cs">
      <DependentUpon>BillOfMaterialsListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Production\BillOfMaterialsView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Production\BillOfMaterialsView.Designer.cs">
      <DependentUpon>BillOfMaterialsView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Production\WorkOrderListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Production\WorkOrderListView.Designer.cs">
      <DependentUpon>WorkOrderListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Production\WorkOrderView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Production\WorkOrderView.Designer.cs">
      <DependentUpon>WorkOrderView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ProductSearchForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ProductSearchForm.Designer.cs">
      <DependentUpon>ProductSearchForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ProductsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ProductsForm.Designer.cs">
      <DependentUpon>ProductsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PurchaseInvoiceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PurchaseInvoiceForm.Designer.cs">
      <DependentUpon>PurchaseInvoiceForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PurchaseInvoicesListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PurchaseInvoicesListView.Designer.cs">
      <DependentUpon>PurchaseInvoicesListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PurchaseReturnInvoiceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PurchaseReturnInvoiceForm.Designer.cs">
      <DependentUpon>PurchaseReturnInvoiceForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PurchaseReturnInvoicesListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PurchaseReturnInvoicesListView.Designer.cs">
      <DependentUpon>PurchaseReturnInvoicesListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\QuickAddCustomer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\QuickAddCustomer.Designer.cs">
      <DependentUpon>QuickAddCustomer.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\QuickAddProduct.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\QuickAddProduct.Designer.cs">
      <DependentUpon>QuickAddProduct.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ReportCenterView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ReportCenterView.Designer.cs">
      <DependentUpon>ReportCenterView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ReportDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ReportTemplatesView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ReportTemplatesView.Designer.cs">
      <DependentUpon>ReportTemplatesView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\RevenueEntryView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\RevenueEntryView.Designer.cs">
      <DependentUpon>RevenueEntryView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\RevenueListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\RevenueListView.Designer.cs">
      <DependentUpon>RevenueListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\RevExpEntryListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\RevExpEntryListView.Designer.cs">
      <DependentUpon>RevExpEntryListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\RevExpEntryView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\RevExpEntryView.Designer.cs">
      <DependentUpon>RevExpEntryView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalesInvoiceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalesInvoiceForm.Designer.cs">
      <DependentUpon>SalesInvoiceForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalesInvoicesListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalesInvoicesListView.Designer.cs">
      <DependentUpon>SalesInvoicesListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalesOrdersListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalesOrdersListView.Designer.cs">
      <DependentUpon>SalesOrdersListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalesPriceOffersListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalesPriceOffersListView.Designer.cs">
      <DependentUpon>SalesPriceOffersListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalesReturnInvoiceForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalesReturnInvoiceForm.Designer.cs">
      <DependentUpon>SalesReturnInvoiceForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalesReturnInvoicesListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalesReturnInvoicesListView.Designer.cs">
      <DependentUpon>SalesReturnInvoicesListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Sales\LoadFromForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Sales\LoadFromForm.Designer.cs">
      <DependentUpon>LoadFromForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Sales\POSForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Sales\POSForm.Designer.cs">
      <DependentUpon>POSForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Sales\SalesOrderView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Sales\SalesOrderView.Designer.cs">
      <DependentUpon>SalesOrderView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Sales\SalesPriceOfferView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Sales\SalesPriceOfferView.Designer.cs">
      <DependentUpon>SalesPriceOfferView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SelectSalesInvoiceLayout.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SelectSalesInvoiceLayout.Designer.cs">
      <DependentUpon>SelectSalesInvoiceLayout.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SimilarProductsSelectView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SimilarProductsSelectView.Designer.cs">
      <DependentUpon>SimilarProductsSelectView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\StockBalanceCorrectionListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\StockBalanceCorrectionListView.Designer.cs">
      <DependentUpon>StockBalanceCorrectionListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\StockBalanceCorrectionView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\StockBalanceCorrectionView.Designer.cs">
      <DependentUpon>StockBalanceCorrectionView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\StockTransferBillForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\StockTransferBillForm.Designer.cs">
      <DependentUpon>StockTransferBillForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\StockTransferBillsListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\StockTransferBillsListView.Designer.cs">
      <DependentUpon>StockTransferBillsListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\StoresView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\StoresView.Designer.cs">
      <DependentUpon>StoresView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SystemSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SystemSettingsForm.Designer.cs">
      <DependentUpon>SystemSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserAccessProfileListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserAccessProfileListView.Designer.cs">
      <DependentUpon>UserAccessProfileListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserAccessProfileView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserAccessProfileView.Designer.cs">
      <DependentUpon>UserAccessProfileView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserListView.Designer.cs">
      <DependentUpon>UserListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserLogView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserLogView.Designer.cs">
      <DependentUpon>UserLogView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserSettingsProfileListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserSettingsProfileListView.Designer.cs">
      <DependentUpon>UserSettingsProfileListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserSettingsProfileView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserSettingsProfileView.Designer.cs">
      <DependentUpon>UserSettingsProfileView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\UserView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\UserView.Designer.cs">
      <DependentUpon>UserView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\VendorInvoiceListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\VendorInvoiceListView.Designer.cs">
      <DependentUpon>VendorInvoiceListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\VendorInvoiceView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\VendorInvoiceView.Designer.cs">
      <DependentUpon>VendorInvoiceView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\VendorsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\VendorsForm.Designer.cs">
      <DependentUpon>VendorsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\VendorsGroupsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\VendorsGroupsForm.Designer.cs">
      <DependentUpon>VendorsGroupsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="XtraForm1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="XtraForm1.Designer.cs">
      <DependentUpon>XtraForm1.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Charts\GridChart.resources" />
    <EmbeddedResource Include="Charts\XtraReport2.resx">
      <DependentUpon>XtraReport2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\AccountBalance.resx">
      <DependentUpon>AccountBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\FrequentlyUsedTileControl.resx">
      <DependentUpon>FrequentlyUsedTileControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductUnitControl.resx">
      <DependentUpon>ProductUnitControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProductUnitsListControl.resx">
      <DependentUpon>ProductUnitsListControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProgressView.resx">
      <DependentUpon>ProgressView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ReportPropertiesControl.resx">
      <DependentUpon>ReportPropertiesControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\SearchView.resx">
      <DependentUpon>SearchView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TileNavigationControl.resx">
      <DependentUpon>TileNavigationControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TwoLevelTileView.resx">
      <DependentUpon>TwoLevelTileView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\XtraReport1.resx">
      <DependentUpon>XtraReport1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="GUI\SalesWorkFlowUserControl.resx">
      <DependentUpon>SalesWorkFlowUserControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainViews\HomeForm.resx">
      <DependentUpon>HomeForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainViews\HomeForms.resx">
      <DependentUpon>HomeForms.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainViews\HomeScreen.resx">
      <DependentUpon>HomeScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainViews\LogonForm.resx">
      <DependentUpon>LogonForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainViews\XtraReportForm.resx">
      <DependentUpon>XtraReportForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\202408041526537_2024.resx">
      <DependentUpon>202408041526537_2024.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\adCahTrnsferMdl.resources" />
    <EmbeddedResource Include="Migrations\addBranchEntity.resources" />
    <EmbeddedResource Include="Migrations\addCostCenter.resources" />
    <EmbeddedResource Include="Migrations\addCurrentAssetsAndOwnerEqAccountSettings.resources" />
    <EmbeddedResource Include="Migrations\AddDateColumnColumnInPettyCash.resources" />
    <EmbeddedResource Include="Migrations\AddDueDatetoBillAndjournalDetails.resources" />
    <EmbeddedResource Include="Migrations\addFieldsToSalesInvoices2.resources" />
    <EmbeddedResource Include="Migrations\AddProductCodetoProductTransaction.resources" />
    <EmbeddedResource Include="Migrations\AddProductCustomField.resources" />
    <EmbeddedResource Include="Migrations\addReportTemplate.resources" />
    <EmbeddedResource Include="Migrations\addSalesPriceOffer.resources" />
    <EmbeddedResource Include="Migrations\addTaxPercToInvoicesDetails.resources" />
    <EmbeddedResource Include="Migrations\addTwoAccountSettings.resources" />
    <EmbeddedResource Include="Migrations\AddUseIdToJornals.resources" />
    <EmbeddedResource Include="Migrations\AddUserDefualtScreen.resources" />
    <EmbeddedResource Include="Migrations\AddUserIDToBillandjournalscashnoteRevExpEntry.resources" />
    <EmbeddedResource Include="Migrations\AddUserIDToRevExpEntry.resources" />
    <EmbeddedResource Include="Migrations\addUserLogTable.resources" />
    <EmbeddedResource Include="Migrations\addUserLogTable2.resources" />
    <EmbeddedResource Include="Migrations\addusertocashnotes.resources" />
    <EmbeddedResource Include="Migrations\AddVendorInvoice.resources" />
    <EmbeddedResource Include="Migrations\add_Branch_ToDrawerPeriod.resources" />
    <EmbeddedResource Include="Migrations\add_CanChangeBranchToUserSettings.resources" />
    <EmbeddedResource Include="Migrations\add_Tax_To_Revexpences.resources" />
    <EmbeddedResource Include="Migrations\ChangesToPettyCashAccounts.resources" />
    <EmbeddedResource Include="Migrations\Contractor_Abstract.resources" />
    <EmbeddedResource Include="Migrations\DefualtUserprint.resources" />
    <EmbeddedResource Include="Migrations\DrawerPeriodModels.resources" />
    <EmbeddedResource Include="Migrations\GetNewCodeAbstract.resources" />
    <EmbeddedResource Include="Migrations\GetNewCodeVendorInvoice.resources" />
    <EmbeddedResource Include="Migrations\Initial.resources" />
    <EmbeddedResource Include="Migrations\invoiceSourcesAndCostOfSoldGoods.resources" />
    <EmbeddedResource Include="Migrations\InvoicesSourceMigration.resources" />
    <EmbeddedResource Include="Migrations\NewCode.resources" />
    <EmbeddedResource Include="Migrations\outGoingBillEntity.resources" />
    <EmbeddedResource Include="Migrations\PettyCashCloseOutAmount.resources" />
    <EmbeddedResource Include="Migrations\PettyCashModels.resources" />
    <EmbeddedResource Include="Migrations\printedBarcodeModel.resources" />
    <EmbeddedResource Include="Migrations\productLocationInStore.resources" />
    <EmbeddedResource Include="Migrations\RemoveNameColumnInPettyCash.resources" />
    <EmbeddedResource Include="Migrations\RenameColumnsInCashNotes.resources" />
    <EmbeddedResource Include="Migrations\rename_branchs_to_store.resources" />
    <EmbeddedResource Include="Migrations\SalesOrderAndOffersSates.resources" />
    <EmbeddedResource Include="Migrations\SalesOrderMigration.resources" />
    <EmbeddedResource Include="Migrations\SalesPriceOfferInitialMigration.resources" />
    <EmbeddedResource Include="Migrations\screenFrequency.resources" />
    <EmbeddedResource Include="Migrations\StockCorrectionTables.resources" />
    <EmbeddedResource Include="Migrations\TaxDeclarationMigration.resources" />
    <EmbeddedResource Include="Migrations\test.resources" />
    <EmbeddedResource Include="Migrations\test1.resources" />
    <EmbeddedResource Include="Migrations\TransactionStateAddedToEntities.resources" />
    <EmbeddedResource Include="Migrations\updateContractorAbstract.resources" />
    <EmbeddedResource Include="Migrations\updateOnReportTemplate.resources" />
    <EmbeddedResource Include="Migrations\UpdteOnPrdutCategory.resources" />
    <EmbeddedResource Include="Migrations\workorder.resources" />
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportForm.resx">
      <DependentUpon>ReportForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\AccountsBalanceReports.resx">
      <DependentUpon>AccountsBalanceReports.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BarcodeReport.resx">
      <DependentUpon>BarcodeReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BillOfMaterialsReport.resx">
      <DependentUpon>BillOfMaterialsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\CashNoteReport.resx">
      <DependentUpon>CashNoteReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\CashTransferReport.resx">
      <DependentUpon>CashTransferReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\ContractorAbstractReport.resx">
      <DependentUpon>ContractorAbstractReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\GridReportP.resx">
      <DependentUpon>GridReportP.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\IncomeStatmentReport.resx">
      <DependentUpon>IncomeStatmentReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Invoices\InvoicesListReport.resx">
      <DependentUpon>InvoicesListReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\JournalReport.resx">
      <DependentUpon>JournalReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MasterReport.resx">
      <DependentUpon>MasterReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PersonalOperations\CashNoteReport.resx">
      <DependentUpon>CashNoteReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PersonalOperations\InvoiceReport.resx">
      <DependentUpon>InvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PersonalOperations\PersonalOperationsReport.resx">
      <DependentUpon>PersonalOperationsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PettyCashCloseOutReport.resx">
      <DependentUpon>PettyCashCloseOutReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PettyCashReport.resx">
      <DependentUpon>PettyCashReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\ProductContentsInventoryReport.resx">
      <DependentUpon>ProductContentsInventoryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\ProductInOutBalanceReport.resx">
      <DependentUpon>ProductInOutBalanceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\ProductStoreValidityReport.resx">
      <DependentUpon>ProductStoreValidityReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PurchaseInvoiceReport.resx">
      <DependentUpon>PurchaseInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PurchaseTaxReport.resx">
      <DependentUpon>PurchaseTaxReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\QuantitySalesAndProductReport.resx">
      <DependentUpon>QuantitySalesAndProductReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\RevExpEntryReport.resx">
      <DependentUpon>RevExpEntryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesCustomerProductsReport.resx">
      <DependentUpon>SalesCustomerProductsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesInvoiceReport.resx">
      <DependentUpon>SalesInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesOrderReport.resx">
      <DependentUpon>SalesOrderReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesPriceOfferReport.resx">
      <DependentUpon>SalesPriceOfferReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesProductCategoriesCostReport.resx">
      <DependentUpon>SalesProductCategoriesCostReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesProductCategoriesSubReport.resx">
      <DependentUpon>SalesProductCategoriesSubReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesProductCatergotiesMainReport.resx">
      <DependentUpon>SalesProductCatergotiesMainReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesProductsCostReport.resx">
      <DependentUpon>SalesProductsCostReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesReturnInvoiceReport.resx">
      <DependentUpon>SalesReturnInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SalesTaxsReport.resx">
      <DependentUpon>SalesTaxsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalProductTransactionsReport.resx">
      <DependentUpon>TotalProductTransactionsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalPurchaseInvoicesReport.resx">
      <DependentUpon>TotalPurchaseInvoicesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalPurchaseReturnReport.resx">
      <DependentUpon>TotalPurchaseReturnReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalSalesCustomerProductsReport.resx">
      <DependentUpon>TotalSalesCustomerProductsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalSalesInvoicesReport.resx">
      <DependentUpon>TotalSalesInvoicesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalSalesProductCategoriesReport.resx">
      <DependentUpon>TotalSalesProductCategoriesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\TotalSalesReturnReport.resx">
      <DependentUpon>TotalSalesReturnReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\VendorInvoiceReport.resx">
      <DependentUpon>VendorInvoiceReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\WorkOrderReport.resx">
      <DependentUpon>WorkOrderReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\BalanceSheetReportView.resx">
      <DependentUpon>BalanceSheetReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\FilterForm.resx">
      <DependentUpon>FilterForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\IncomeStatmentReportView.resx">
      <DependentUpon>IncomeStatmentReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\MerchandisingAccountStatment.resx">
      <DependentUpon>MerchandisingAccountStatment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductBalanceDetailedInStores.resx">
      <DependentUpon>ProductBalanceDetailedInStores.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductBalanceInStores.resx">
      <DependentUpon>ProductBalanceInStores.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductInOutBalance.resx">
      <DependentUpon>ProductInOutBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductReachedReorderLevel.resx">
      <DependentUpon>ProductReachedReorderLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductSalesSummryFromPurchaseInvoicesView.resx">
      <DependentUpon>ProductSalesSummryFromPurchaseInvoicesView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductStoreTransaction.resx">
      <DependentUpon>ProductStoreTransaction.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\ProductStoreValidityReportView.resx">
      <DependentUpon>ProductStoreValidityReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\SalesProductCategoriesCostView.resx">
      <DependentUpon>SalesProductCategoriesCostView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\SalesProductCatergoriesReportView.resx">
      <DependentUpon>SalesProductCatergoriesReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\SalesProductCostView.resx">
      <DependentUpon>SalesProductCostView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\SalesTaxsReportView.resx">
      <DependentUpon>SalesTaxsReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\StatmentOfAccount.resx">
      <DependentUpon>StatmentOfAccount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\TotalProductTransactionsReportView.resx">
      <DependentUpon>TotalProductTransactionsReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\TotalPurchaseInvoicesReportView.resx">
      <DependentUpon>TotalPurchaseInvoicesReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\TotalPurchaseReturnReportView.resx">
      <DependentUpon>TotalPurchaseReturnReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\TotalSalesCustomerProductView.resx">
      <DependentUpon>TotalSalesCustomerProductView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\TotalSalesInvoicesReportView.resx">
      <DependentUpon>TotalSalesInvoicesReportView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ReportViews\TotalSalesProductCategoriesView.resources" />
    <EmbeddedResource Include="ReportViews\TotalSalesReturnReportView.resources" />
    <EmbeddedResource Include="ReportViews\TrialBalance.resx">
      <DependentUpon>TrialBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\AddProductsView.resx">
      <DependentUpon>AddProductsView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\AddQuickVendor.resx">
      <DependentUpon>AddQuickVendor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CashNoteInListView.resx">
      <DependentUpon>CashNoteInListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CashNoteView.resx">
      <DependentUpon>CashNoteView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CashTransferView.resx">
      <DependentUpon>CashTransferView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ConnectToServerForm.resx">
      <DependentUpon>ConnectToServerForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ContractorAbstractListView.resx">
      <DependentUpon>ContractorAbstractListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ContractorAbstractView.resx">
      <DependentUpon>ContractorAbstractView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CostCentersView.resx">
      <DependentUpon>CostCentersView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CurrenciesView.resx">
      <DependentUpon>CurrenciesView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\DrawerDailyLogView.resx">
      <DependentUpon>DrawerDailyLogView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\DrawerPeriodsList.resx">
      <DependentUpon>DrawerPeriodsList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Financial\DrawerPeriodForm.resx">
      <DependentUpon>DrawerPeriodForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Financial\PettyCashCloseOutListView.resx">
      <DependentUpon>PettyCashCloseOutListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Financial\PettyCashCloseOutView.resx">
      <DependentUpon>PettyCashCloseOutView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Financial\PettyCashHolderView.resx">
      <DependentUpon>PettyCashHolderView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Financial\PettyCashListView.resx">
      <DependentUpon>PettyCashListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Financial\PettyCashView.resx">
      <DependentUpon>PettyCashView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\frm_LogViewer.resx">
      <DependentUpon>frm_LogViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\GroupOfProductsView.resx">
      <DependentUpon>GroupOfProductsView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\GroupsOfProductsListView.resx">
      <DependentUpon>GroupsOfProductsListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ImportProductFromExcelView.resx">
      <DependentUpon>ImportProductFromExcelView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\InvoiceShortcutForm.resx">
      <DependentUpon>InvoiceShortcutForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\JournalListView.resx">
      <DependentUpon>JournalListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\JournalView.resx">
      <DependentUpon>JournalView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\OpenBalanceBillForm.resx">
      <DependentUpon>OpenBalanceBillForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\OutgoingBillView.resx">
      <DependentUpon>OutgoingBillView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PayForm.resx">
      <DependentUpon>PayForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PersonalsForm.resx">
      <DependentUpon>PersonalsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PersonalsGroupsForm.resx">
      <DependentUpon>PersonalsGroupsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PrintBarcodeView.resx">
      <DependentUpon>PrintBarcodeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ProductCategoryForm.resx">
      <DependentUpon>ProductCategoryForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ProductDamageBillForm.resx">
      <DependentUpon>ProductDamageBillForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ProductDamageBillsListView.resx">
      <DependentUpon>ProductDamageBillsListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ProductForm.resx">
      <DependentUpon>ProductForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Production\BillOfMaterialsListView.resx">
      <DependentUpon>BillOfMaterialsListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Production\BillOfMaterialsView.resx">
      <DependentUpon>BillOfMaterialsView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Production\WorkOrderListView.resx">
      <DependentUpon>WorkOrderListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Production\WorkOrderView.resx">
      <DependentUpon>WorkOrderView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ProductSearchForm.resx">
      <DependentUpon>ProductSearchForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ProductsForm.resx">
      <DependentUpon>ProductsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PurchaseInvoiceForm.resx">
      <DependentUpon>PurchaseInvoiceForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PurchaseInvoicesListView.resx">
      <DependentUpon>PurchaseInvoicesListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PurchaseReturnInvoiceForm.resx">
      <DependentUpon>PurchaseReturnInvoiceForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PurchaseReturnInvoicesListView.resx">
      <DependentUpon>PurchaseReturnInvoicesListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\QuickAddCustomer.resx">
      <DependentUpon>QuickAddCustomer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\QuickAddProduct.resx">
      <DependentUpon>QuickAddProduct.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ReportCenterView.resx">
      <DependentUpon>ReportCenterView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ReportDesigner.resx">
      <DependentUpon>ReportDesigner.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ReportTemplatesView.resx">
      <DependentUpon>ReportTemplatesView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\RevExpEntryView.resx">
      <DependentUpon>RevExpEntryView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalesInvoiceForm.resx">
      <DependentUpon>SalesInvoiceForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalesInvoicesListView.resx">
      <DependentUpon>SalesInvoicesListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalesOrdersListView.resx">
      <DependentUpon>SalesOrdersListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalesPriceOffersListView.resx">
      <DependentUpon>SalesPriceOffersListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalesReturnInvoiceForm.resx">
      <DependentUpon>SalesReturnInvoiceForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalesReturnInvoicesListView.resx">
      <DependentUpon>SalesReturnInvoicesListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Sales\LoadFromForm.resx">
      <DependentUpon>LoadFromForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Sales\SalesOrderView.resx">
      <DependentUpon>SalesOrderView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Sales\SalesPriceOfferView.resx">
      <DependentUpon>SalesPriceOfferView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SelectSalesInvoiceLayout.resx">
      <DependentUpon>SelectSalesInvoiceLayout.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SimilarProductsSelectView.resx">
      <DependentUpon>SimilarProductsSelectView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\StockBalanceCorrectionListView.resx">
      <DependentUpon>StockBalanceCorrectionListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\StockBalanceCorrectionView.resx">
      <DependentUpon>StockBalanceCorrectionView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\StockTransferBillForm.resx">
      <DependentUpon>StockTransferBillForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\StockTransferBillsListView.resx">
      <DependentUpon>StockTransferBillsListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\StoresView.resx">
      <DependentUpon>StoresView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SystemSettingsForm.resx">
      <DependentUpon>SystemSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserAccessProfileListView.resx">
      <DependentUpon>UserAccessProfileListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserAccessProfileView.resx">
      <DependentUpon>UserAccessProfileView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserListView.resx">
      <DependentUpon>UserListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserLogView.resx">
      <DependentUpon>UserLogView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserSettingsProfileListView.resx">
      <DependentUpon>UserSettingsProfileListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserSettingsProfileView.resx">
      <DependentUpon>UserSettingsProfileView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\UserView.resx">
      <DependentUpon>UserView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\VendorInvoiceListView.resx">
      <DependentUpon>VendorInvoiceListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\VendorInvoiceView.resx">
      <DependentUpon>VendorInvoiceView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="XtraForm1.resx">
      <DependentUpon>XtraForm1.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\DataSources\EasyStock.Models.Account.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ActualCost.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ActualMaterialConsumption.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Bank.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Bill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.BillingDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.BillOfMaterials.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.BOMDetails.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.BOMExpenses.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Branch.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.CashNote.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.CashTransfer.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.CompanyInfo.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ContractorAbstract.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.CostCenter.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Currency.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Customer.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.CustomerGroup.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.DefaultExpenses.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.DefaultMaterialConsumption.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Drawer.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.DrawerPeriod.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.DrawerPeriodTransSummeryItem.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.FrequentlyUsedScreen.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.GroupOfProducts.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.GroupOfProductsDetails.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.IBill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.InvoiceDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.InvoiceShortCut.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.IProductRowDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ISalesBill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Journal.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.JournalDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.OpenBalanceBill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.OutgoingBill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PayCard.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PayDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Personal.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PersonalGroup.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PettyCash+PettyCashType.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PettyCash.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PettyCashCloseOut.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PettyCashHolder.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PriceList.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PrintedBarcode.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Product.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductCategory.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductColor.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductCompany.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductCustomField.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductDamageBill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductSize.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductStoreLocation.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductTransaction.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductUnit.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductUnitBarcode.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ProductVendor.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PurchaseInvoice.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.PurchaseReturnInvoice.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.ReportTemplate.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.RevExpEntry.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.RevExpEntryDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SalesInvoice.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SalesOrder.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SalesOrderDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SalesPriceOffer.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SalesPriceOfferDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SalesReturnInvoice.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SimilarProduct.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.StockBalanceAfterCorrectionDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.StockBalanceBeforeCorrectionDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.StockBalanceCorrection.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.StockTransferBill.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Store.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.SystemSettings.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.TransactionLinkModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.UnitOfMeasurement.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.User.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.UserAccessProfile.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.UserAccessProfileDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.UserSettingsProfile.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.Vendor.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.VendorGroup.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.VendorInvoice.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.WorkOrder.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.WorkOrderDetails.datasource" />
    <None Include="Properties\DataSources\EasyStock.Models.WorkType.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.AccountsBalanceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.BalanceSheetReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.BarcodeModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.BillOfMaterialsReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.BOMDEtails.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.CashNoteReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.CashTransferReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.CompanyInfoReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.Consumptions.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ContractorAbstractReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.Details.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.DrawerDailyLogReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.InOutModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.InvoiceDetailReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.InvoiceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.JournalDetailReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.JournalReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.MerchandisingAccountStatmentModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PayDetailReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PersonalInfoSummaryModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PettyCashCloseOutReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PettyCashReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductBalanceDetailedInStoreReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductBalanceInStore.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductBalanceInStoreReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductContentsInventory.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductContentsInventoryDetail.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductInOutBalanceModel+Filter.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductInOutBalanceModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductInOutModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductListModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductReachedReorderLevelReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductSearchModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductStoreTransactionReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductStoreValidityReprtModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.ProductTransactionReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PurchaseInvoiceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PurchaseReturnInvoiceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PurchaseTaxesReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.PurchaseTaxsReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.QuantitySalesAndProduct.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.RevExpEntryDetailReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.RevExpEntryReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesCustomerProductsModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesInvoiceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesOrderReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesPriceOfferReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesProductCategoriesCostModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesProductCategoriesMainModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesProductCategoriesSubModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesProductCostModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesReturnInvoiceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.SalesSummeryOfPurchaseInvoicesReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.StatmentOfAccountModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.StockBalanceCorrectionDetailReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.StockBalanceCorrectionReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalProductTransactionsReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalPurchaseInvoicesReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalPurchaseReturnReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalSalesCustomerProductsModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalSalesInvoicesReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalSalesProductCategoriesModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TotalSalesReturnReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.TrialBalanceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.VendorInvoiceReportModel.datasource" />
    <None Include="Properties\DataSources\EasyStock.ReportModels.WorkOrderReportModel.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <EmbeddedResource Include="Views\AccountsFrom.resx">
      <DependentUpon>AccountsFrom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\BillingDetailsForm.resx">
      <DependentUpon>BillingDetailsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\BranchesView.resx">
      <DependentUpon>BranchesView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CashNoteListView.resx">
      <DependentUpon>CashNoteListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\CompanyInfoForm.resx">
      <DependentUpon>CompanyInfoForm.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="app.manifest" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EasyStock.AC\EasyStock.AC.csproj">
      <Project>{193CCA67-AA5B-46E6-8C50-7EFDFE8EAB31}</Project>
      <Name>EasyStock.AC</Name>
    </ProjectReference>
    <ProjectReference Include="..\EasyStock.Common\EasyStock.Common.csproj">
      <Project>{09d55fd4-2e33-4bf6-965c-f7f7fe7fe0b2}</Project>
      <Name>EasyStock.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\EasyStock.HR\EasyStock.HR.csproj">
      <Project>{039224d9-0485-4303-842a-06b5eefdf284}</Project>
      <Name>EasyStock.HR</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\bo_address.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\fontsize.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\selecttool_pantool.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\fittopage.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\refreshallpivottable.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\filterquery.svg" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="EasyStock.ico" />
    <None Include="Resources\lmc.png" />
    <None Include="Resources\tableconverttorange.svg" />
    <None Include="Resources\sales.svg" />
    <None Include="Resources\convertto.svg" />
    <None Include="Resources\actions_zoom2.svg" />
    <None Include="Resources\business_calculator.svg" />
    <None Include="Resources\import.svg" />
    <None Include="Resources\exportas.svg" />
    <None Include="Resources\duplicatevalues1.svg" />
    <None Include="Resources\copy.svg" />
    <None Include="Resources\printcollated.svg" />
    <None Include="Resources\editwrappoints.svg" />
    <None Include="Resources\checkbox.svg" />
    <None Include="Resources\bo_address1.svg" />
    <None Include="Resources\compressweekend.svg" />
    <None Include="Resources\touchmode_32x32.png" />
    <None Include="Resources\touchmode_16x16.png" />
    <None Include="Resources\viewsettings.svg" />
    <None Include="Resources\duplicatevalues.svg" />
    <None Include="Resources\enablesearch.svg" />
    <None Include="Resources\electronics_keyboard.svg" />
    <None Include="Resources\replace.svg" />
    <None Include="Resources\showcontainerheader.svg" />
    <None Include="Resources\new.svg" />
    <None Include="Resources\preview.svg" />
    <None Include="Resources\security_unlock.svg" />
    <None Include="Resources\private.svg" />
    <None Include="Resources\open2.svg" />
    <None Include="Resources\time.svg" />
    <None Include="Resources\printlayoutview.svg" />
    <None Include="Resources\saveas.svg" />
    <None Include="Resources\electronics_desktopmac.svg" />
    <None Include="Resources\update.svg" />
    <None Include="Resources\bo_product_group.svg" />
    <None Include="Resources\select.svg" />
    <None Include="Resources\actions_zoom1.svg" />
    <None Include="Resources\selectdatamember.svg" />
    <None Include="Resources\verticallines.svg" />
    <None Include="Resources\hide_16x16.png" />
    <None Include="Resources\show_16x16.png" />
    <None Include="Resources\actions_zoom.svg" />
    <None Include="Resources\print.svg" />
    <None Include="Resources\delete.svg" />
    <None Include="Resources\save1.svg" />
    <None Include="Resources\actions_edit.svg" />
    <None Include="Resources\save.svg" />
    <None Include="Resources\exporttoxls.svg" />
    <None Include="Resources\Untitled-2AAAAAAhaaaaaaay.png" />
    <None Include="Resources\3d glass window logo mockup.png" />
    <None Include="Resources\clearheaderandfooter.svg" />
    <None Include="Resources\managedatasource.svg" />
    <None Include="Resources\actions_remove.svg" />
    <None Include="Resources\actions_add.svg" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="BCrypt.Net-Next, Version=4.0.3.0, Culture=neutral, PublicKeyToken=1e11be04b6288443, processorArchitecture=MSIL">
      <HintPath>..\packages\BCrypt.Net-Next.4.0.3\lib\net472\BCrypt.Net-Next.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Charts.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.DataAccess.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v19.2.UI, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Diagram.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Dialogs.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Images.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v19.2.Drawing, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.PivotGrid.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v19.2.Export, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v19.2.UI, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v19.2.Extensions, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v19.2.UI, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v19.2.Wizard, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraDiagram.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraDialogs.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGauges.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraNavBar.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPivotGrid.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPrinting.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v19.2.CodeCompletion, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v19.2.Extensions, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraRichEdit.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraTreeList.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraVerticalGrid.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="FoxLearn.License">
      <HintPath>..\..\..\Downloads\FoxLearn.License.dll</HintPath>
    </Reference>
    <Reference Include="protobuf-net, Version=3.0.0.0, Culture=neutral, PublicKeyToken=257b51d87d2e4d67, processorArchitecture=MSIL">
      <HintPath>..\packages\protobuf-net.3.2.30\lib\net462\protobuf-net.dll</HintPath>
    </Reference>
    <Reference Include="protobuf-net.Core, Version=3.0.0.0, Culture=neutral, PublicKeyToken=257b51d87d2e4d67, processorArchitecture=MSIL">
      <HintPath>..\packages\protobuf-net.Core.3.2.30\lib\net462\protobuf-net.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Collections.Immutable.8.0.0\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Deployment">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="Vip.Notification, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Vip.Notification.1.0.4\lib\net45\Vip.Notification.dll</HintPath>
    </Reference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>