﻿using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class SalesTaxsReportView : XtraReportForm
    {
        public SalesTaxsReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Customer
        })
        {
            this.InitializeComponent();
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                SalesTaxsReport rpt = new SalesTaxsReport();
                ICollection<PurchaseTaxsReportModel> dataSource = this.GetData();
                rpt.DataSource = dataSource;
                rpt.SetCompanyInfo(dataSource.FirstOrDefault<PurchaseTaxsReportModel>());
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<PurchaseTaxsReportModel> GetData()
        {
            int[] CustomerIDs = base.FiltersForm.Customers.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<SalesInvoice> filterdInvoices = context.SalesInvoices.Where((SalesInvoice inv) => DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate);
                IQueryable<RevExpEntry> filterdRevunue = from detail in context.RevExpEntries.Include((RevExpEntry x) => x.Personal)
                                                         where detail.Taxable && (int)detail.EntryType == 8 && DbFunctions.TruncateTime(detail.DateTime) >= FiltersForm.startDate && DbFunctions.TruncateTime(detail.DateTime) <= FiltersForm.endtDate
                                                         select detail;
                if (CustomerIDs != null && CustomerIDs.Count() > 0)
                {
                    filterdInvoices = filterdInvoices.Where((SalesInvoice c) => CustomerIDs.Contains(c.CustomerID));
                }
                List<PurchaseTaxsReportModel> data = (from inv in filterdInvoices
                                                      from cus in context.Customers.Where((Customer customer) => customer.ID == inv.CustomerID).DefaultIfEmpty()
                                                      select new PurchaseTaxsReportModel
                                                      {
                                                          InvoicesCode = inv.Code,
                                                          InvoiceDate = inv.Date,
                                                          TaxAmount = inv.Tax,
                                                          Discount = inv.Discount,
                                                          Net = inv.Total + inv.Tax + inv.OtherExpenses - inv.Discount,
                                                          Total = inv.Total,
                                                          Name = ((cus != null) ? cus.Name : ""),
                                                          TaxFileNumber = ((cus != null) ? cus.TaxFileNumber : "")
                                                      }).ToList();
                IEnumerable<PurchaseTaxsReportModel> data2 = from detail in filterdRevunue.ToList()
                                                             select new PurchaseTaxsReportModel
                                                             {
                                                                 InvoicesCode = detail.InvoiceCode,
                                                                 InvoiceDate = detail.DateTime,
                                                                 TaxAmount = detail.TaxValue,
                                                                 Discount = detail.DiscountValue,
                                                                 Net = detail.TotalAfterTax,
                                                                 Total = detail.Total,
                                                                 Name = ((detail.Personal != null) ? detail.Personal.Name : ""),
                                                                 TaxFileNumber = ((detail.Personal != null) ? detail.Personal.TaxFileNumber : "")
                                                             };
                data.AddRange(data2.ToList());
                return data;
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
        private void SalesTaxsReportView_Load(object sender, EventArgs e)
        {
        }
    }
}
