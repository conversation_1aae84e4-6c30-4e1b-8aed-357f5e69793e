﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class ProductReachedReorderLevelReportModel
    {
        [Display(Name = "N°")]
        public string ID { get; set; }

        [Display(Name = "Code Article")]
        public int ProductID { get; set; }

        [Display(Name = "Article")]
        public string ProductName { get; set; }

        [Display(Name = "Code Dépôt")]
        public int StoreID { get; set; }

        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Quantité")]
        public double Quantity { get; set; }

        [Display(Name = "Seuil de commande")]
        public double ReorderLevel { get; set; }

        [Display(Name = "Solde détaillé")]
        public string DetailedBalance { get; set; }
    }
}
