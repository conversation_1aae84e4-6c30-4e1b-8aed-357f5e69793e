﻿using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports.PersonalOperations;
using EasyStock.Views;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class PersonalOperations : XtraReportForm
    {
        public PersonalOperations(PersonalType _personalType) : base(new ReportFilter[]
        {
            (_personalType == PersonalType.Customer) ? ReportFilter.Customer : ReportFilter.Vendor,
            ReportFilter.Date
        })
        {
            this.InitializeComponent();
            base.FiltersForm.Customers.MultiSelect = false;
            base.FiltersForm.Vendors.MultiSelect = false;
            base.FiltersForm.ItemForVendors.Text = "Fournisseur";
            base.FiltersForm.ItemForCustomers.Text = "Client";
            this.personalType = _personalType;
            bool flag = this.personalType == PersonalType.Customer;
            if (flag)
            {
                this.Text = "État des opérations du client";
            }
            else
            {
                this.Text = "État des opérations du fournisseur";
            }
        }


        private DateTime? endtDate
        {
            get
            {
                bool hasEndtDate = base.FiltersForm.hasEndtDate;
                DateTime? result;
                if (hasEndtDate)
                {
                    result = new DateTime?(base.FiltersForm.ToDate.DateTime.Date);
                }
                else
                {
                    result = null;
                }
                return result;
            }
        }

        private DateTime? startDate
        {
            get
            {
                bool hasStartDate = base.FiltersForm.hasStartDate;
                DateTime? result;
                if (hasStartDate)
                {
                    result = new DateTime?(base.FiltersForm.FromDate.DateTime.Date);
                }
                else
                {
                    result = null;
                }
                return result;
            }
        }

        internal override bool ValidateFilters()
        {
            bool flag = this.personalType == PersonalType.Customer;
            bool result;
            if (flag)
            {
                IList<int> editValue = base.FiltersForm.Customers.EditValue;
                bool flag2 = ((editValue != null) ? editValue.Count : 0) == 0;
                if (flag2)
                {
                    base.FiltersForm.Customers.ErrorText = "Vous devez sélectionner un client";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            else
            {
                IList<int> editValue2 = base.FiltersForm.Vendors.EditValue;
                bool flag3 = ((editValue2 != null) ? editValue2.Count : 0) == 0;
                if (flag3)
                {
                    base.FiltersForm.Vendors.ErrorText = "Vous devez sélectionner un fournisseur";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }

            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                PersonalOperationsReport rpt = new PersonalOperationsReport();
                rpt.objectDataSource1.DataSource = new CompanyInfoReportModel();
                rpt.SetCompanyInfo(rpt.objectDataSource1.DataSource as CompanyInfoReportModel);
                int account = (this.personalType == PersonalType.Customer) ? base.FiltersForm.CustomerID : base.FiltersForm.VendorID;
                PersonalInfoSummaryModel infoModel = new PersonalInfoSummaryModel();
                Personal personal = this.db.Personals.Include((Personal x) => x.Account).SingleOrDefault((Personal x) => x.ID == account);
                infoModel.ID = personal.ID;
                infoModel.Name = personal.Name;
                infoModel.Phone = personal.Phone;
                infoModel.Address = personal.Address;
                infoModel.Mobile = personal.Mobile;
                infoModel.City = personal.City;
                PersonalType personalType = this.personalType;
                PersonalType personalType2 = personalType;
                if (personalType2 != PersonalType.Customer)
                {
                    if (personalType2 == PersonalType.Vendor)
                    {
                        IQueryable<PurchaseInvoice> pq = this.db.PurchaseInvoices.AsQueryable<PurchaseInvoice>();
                        bool flag2 = this.startDate != null;
                        if (flag2)
                        {
                            pq = (from x in pq
                                  where DbFunctions.TruncateTime((DateTime?)x.Date) >= this.startDate
                                  select x).AsQueryable<PurchaseInvoice>();
                        }
                        bool flag3 = this.endtDate != null;
                        if (flag3)
                        {
                            pq = (from x in pq
                                  where DbFunctions.TruncateTime((DateTime?)x.Date) <= this.endtDate
                                  select x).AsQueryable<PurchaseInvoice>();
                        }
                        List<int> Purchase = (from x in pq
                                              where x.VendorID == this.FiltersForm.VendorID
                                              select x.ID).ToList<int>();
                        bool flag4 = Purchase.Count > 0;
                        if (flag4)
                        {
                            List<PurchaseInvoiceReportModel> SalesInvoiceForm = PurchaseInvoiceForm.GetPrintDataSource(Purchase);
                            rpt.xrSubreportSalesInvoice.ReportSource = new InvoiceReport(SalesInvoiceForm, "Facture d'achat");
                            infoModel.TotalInvoices = SalesInvoiceForm.Sum((PurchaseInvoiceReportModel x) => new double?(x.Total)).GetValueOrDefault();
                        }
                        IQueryable<SalesReturnInvoice> prq = this.db.SalesReturnInvoices.AsQueryable<SalesReturnInvoice>();
                        bool flag5 = this.startDate != null;
                        if (flag5)
                        {
                            prq = (from x in prq
                                   where DbFunctions.TruncateTime((DateTime?)x.Date) >= this.startDate
                                   select x).AsQueryable<SalesReturnInvoice>();
                        }
                        bool flag6 = this.endtDate != null;
                        if (flag6)
                        {
                            prq = (from x in prq
                                   where DbFunctions.TruncateTime((DateTime?)x.Date) <= this.endtDate
                                   select x).AsQueryable<SalesReturnInvoice>();
                        }
                        List<int> PurchaseReturn = (from x in prq
                                                    where x.CustomerID == this.FiltersForm.VendorID
                                                    select x.ID).ToList<int>();
                        bool flag7 = PurchaseReturn.Count > 0;
                        if (flag7)
                        {
                            List<PurchaseReturnInvoiceReportModel> SalesReturnInvoiceForm = PurchaseReturnInvoiceForm.GetPrintDataSource(PurchaseReturn);
                            rpt.xrSubreportSalesReturnInvoice.ReportSource = new InvoiceReport(SalesReturnInvoiceForm, "Facture de retour d'achat");
                            infoModel.TotalReturnInvoices = SalesReturnInvoiceForm.Sum((PurchaseReturnInvoiceReportModel x) => new double?(x.Total)).GetValueOrDefault();
                        }
                    }
                }
                else
                {
                    IQueryable<SalesInvoice> sq = this.db.SalesInvoices.AsQueryable<SalesInvoice>();
                    bool flag8 = this.startDate != null;
                    if (flag8)
                    {
                        sq = (from x in sq
                              where DbFunctions.TruncateTime((DateTime?)x.Date) >= this.startDate
                              select x).AsQueryable<SalesInvoice>();
                    }
                    bool flag9 = this.endtDate != null;
                    if (flag9)
                    {
                        sq = (from x in sq
                              where DbFunctions.TruncateTime((DateTime?)x.Date) <= this.endtDate
                              select x).AsQueryable<SalesInvoice>();
                    }
                    List<int> Sales = (from x in sq
                                       where x.CustomerID == this.FiltersForm.CustomerID
                                       select x.ID).ToList<int>();
                    bool flag10 = Sales.Count > 0;
                    if (flag10)
                    {
                        List<SalesInvoiceReportModel> SalesInvoiceForm2 = SalesInvoiceForm.GetPrintDataSource(Sales);
                        rpt.xrSubreportSalesInvoice.ReportSource = new InvoiceReport(SalesInvoiceForm2, "Facture de vente");
                        infoModel.TotalInvoices = SalesInvoiceForm2.Sum((SalesInvoiceReportModel x) => new double?(x.Total)).GetValueOrDefault();
                    }
                    IQueryable<SalesReturnInvoice> srq = this.db.SalesReturnInvoices.AsQueryable<SalesReturnInvoice>();
                    bool flag11 = this.startDate != null;
                    if (flag11)
                    {
                        srq = (from x in srq
                               where DbFunctions.TruncateTime((DateTime?)x.Date) >= this.startDate
                               select x).AsQueryable<SalesReturnInvoice>();
                    }
                    bool flag12 = this.endtDate != null;
                    if (flag12)
                    {
                        srq = (from x in srq
                               where DbFunctions.TruncateTime((DateTime?)x.Date) <= this.endtDate
                               select x).AsQueryable<SalesReturnInvoice>();
                    }
                    List<int> SalesReturn = (from x in srq
                                             where x.CustomerID == this.FiltersForm.CustomerID
                                             select x.ID).ToList<int>();
                    bool flag13 = SalesReturn.Count > 0;
                    if (flag13)
                    {
                        List<SalesReturnInvoiceReportModel> SalesReturnInvoiceForm2 = SalesReturnInvoiceForm.GetPrintDataSource(SalesReturn);
                        rpt.xrSubreportSalesReturnInvoice.ReportSource = new InvoiceReport(SalesReturnInvoiceForm2, "Facture de retour de vente");
                        infoModel.TotalReturnInvoices = SalesReturnInvoiceForm2.Sum((SalesReturnInvoiceReportModel x) => new double?(x.Total)).GetValueOrDefault();
                    }
                }
                IQueryable<CashNote> CashNoteIn = (from x in this.db.CashNotes
                                                   where x.PersonalID == account && (int)x.PersonalType == (int)this.personalType && (int)x.Type == 6
                                                   select x).AsQueryable<CashNote>();
                bool flag14 = this.startDate != null;
                if (flag14)
                {
                    CashNoteIn = (from x in CashNoteIn
                                  where DbFunctions.TruncateTime((DateTime?)x.Date) >= this.startDate
                                  select x).AsQueryable<CashNote>();
                }
                bool flag15 = this.endtDate != null;
                if (flag15)
                {
                    CashNoteIn = (from x in CashNoteIn
                                  where DbFunctions.TruncateTime((DateTime?)x.Date) <= this.endtDate
                                  select x).AsQueryable<CashNote>();
                }
                bool flag16 = CashNoteIn.Count<CashNote>() > 0;
                if (flag16)
                {
                    rpt.xrSubreportCashNoteIn.ReportSource = new CashNoteReport((from x in CashNoteIn
                                                                                 select x.ID).ToList<int>(), "Reçu de paiement");
                }
                IQueryable<CashNote> CashNoteOut = (from x in this.db.CashNotes
                                                    where x.PersonalID == account && (int)x.PersonalType == (int)this.personalType && (int)x.Type == 7
                                                    select x).AsQueryable<CashNote>();
                bool flag17 = this.startDate != null;
                if (flag17)
                {
                    CashNoteOut = (from x in CashNoteOut
                                   where DbFunctions.TruncateTime((DateTime?)x.Date) >= this.startDate
                                   select x).AsQueryable<CashNote>();
                }
                bool flag18 = this.endtDate != null;
                if (flag18)
                {
                    CashNoteOut = (from x in CashNoteOut
                                   where DbFunctions.TruncateTime((DateTime?)x.Date) <= this.endtDate
                                   select x).AsQueryable<CashNote>();
                }
                bool flag19 = CashNoteOut.Count<CashNote>() > 0;
                if (flag19)
                {
                    rpt.xrSubreportCashNoteOut.ReportSource = new CashNoteReport((from x in CashNoteOut
                                                                                  select x.ID).ToList<int>(), "Reçu de règlement");
                }
                infoModel.TotalCashIn = ((CashNoteIn != null) ? CashNoteIn.Sum((CashNote x) => (double?)x.TotalPaid) : null).GetValueOrDefault();
                infoModel.TotalCashOut = ((CashNoteOut != null) ? CashNoteOut.Sum((CashNote x) => (double?)x.TotalPaid) : null).GetValueOrDefault();
                double balance = personal.Account.GetRawBalance();
                bool flag20 = balance > 0.0;
                if (flag20)
                {
                    infoModel.BalanceDebit = balance;
                }
                else
                {
                    infoModel.BalanceCredit = balance;
                }
                rpt.xrSubreportInfo.ReportSource = new PersonalInfoReport(infoModel);
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private PersonalType personalType;
    }
}
