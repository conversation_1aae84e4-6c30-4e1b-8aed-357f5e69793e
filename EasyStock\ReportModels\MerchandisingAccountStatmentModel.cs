﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    internal class MerchandisingAccountStatmentModel
    {
        [Display(Name = "N°")]
        public int Index { get; set; }

        [Display(Name = "Compte")]
        public string Account { get; set; }

        [Display(Name = "Débit")]
        public double Debit { get; set; }

        [Display(Name = "Crédit")]
        public double Credit { get; set; }
    }
}
