﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public abstract class ProductTransactionReportModel
    {
        [Display(Name = "Code article")]
        public int ProductID { get; set; }

        [Display(Name = "Nom de l'article")]
        public string ProductName { get; set; }

        [Display(Name = "Code-barres")]
        public string ProductCode { get; set; }

        [Display(Name = "Unité")]
        public string UnitName { get; set; }

        [Display(Name = "Série")]
        public string Serial { get; set; }

        [Display(Name = "Couleur")]
        public string Color { get; set; }

        [Display(Name = "Taille")]
        public string Size { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Quantité")]
        [Range(0.001, 1.7976931348623157E+308, ErrorMessage = "La quantité doit être supérieure à 0.001")]
        public double Quantity { get; set; }

        [Display(Name = "Prix")]
        public double Price { get; set; }

        [Display(Name = "Montant Total")]
        public double Total
        {
            get
            {
                return this.Price * this.Quantity;
            }
        }
    }
}
