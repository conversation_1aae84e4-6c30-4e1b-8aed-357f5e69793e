﻿using DevExpress.XtraRichEdit.Import.OpenXml;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace EasyStock.ReportModels
{

    public class CashNoteReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Code de la personne")]
        public int PersonalID { get; set; }

        [Display(Name = "Personne")]
        public string PersonalName { get; set; }

        [Display(Name = "Type de personne")]
        public PersonalType PersonalType { get; set; }

        [Display(Name = "Numéro")]
        public int ID { get; set; }

        [Display(Name = "Code")]
        public string Code { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Facture")]
        public int? InvoiceID { get; set; }

        [Display(Name = "Lié à la facture")]
        public CashLinkType InvoicesType { get; set; }

        [Display(Name = "Notes")]
        public string Note { get; set; }

        [Display(Name = "Utilisateur", GroupName = "Données de la facture")]
        public string UserID { get; set; }

        [Display(Name = "Total payé")]
        public double TotalPaid { get; set; }

        [Display(Name = "Type de document")]
        public CashNoteType Type { get; set; }

        [Display(Name = "Paiements", GroupName = "Règlement")]
        public ICollection<PayDetailReportModel> PayDetails { get; set; }

        [Display(Name = "Total payé en lettres")]
        public string TotalPaidText { get; set; }

        public static List<CashNoteReportModel> GetPrintDataSource(IList<int> Ids)
        {
            ERPDataContext context = new ERPDataContext();
            try
            {
              
                List<CashNoteReportModel> q1 = (from i in context.CashNotes
                                                from p in context.Personals.Where((Personal x) => i.PersonalID == x.ID && (int)i.PersonalType != 2).DefaultIfEmpty()
                                                from a in context.Accounts.Where((Account x) => i.PersonalID == x.ID && (int)i.PersonalType == 2).DefaultIfEmpty()
                                                join b in context.Branches on i.BranchID equals b.ID
                                                join us in context.Users on i.UserID equals us.ID
                                                where Ids.Contains(i.ID)
                select new CashNoteReportModel
                                                {
                                                    UserID = us.Name,
                                                    BranchAddress = b.Address,
                                                    BranchCity = b.City,
                                                    Code = i.Code,
                                                    BranchName = b.Name,
                                                    BranchPhone = b.Phone,
                                                    Date = i.Date,
                                                    Discount = i.Discount,
                                                    Note = i.Note,
                                                    ID = i.ID,
                                                    InvoiceID = i.LinkID,
                                                    InvoicesType = i.LinkType,
                                                    PersonalID = i.PersonalID,
                                                    BranchID = i.BranchID,
                                                    PersonalName = ((p != null) ? p.Name : a.Name),
                                                    PersonalType = i.PersonalType,
                                                    Type = i.Type,
                                                    TotalPaid = i.TotalPaid,
                                                    PayDetails = (from x in context.PayDetails
                                                                  where x.SourceID == i.ID && (int)x.SourceType == (int)(((int)i.Type == 6) ? SystemProcess.CashNoteIn : SystemProcess.CashNoteOut)
                                                                  select x into pd
                                                                  join cr in context.Currencies on pd.CurrancyID equals cr.ID
                                                                  from b in context.BillingDetails.Where((BillingDetail x) => x.ID == pd.MethodID && (int)pd.MethodType != 2).DefaultIfEmpty()
                                                                  from a in context.Accounts.Where((Account x) => x.ID == pd.MethodID && (int)pd.MethodType == 2).DefaultIfEmpty()
                                                                  select new PayDetailReportModel
                                                                  {
                                                                      Amount = pd.Amount,
                                                                      Currancy = cr.Name,
                                                                      CurrancyRate = pd.CurrancyRate,
                                                                      MethodType = pd.MethodType,
                                                                      MethodName = ((b != null) ? b.Name : a.Name)
                                                                  }).ToList()
                                                } into x
                                                orderby x.Date
                                                select x).ToList();
                q1.ForEach(delegate (CashNoteReportModel x)
                {
                    x.TotalPaidText = Texting.ConvertMoneyToText(x.TotalPaid.ToString(), 1);
                });
                return q1;
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
    }
}
