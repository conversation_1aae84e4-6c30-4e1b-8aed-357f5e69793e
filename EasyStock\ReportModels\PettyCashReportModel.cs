﻿using System;
using System.ComponentModel.DataAnnotations;
using EasyStock.Models;

namespace EasyStock.ReportModels
{
    public class PettyCashReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Numéro")]
        public int ID { get; set; }

        [Display(Name = "Succursale")]
        public new string BranchName { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Titulaire")]
        public string HolderName { get; set; }

        [Display(Name = "Type de Caisse")]
        public PettyCash.PettyCashType Type { get; set; }

        [Display(Name = "Remarques")]
        public string Notes { get; set; }

        [Display(Name = "Clôturé")]
        public bool IsClosed { get; set; }

        [Display(Name = "Montant")]
        public double Amount { get; set; }

        [Display(Name = "Centre de Coût")]
        public string CostCenter { get; set; }

        [Display(Name = "Devise")]
        public string Currency { get; set; }
    }
}
