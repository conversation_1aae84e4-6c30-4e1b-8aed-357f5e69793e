﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class SalesInvoiceReportModel : InvoiceReportModel
    {
        [Display(Name = "Code du client")]
        public int CustomerID { get; set; }

        [StringLength(400)]
        [Display(Name = "Nom du client")]
        public string CustomerName { get; set; }

        [Display(Name = "Ville du client")]
        public string CustomerCity { get; set; }

        [StringLength(250)]
        [Display(Name = "Adresse du client")]
        public string CustomerAddress { get; set; }

        [StringLength(50)]
        [Display(Name = "Téléphone du client")]
        public string CustomerPhone { get; set; }

        [Display(Name = "Mobile du client")]
        public string CustomerMobile { get; set; }

        [Display(Name = "Numéro fiscal du client")]
        public string CustomerTaxNumber { get; set; }

        [Display(Name = "Numéro de bon de livraison")]
        public string DeliveryOrderNumber { get; set; }

        [Display(Name = "Date de livraison")]
        public DateTime? PostDate { get; set; }

        [Display(Name = "Lieu de livraison")]
        public string DropOffLocation { get; set; }

        [Display(Name = "Adresse de livraison")]
        public string DropOffAddress { get; set; }

        [Display(Name = "Numéro de commande")]
        public string OrderNumber { get; set; }

        [Display(Name = "Type de livraison")]
        public string DropOffType { get; set; }
    }
}
