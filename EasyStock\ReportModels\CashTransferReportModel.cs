﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
	public class CashTransferReportModel : CompanyInfoReportModel
	{
		[Display(Name = " Numéro ")]
		public int ID { get; set; }

		[Display(Name = "<PERSON><PERSON><PERSON>ô<PERSON>")]
		public string FromStore { get; set; }

		[Display(Name = "Compte de Départ")]
		public string FromAccount { get; set; }

		[Display(Name = "Compte de Destination")]
		public string ToAccount { get; set; }

		[Display(Name = "Devise")]
		public string Currency { get; set; }

		[Display(Name = "Taux de Change")]
		public double exchangerate { get; set; }

		[Display(Name = "Montant")]
		public double Amount { get; set; }

		[Display(Name = "Montant Local")]
		public double LocalAmount { get; set; }

		[Display(Name = "Date")]
		public DateTime Date { get; set; }

		[Display(Name = "Notes")]
		public string Notes { get; set; }
	}
}
