﻿using DevExpress.LookAndFeel;
using DevExpress.Utils;
using DevExpress.Utils.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controls;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using EasyStock.Views;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Drawing;
using System.Linq;

namespace EasyStock.ReportViews
{
    public partial class ProductBalanceInStores : ReportForm
    {
        public ProductBalanceInStores() : base(new ReportFilter[]
        {
            ReportFilter.Product,
            ReportFilter.Store,
            ReportFilter.Date
        }, typeof(ProductBalanceInStoreReportModel))
        {
            this.InitializeComponent();
            this.gridView1.RowCellStyle += this.GridView1_RowCellStyle;
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.gridView1.DataSourceChanged += this.GridView1_DataSourceChanged;
            this.gridView1.CustomColumnDisplayText += this.GridView1_CustomColumnDisplayText;
            base.FiltersForm.ItemForFromDate.Text = "Date";
            base.FiltersForm.ItemForToDate.Visibility = LayoutVisibility.Never;
            base.FiltersForm.FromDate.EditValue = DateTime.Now;
            this.AddTextBoxes();
        }

        private void GridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "DetailedBalance" && gridView1.GetRowCellValue(gridView1.GetRowHandle(e.ListSourceRowIndex), "Quantity") is double qty && gridView1.GetRowCellValue(gridView1.GetRowHandle(e.ListSourceRowIndex), "ProductID") is int id)
            {
                e.DisplayText = ProductHelper.GetDetailedBalance(id, qty);
            }
        }
        private void AddTextBoxes()
        {
            this.BalanceTextEdit.Properties.ReadOnly = true;
            this.TotalBuyTextEdit.Properties.ReadOnly = true;
            this.TotalSellTextEdit.Properties.ReadOnly = true;
            this.CountTextEdit.Properties.ReadOnly = true;
            this.layoutForTotal = new LayoutControl();
            this.layoutForTotal.OptionsPrint.OldPrinting = true;
            this.layoutForTotal.MaximumSize = new Size(300, 0);
            this.layoutForTotal.Padding = new System.Windows.Forms.Padding(0);
            LayoutControlGroup totalGroup = this.Root.AddGroup("Totaux");
            totalGroup.Padding = new DevExpress.XtraLayout.Utils.Padding(0);
            totalGroup.AddItem(" ", this.layoutForTotal);
            this.Root.Add(totalGroup);
            this.Root.AddItem(new EmptySpaceItem(), totalGroup, InsertType.Left);
            this.layoutForTotal.AddItem("Nombre d'articles", this.CountTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Solde total", this.BalanceTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Total prix de vente", this.TotalSellTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Total prix d'achat", this.TotalBuyTextEdit).TextLocation = Locations.Right;
            this.SetTextAlignment();
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            this.gridView1.ClearSorting();
            GridColumn colDate = this.gridView1.Columns["ID"];
            bool flag = colDate != null;
            if (flag)
            {
                colDate.VisibleIndex = -1;
            }
            this.gridView1.BestFitColumns(true);
        }
        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            if ((info.InRow || info.InRowCell) && view.GetFocusedRowCellValue("ProductID") is int id)
            {
                HomeForm.OpenForm(ProductForm.Instance);
                ProductForm.Instance.GoTo(id);
            }
        }

        private void GridView1_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            GridView view = sender as GridView;
            if (e.Column.FieldName == "Quantity" && e.CellValue is double value)
            {
                Color color = Color.FromArgb(200, DXSkinColors.FillColors.Warning);
                if (value > 0.0)
                {
                    color = Color.FromArgb(200, DXSkinColors.FillColors.Success);
                }
                e.Appearance.BackColor = color;
            }
        }
        private void SetTextAlignment()
        {
            this.layoutForTotal.Items.ForEach(delegate (BaseLayoutItem x)
            {
                x.AppearanceItemCaption.TextOptions.HAlignment = HorzAlignment.Far;
            });
        }

        private int[] SelectedProducts
        {
            get
            {
                GridPopupContainerControl products = base.FiltersForm.Products;
                int[] result;
                if (products == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = products.EditValue;
                    result = ((editValue != null) ? editValue.ToArray<int>() : null);
                }
                return result;
            }
        }

        private int[] SelectedStores
        {
            get
            {
                GridPopupContainerControl stores = base.FiltersForm.Stores;
                int[] result;
                if (stores == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = stores.EditValue;
                    result = ((editValue != null) ? editValue.ToArray<int>() : null);
                }
                return result;
            }
        }

        private IQueryable<ProductTransaction> ProductTransactions
        {
            get
            {
                IQueryable<ProductTransaction> q = from x in this.db.ProductTransactions
                                                   where (int)x.TransactionState == 2
                                                   select x into jd
                                                   join product in this.db.Products on jd.ProductID equals product.ID
                                                   where jd.Date <= this.SelectedDate && (int)product.Type != 1
                                                   select jd;
                bool hasProducts = base.FiltersForm.hasProducts;
                if (hasProducts)
                {
                    q = from x in q
                        where this.SelectedProducts.Contains(x.ProductID)
                        select x;
                }
                bool hasStores = base.FiltersForm.hasStores;
                if (hasStores)
                {
                    q = from x in q
                        where this.SelectedStores.Contains(x.StoreID)
                        select x;
                }
                return q;
            }
        }

        private DateTime SelectedDate
        {
            get
            {
                return (base.FiltersForm.hasStartDate ? base.FiltersForm.FromDate.DateTime.Date : DateTime.Now.Date).AddDays(1.0);
            }
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                result = true;
            }
            return result;
        }

        public IQueryable<ProductBalanceInStoreReportModel> ReportFinalDataSource()
        {
            return from t in ProductTransactions
                   group t by new { t.StoreID, t.ProductID } into g
                   join br in db.Stores on g.Key.StoreID equals br.ID
                   join pr in db.Products.Include((Product x) => x.Units) on g.Key.ProductID equals pr.ID
                   select new ProductBalanceInStoreReportModel
                   {
                       ID = string.Concat(g.Key.ProductID.ToString() + "-", g.Key.StoreID.ToString()),
                       StoreID = g.Key.StoreID,
                       StoreName = br.Name,
                       ProductID = g.Key.ProductID,
                       ProductName = pr.Name,
                       SellPrice = pr.Units.FirstOrDefault((ProductUnit x) => x.Factor == 1.0).SellPrice,
                       BuyPrice = pr.Units.FirstOrDefault((ProductUnit x) => x.Factor == 1.0).BuyPrice,
                       Quantity = (g.Where(j => (int)j.TransactionType == 0).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0) - (g.Where(j => (int)j.TransactionType == 1).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0)
                   };
        }
        public override IQueryable GetQuery()
        {
            return this.ReportFinalDataSource();
        }

        public override void Print()
        {
            this.layoutForTotal.Flip();
            GridReportP.Print(this.layoutControl1, this.Text, this.labelControl1.Text, true);
            this.layoutForTotal.Flip();
        }

        public override void RefreshDataSource()
        {
            base.RefreshDataSource();
            bool flag = this.db.Database.Connection.State == ConnectionState.Closed;
            if (flag)
            {
                this.db.Database.Connection.Open();
            }
            this.CountTextEdit.Text = (from x in this.ReportFinalDataSource()
                                       group x by x.ProductID).Count().ToString();
            this.BalanceTextEdit.Text = this.ReportFinalDataSource().Sum(x => (double?)x.Quantity).ToString();
            this.TotalBuyTextEdit.Text = (from x in this.ReportFinalDataSource()
                                          select (double?)(x.BuyPrice * x.Quantity)).Sum().ToString();
            this.TotalSellTextEdit.Text = (from x in this.ReportFinalDataSource()
                                           select (double?)(x.SellPrice * x.Quantity)).Sum().ToString();
        }

        private LayoutControl layoutForTotal;

        private TextEdit CountTextEdit = new TextEdit();

        private TextEdit BalanceTextEdit = new TextEdit();

        private TextEdit TotalSellTextEdit = new TextEdit();

        private TextEdit TotalBuyTextEdit = new TextEdit();
    }
}
