﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("BOMDetails")]
    [DisplayColumn("Détails de la liste des matières")]
    public class BOMDetails : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Numéro de la liste des matières")]
        [Required(ErrorMessage = "Ce champ est requis")]
        public int BOMID
        {
            get
            {
                return this.bomID;
            }
            set
            {
                base.SetProperty<int>(ref this.bomID, value, "BOMID");
            }
        }

        public BillOfMaterials BOM { get; set; }

        [Display(Name = "Article")]
        [Required(ErrorMessage = "Ce champ est requis")]
        public int ProductID
        {
            get
            {
                return this.productID;
            }
            set
            {
                base.SetProperty<int>(ref this.productID, value, "ProductID");
            }
        }

        [Display(Name = "Unité")]
        [Required(ErrorMessage = "Ce champ est requis")]
        public int UintID
        {
            get
            {
                return this.uintID;
            }
            set
            {
                base.SetProperty<int>(ref this.uintID, value, "UintID");
            }
        }

        [Display(Name = "Quantité")]
        [Required(ErrorMessage = "Ce champ est requis")]
        public double Quantity
        {
            get
            {
                return this.quantity;
            }
            set
            {
                base.SetProperty<double>(ref this.quantity, value, "Quantity");
            }
        }

        private int id;

        private int bomID;

        private int productID;

        private int uintID;

        private double quantity;
    }
}
