﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000308 RID: 776
	public partial class TotalSalesProductCategoriesView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x06001322 RID: 4898 RVA: 0x00151E64 File Offset: 0x00150064
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001323 RID: 4899 RVA: 0x00151E9C File Offset: 0x0015009C
		private void InitializeComponent()
		{
			base.SuspendLayout();
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			base.Name = "TotalSalesProductCategoriesView";
			this.Text = "Total des ventes par catégories d'articles";
			base.ResumeLayout(false);
		}

		// Token: 0x04001926 RID: 6438
		private global::System.ComponentModel.IContainer components = null;
	}
}
