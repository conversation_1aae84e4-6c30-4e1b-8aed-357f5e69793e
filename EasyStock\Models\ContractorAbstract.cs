﻿using EasyStock.Common;
using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    [DisplayName("Abstracts de contracteurs")]
    [Table("ContractorAbstracts")]
    public class ContractorAbstract : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "N°")]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code")]
        public string Code
        {
            get
            {
                return this.code;
            }
            set
            {
                base.SetProperty<string>(ref this.code, value, "Code");
            }
        }

        [Display(Name = "Entrepreneur")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int ContractorID
        {
            get
            {
                return this.contracorid;
            }
            set
            {
                base.SetProperty<int>(ref this.contracorid, value, "ContractorID");
            }
        }

        [Display(Name = "Date de l'extrait")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime Date
        {
            get
            {
                return this.date;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.date, value, "Date");
            }
        }

        public WorkType Type { get; set; }

        [Display(Name = "Type de travail")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int WorkTypeID
        {
            get
            {
                return this.typeID;
            }
            set
            {
                base.SetProperty<int>(ref this.typeID, value, "WorkTypeID");
            }
        }

        [Display(Name = "Montant net")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double NetAmount
        {
            get
            {
                return this.netAmount;
            }
            set
            {
                base.SetProperty<double>(ref this.netAmount, value, "NetAmount");
            }
        }

        [Display(Name = "Montant payé")]
        public double PaidAmount
        {
            get
            {
                return this.Paid;
            }
            set
            {
                base.SetProperty<double>(ref this.Paid, value, "PaidAmount");
            }
        }

        [NotMapped]
        [Display(Name = "Restant")]
        public double Remaing
        {
            get
            {
                return this.NetAmount - this.PaidAmount;
            }
        }

        public Drawer Drawer { get; set; }

        [Display(Name = "N° de tiroir")]
        public int? DrawerID { get; set; }

        public Journal Journal { get; set; }

        [Display(Name = "N° de journal")]
        public int JournalID { get; set; }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        public void GetNewCode()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                Type sss = base.GetType();
                int maxLength = (from x in db.ContractorAbstracts.AsNoTracking()
                                 select x.Code).Max((string x) => (int?)x.Length).GetValueOrDefault();
                bool flag = maxLength == 0;
                if (flag)
                {
                    this.Code = "1";
                }
                else
                {
                    string maxCode = (from x in db.ContractorAbstracts.AsNoTracking()
                                      where x.Code.Length == maxLength
                                      orderby x.ID descending
                                      select x).FirstOrDefault<ContractorAbstract>().Code;
                    bool flag2 = maxCode != string.Empty;
                    if (flag2)
                    {
                        this.Code = maxCode.GetNextSequence();
                    }
                }
            }
        }

        private int id;

        private string code;

        private int contracorid;

        private DateTime date;

        private int typeID;

        private double netAmount;

        private double Paid;

        private string notes;
    }
}
