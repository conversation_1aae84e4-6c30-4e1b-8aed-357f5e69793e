﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.ReportModels
{
    public class CompanyInfoReportModel
    {
        [Display(Name = "Code de la succursale")]
        public int BranchID { get; set; }

        [Display(Name = "Nom de la succursale")]
        public string BranchName { get; set; }

        [Display(Name = "Téléphone de la succursale")]
        public string BranchPhone { get; set; }

        [Display(Name = "Ville de la succursale")]
        public string BranchCity { get; set; }

        [Display(Name = "Adresse de la succursale")]
        public string BranchAddress { get; set; }

        [Display(Name = "Nom de l'entreprise")]
        public string CompanyName { get; set; }

        [Column(TypeName = "image")]
        [Display(Name = "Logo")]
        public byte[] CompanyLogo { get; set; }

        [Display(Name = "Adresse de l'entreprise")]
        [Required(ErrorMessage = "Ce champ est requis.")]
        [StringLength(50)]
        public string CompanyAddress { get; set; }

        [Display(Name = "Ville de l'entreprise")]
        [Required(ErrorMessage = "Ce champ est requis.")]
        [StringLength(50)]
        public string CompanyCity { get; set; }

        [Display(Name = "Téléphone de l'entreprise")]
        [Required(ErrorMessage = "Ce champ est requis.")]
        [StringLength(50)]
        public string CompanyPhone { get; set; }

        [Display(Name = "Mobile de l'entreprise")]
        public string CompanyMobile { get; set; }

        [Display(Name = "N° RC")]
        public string CommercialBook { get; set; }

        [Display(Name = "N° I.F")]
        public string CompanyTaxCard { get; set; }

        [Display(Name = "N° I.S")]
        public string CompanyNIS { get; set; }

        [Display(Name = "R.I.B")]
        public string CompanyRIB { get; set; }

        [Display(Name = "ART")]
        public string CompanyART { get; set; }
    }
}
