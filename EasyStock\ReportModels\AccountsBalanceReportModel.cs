﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class AccountsBalanceReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Code de compte")]
        public int AccountCode { get; set; }

        [Display(Name = "Nom du compte")]
        public string Name { get; set; }

        [Display(Name = "Compte principal")]
        public string MainAccount { get; set; }

        [Display(Name = "Débit")]
        public double Debit
        {
            get
            {
                return (this.Balance < 0.0) ? Math.Abs(this.Balance) : 0.0;
            }
        }

        [Display(Name = "Crédit")]
        public double Credit
        {
            get
            {
                return (this.Balance > 0.0) ? this.Balance : 0.0;
            }
        }

        public double TotalCredit { get; set; }

        public double TotalDebit { get; set; }

        public double Balance
        {
            get
            {
                return this.TotalCredit - this.TotalDebit;
            }
        }
    }
}
