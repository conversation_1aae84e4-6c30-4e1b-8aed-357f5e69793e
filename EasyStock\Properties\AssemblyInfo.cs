﻿using System;
using System.Diagnostics;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.Versioning;

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyTitle("EasyStock")]
[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("EasyStock")]
[assembly: AssemblyProduct("OraScoop Mars Algeria")]
[assembly: AssemblyCopyright("Copyright ©  2023 for  OMA")]
[assembly: AssemblyTrademark("EasyStock")]
[assembly: ComVisible(false)]
[assembly: Guid("d7677ea7-3a70-44fe-b86f-2e6335308682")]
[assembly: AssemblyFileVersion("*******")]
