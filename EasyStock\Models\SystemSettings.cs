﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Paramètres du système")]
    public class SystemSettings
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Display(Name = "Mode de conversion en texte", GroupName = "{Tabs}/Général")]
        public MoneyToTextModes MoneyToTextMode { get; set; }

        [Display(Name = "Chemin des rapports par défaut", GroupName = "{Tabs}/Impression")]
        public string PrimaryReportingFolderName { get; set; }

        [Display(Name = "Chemin des rapports secondaire", GroupName = "{Tabs}/Impression")]
        public string SecondaryReportingPath { get; set; }

        [DefaultValue(0)]
        [Display(Name = "Comptes des employés", GroupName = "{Tabs}/Comptes/Resources humaines")]
        public int EmployeesDueAccount { get; set; }

        [Display(Name = "Dépenses salariales", GroupName = "{Tabs}/Comptes/Resources humaines")]
        [DefaultValue(0)]
        public int WagesAccount { get; set; }

        [Display(Name = "Salaires dus", GroupName = "{Tabs}/Comptes/Resources humaines")]
        [DefaultValue(0)]
        public int DueSalerysAccount { get; set; }

        [Display(Name = "Actifs", GroupName = "{Tabs}/Comptes/Actifs")]
        public int AssetsAccount { get; set; }

        [Display(Name = "Actifs courants", GroupName = "{Tabs}/Comptes/Actifs")]
        public int CurrentAssetsAccount { get; set; }

        [Display(Name = "Passifs et Capitaux propres", GroupName = "{Tabs}/Comptes/Passifs et Capitaux propres")]
        public int LiabilitiesAndOwnersEquity { get; set; }

        [Display(Name = "Capitaux propres", GroupName = "{Tabs}/Comptes/Passifs et Capitaux propres")]
        public int OwnersEquityAccount { get; set; }

        [Display(Name = "Actifs fixes", GroupName = "{Tabs}/Comptes/Actifs")]
        public int FixedAssetsAccount { get; set; }

        [Display(Name = "Caisses", GroupName = "{Tabs}/Comptes/Actifs")]
        public int DrawerAccount { get; set; }

        [Display(Name = "Banques", GroupName = "{Tabs}/Comptes/Actifs")]
        public int BanksAccount { get; set; }

        [Display(Name = "Clients", GroupName = "{Tabs}/Comptes/Actifs")]
        public int CustomersAccount { get; set; }

        [Display(Name = "Effets à recevoir", GroupName = "{Tabs}/Comptes/Actifs")]
        public int NotesReceivableAccount { get; set; }

        [Display(Name = "Caisse petite caisse permanente", GroupName = "{Tabs}/Comptes/Actifs")]
        public int PermanentPettyCashAccount { get; set; }

        [Display(Name = "Caisse petite caisse temporaire", GroupName = "{Tabs}/Comptes/Actifs")]
        public int TemporaryPettyCashAccount { get; set; }

        [Display(Name = "Fournisseurs", GroupName = "{Tabs}/Comptes/Passifs et Capitaux propres")]
        public int VendorsAccount { get; set; }

        [Display(Name = "Capital", GroupName = "{Tabs}/Comptes/Passifs et Capitaux propres")]
        public int CapitalAccount { get; set; }

        [Display(Name = "Effets à payer", GroupName = "{Tabs}/Comptes/Passifs et Capitaux propres")]
        public int NotesPayableAccount { get; set; }

        [Display(Name = "Amortissement cumulé", GroupName = "{Tabs}/Comptes/Passifs et Capitaux propres")]
        public int DepreciationAccount { get; set; }

        [Display(Name = "Impôt", GroupName = "{Tabs}/Comptes/Impôts")]
        public int TaxAccount { get; set; }

        [Display(Name = "Taxe sur les achats", GroupName = "{Tabs}/Comptes/Impôts")]
        public int PurchaseAddTaxAccount { get; set; }

        [Display(Name = "Taxe sur les ventes", GroupName = "{Tabs}/Comptes/Impôts")]
        public int SalesAddTaxAccount { get; set; }

        [Display(Name = "Réduction sur les ventes", GroupName = "{Tabs}/Comptes/Impôts")]
        public int SalesDeductTaxAccount { get; set; }

        [Display(Name = "Réduction sur les achats", GroupName = "{Tabs}/Comptes/Impôts")]
        public int PurchaseDeductTaxAccount { get; set; }

        [Display(Name = "Comptabilité", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int MerchandisingAccount { get; set; }

        [Display(Name = "Inventaire", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int InventoryAccount { get; set; }

        [Display(Name = "Achats", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int PurchasesAccount { get; set; }

        [Display(Name = "Retour sur achats", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int PurchasesReturnAccount { get; set; }

        [Display(Name = "Ventes", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int SalesAccount { get; set; }

        [Display(Name = "Retour sur ventes", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int SalesReturnAccount { get; set; }

        [Display(Name = "Inventaire de début", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int OpenInventoryAccount { get; set; }

        [Display(Name = "Inventaire de fin", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int CloseInventoryAccount { get; set; }

        [Display(Name = "Réduction en espèces obtenue", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int PurchaseDiscountAccount { get; set; }

        [Display(Name = "Réduction en espèces autorisée", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int SalesDiscountAccount { get; set; }

        [Display(Name = "Coût des biens vendus", GroupName = "{Tabs}/Comptes/Compte de résultat")]
        public int CostOfSoldGoodsAccount { get; set; }

        [Display(Name = "Dépenses", GroupName = "{Tabs}/Comptes/Recettes et Dépenses")]
        public int ExpensesAccount { get; set; }

        [Display(Name = "Recettes", GroupName = "{Tabs}/Comptes/Recettes et Dépenses")]
        public int RevenueAccount { get; set; }

        [Display(Name = "En cas de duplication des numéros de factures", GroupName = "{Tabs}/Factures/Factures")]
        public RedundancyOptions InvoicesCodeRedundancy { get; set; }

        [Display(Name = "En cas de duplication du numéro de facture - créer un nouveau numéro lors de l'enregistrement", GroupName = "{Tabs}/Factures/Factures")]
        public bool InvoicesCodeRedundancyOnSave { get; set; }

        [Display(Name = "Activer", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        public bool ReadFormScaleBarcode { get; set; } = true;

        [Display(Name = "Longueur du code-barres", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue(13)]
        public byte BarcodeLength { get; set; } = 13;

        [Display(Name = "Préfixe du code-barres", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue("20")]
        public string ScaleBarcodePrefix { get; set; } = "20";

        [Display(Name = "Longueur du code article", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue(5)]
        public byte ProductCodeLength { get; set; } = 5;

        [Display(Name = "Longueur du code de prix/poids", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue(6)]
        public byte ValueCodeLength { get; set; } = 6;

        [Display(Name = "Type de lecture", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue(ReadValueMode.Weight)]
        public ReadValueMode ReadMode { get; set; }

        [Display(Name = "Ignorer le chiffre de contrôle", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue(true)]
        public bool IgnoreCheckDigit { get; set; } = true;

        [Display(Name = "Diviser la valeur par", GroupName = "{Tabs}/Factures/Lecture depuis le code-barres de la balance")]
        [DefaultValue(3)]
        public byte DivideValueBy { get; set; } = 3;

        [Display(Name = "Unités des articles", GroupName = "{Tabs}/articles")]
        public ProductUnitsMode ProductUnitsMode { get; set; }

        [Display(Name = "Taux de Taxe sur les ventes", GroupName = "{Tabs}/Impôts")]
        [Range(0.0, 0.99, ErrorMessage = "Le taux de Taxe doit être compris entre 0 et 99 %")]
        public double SalesTax { get; set; }

        [Display(Name = "Taux de Taxe sur les achats", GroupName = "{Tabs}/Impôts")]
        [Range(0.0, 0.99, ErrorMessage = "Le taux de Taxe doit être compris entre 0 et 99 %")]
        public double PurchaseTax { get; set; }

        [Display(Name = "Calculer la Taxe après réduction", GroupName = "{Tabs}/Impôts")]
        public bool CalculateTaxAfterDiscount { get; set; }

        [Display(Name = "Prix des unités TTC", GroupName = "{Tabs}/Impôts")]
        public bool PriceIncludeTax { get; set; }
    }
}
