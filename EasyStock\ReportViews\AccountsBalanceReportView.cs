﻿using DevExpress.XtraLayout.Utils;
using DevExpress.XtraReports;
using DevExpress.XtraReports.UI;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class AccountsBalanceReportView : XtraReportForm
    {
        public AccountsBalanceReportView()
        : base(new ReportFilter[2]
        {
            ReportFilter.Date,
            ReportFilter.Account
        })
        {
            InitializeComponent();

            base.FiltersForm.ItemForFromDate.Text = "Date";
            base.FiltersForm.ItemForToDate.Visibility = LayoutVisibility.Never;
            base.FiltersForm.FromDate.EditValue = DateTime.Now;
        }
        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "Vous devez sélectionner la date.";
                result = false;
            }
            else
            {
                result = true;
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                Reports.AccountsBalanceReports rpt = new Reports.AccountsBalanceReports();
                ICollection<AccountsBalanceReportModel> dataSource = this.GetData();
                rpt.DataSource = dataSource;

                AccountsBalanceReportModel firstItem = dataSource.FirstOrDefault();
                if (firstItem != null)
                {
                    rpt.SetCompanyInfo(firstItem);
                }
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;

                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });

            }
        }
        private ICollection<AccountsBalanceReportModel> GetData()
        {
            int[] AccountIDs = base.FiltersForm.Accounts.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<Account> Accounts = context.Accounts.AsQueryable();
                if (AccountIDs != null && AccountIDs.Count() > 0)
                {
                    var allAccounts = context.Accounts.Select((Account x) => new { x.ID, x.Number }).ToList();
                    List<int> selectedAccounts = new List<int>();
                    List<string> selectedAccountsNumbers = (from Acc in context.Accounts
                                                            where AccountIDs.Contains(Acc.ID)
                                                            select Acc into x
                                                            select x.Number).ToList();
                    foreach (string item in selectedAccountsNumbers)
                    {
                        var result = allAccounts.Where(x => x.Number.StartsWith(item));
                        selectedAccounts.AddRange(result.Select(x => x.ID).ToList());
                        allAccounts.RemoveAll(x => x.Number.StartsWith(item));
                        if (allAccounts.Count != 0)
                        {
                        }
                    }
                    Accounts = Accounts.Where((Account a) => selectedAccounts.Contains(a.ID));
                }
                var data = from jou in db.Journals.Where(j => DbFunctions.TruncateTime(j.Date) <= FiltersForm.startDate).AsEnumerable()
                           join det in db.JournalDetails.AsEnumerable() on jou.ID equals det.JournalID
                           group det by new { det.AccountID } into g
                           join acc in Accounts.AsEnumerable() on g.Key.AccountID equals acc.ID into accGroup
                           from acc in accGroup.DefaultIfEmpty()
                           let parentAcc = Accounts.AsEnumerable().FirstOrDefault(a => a.ID == acc?.ParentID)
                           select new AccountsBalanceReportModel
                           {
                               AccountCode = acc.ID,
                               Name = acc.Name,
                               MainAccount = parentAcc?.Name,
                               TotalCredit = g.Sum(x => (double?)(x.Credit * x.CurrencyRate)?? 0.00),
                               TotalDebit = g.Sum(x => (double?)(x.Debit * x.CurrencyRate)?? 0.00)
                           };

                return data.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
    }
}
