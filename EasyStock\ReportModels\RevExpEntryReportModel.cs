﻿using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class RevExpEntryReportModel : CompanyInfoReportModel
    {
        [Display(Name = "N°")]
        public int ID { get; set; }

        [Display(Name = "Code")]
        public string Code { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Méthode de Paiement")]
        public CashNotePaySourceType MethodType { get; set; }

        [Display(Name = "Compte de Paiement")]
        public string MethodName { get; set; }

        [Display(Name = "Code du Compte de Paiement")]
        public int MethodID { get; set; }

        [Display(Name = "Total")]
        public double Total { get; set; }

        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Détails")]
        public ICollection<RevExpEntryDetailReportModel> Details { get; set; }

        [Display(Name = "Type")]
        public RevExpEntryType EntryType { get; set; }

        [Display(Name = "Total en Texte")]
        public string TotalPaidText { get; set; }

        [Display(Name = "Utilisateur")]
        public string UserID { get; set; }
    }
}
