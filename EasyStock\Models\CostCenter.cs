﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.Models
{
    [DisplayColumn("Centres de coûts")]
    public class CostCenter : BaseNotifyPropertyChangedModel
    {
        public int ID
        {
            get { return this.id; }
            set { base.SetProperty<int>(ref this.id, value, "ID"); }
        }

        [Display(Name = "Code")]
        public string Code
        {
            get { return this.code; }
            set { base.SetProperty<string>(ref this.code, value, "Code"); }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        public string Name
        {
            get { return this.name; }
            set { base.SetProperty<string>(ref this.name, value, "Name"); }
        }

        public CostCenter Parent { get; set; }

        [Display(Name = "Parent ID")]
        public int? ParentID
        {
            get { return this.parentID; }
            set { base.SetProperty<int?>(ref this.parentID, value, "ParentID"); }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get { return this.notes; }
            set { base.SetProperty<string>(ref this.notes, value, "Notes"); }
        }

        private int id;
        private string code;
        private string name;
        private int? parentID;
        private string notes;
    }
}
