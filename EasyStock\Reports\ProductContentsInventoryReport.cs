﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x02000369 RID: 873
	public class ProductContentsInventoryReport : XtraReport
	{
		// Token: 0x060014DE RID: 5342 RVA: 0x0000AAD7 File Offset: 0x00008CD7
		public ProductContentsInventoryReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x060014DF RID: 5343 RVA: 0x0017F138 File Offset: 0x0017D338
		private void tableCellInQuantity_BeforePrint(object sender, CancelEventArgs e)
		{
			XRTableCell cell = sender as XRTableCell;
			bool flag = !string.IsNullOrWhiteSpace(cell.Text);
			if (flag)
			{
				double count = Convert.ToDouble(cell.Text);
				this.DetailReportQuantityIn.Visible = (count > 0.0);
			}
		}

		// Token: 0x060014E0 RID: 5344 RVA: 0x0017F188 File Offset: 0x0017D388
		private void tableCellOutQuantity_BeforePrint(object sender, CancelEventArgs e)
		{
			XRTableCell cell = sender as XRTableCell;
			bool flag = !string.IsNullOrWhiteSpace(cell.Text);
			if (flag)
			{
				double count = Convert.ToDouble(cell.Text);
				this.DetailReportQuantityOut.Visible = (count > 0.0);
			}
		}

		// Token: 0x060014E1 RID: 5345 RVA: 0x0017F1D8 File Offset: 0x0017D3D8
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060014E2 RID: 5346 RVA: 0x0017F210 File Offset: 0x0017D410
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.objectDataSource1 = new DevExpress.DataAccess.ObjectBinding.ObjectDataSource(this.components);
            this.Title = new DevExpress.XtraReports.UI.XRControlStyle();
            this.DetailCaption1 = new DevExpress.XtraReports.UI.XRControlStyle();
            this.DetailData1 = new DevExpress.XtraReports.UI.XRControlStyle();
            this.DetailCaption2 = new DevExpress.XtraReports.UI.XRControlStyle();
            this.DetailData2 = new DevExpress.XtraReports.UI.XRControlStyle();
            this.DetailData3_Odd = new DevExpress.XtraReports.UI.XRControlStyle();
            this.PageInfo = new DevExpress.XtraReports.UI.XRControlStyle();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow8 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow9 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow10 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_ReportName = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_Filters = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPictureBox2 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCellInQuantity = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCellOutQuantity = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReportQuantityIn = new DevExpress.XtraReports.UI.DetailReportBand();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell37 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell38 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell39 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell40 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell41 = new DevExpress.XtraReports.UI.XRTableCell();
            this.Detail1 = new DevExpress.XtraReports.UI.DetailBand();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell42 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell43 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell44 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell45 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell46 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReportQuantityOut = new DevExpress.XtraReports.UI.DetailReportBand();
            this.GroupHeader2 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.table4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell47 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell48 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell49 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell50 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell51 = new DevExpress.XtraReports.UI.XRTableCell();
            this.Detail2 = new DevExpress.XtraReports.UI.DetailBand();
            this.table5 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell52 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell53 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell54 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell55 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell56 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.objectDataSource1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // objectDataSource1
            // 
            this.objectDataSource1.DataSource = typeof(EasyStock.ReportModels.ProductContentsInventory);
            this.objectDataSource1.Name = "objectDataSource1";
            // 
            // Title
            // 
            this.Title.BackColor = System.Drawing.Color.Transparent;
            this.Title.BorderColor = System.Drawing.Color.Black;
            this.Title.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.Title.BorderWidth = 1F;
            this.Title.Font = new DevExpress.Drawing.DXFont("Arial", 14.25F);
            this.Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(75)))), ((int)(((byte)(75)))), ((int)(((byte)(75)))));
            this.Title.Name = "Title";
            // 
            // DetailCaption1
            // 
            this.DetailCaption1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(75)))), ((int)(((byte)(75)))), ((int)(((byte)(75)))));
            this.DetailCaption1.BorderColor = System.Drawing.Color.White;
            this.DetailCaption1.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.DetailCaption1.BorderWidth = 2F;
            this.DetailCaption1.Font = new DevExpress.Drawing.DXFont("Arial", 8.25F, DevExpress.Drawing.DXFontStyle.Bold);
            this.DetailCaption1.ForeColor = System.Drawing.Color.White;
            this.DetailCaption1.Name = "DetailCaption1";
            this.DetailCaption1.Padding = new DevExpress.XtraPrinting.PaddingInfo(6, 6, 0, 0, 100F);
            this.DetailCaption1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // DetailData1
            // 
            this.DetailData1.BorderColor = System.Drawing.Color.Transparent;
            this.DetailData1.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.DetailData1.BorderWidth = 2F;
            this.DetailData1.Font = new DevExpress.Drawing.DXFont("Arial", 8.25F);
            this.DetailData1.ForeColor = System.Drawing.Color.Black;
            this.DetailData1.Name = "DetailData1";
            this.DetailData1.Padding = new DevExpress.XtraPrinting.PaddingInfo(6, 6, 0, 0, 100F);
            this.DetailData1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // DetailCaption2
            // 
            this.DetailCaption2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(111)))), ((int)(((byte)(111)))));
            this.DetailCaption2.BorderColor = System.Drawing.Color.White;
            this.DetailCaption2.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.DetailCaption2.BorderWidth = 2F;
            this.DetailCaption2.Font = new DevExpress.Drawing.DXFont("Arial", 8.25F, DevExpress.Drawing.DXFontStyle.Bold);
            this.DetailCaption2.ForeColor = System.Drawing.Color.White;
            this.DetailCaption2.Name = "DetailCaption2";
            this.DetailCaption2.Padding = new DevExpress.XtraPrinting.PaddingInfo(6, 6, 0, 0, 100F);
            this.DetailCaption2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // DetailData2
            // 
            this.DetailData2.BorderColor = System.Drawing.Color.Transparent;
            this.DetailData2.Borders = DevExpress.XtraPrinting.BorderSide.Left;
            this.DetailData2.BorderWidth = 2F;
            this.DetailData2.Font = new DevExpress.Drawing.DXFont("Arial", 8.25F);
            this.DetailData2.ForeColor = System.Drawing.Color.Black;
            this.DetailData2.Name = "DetailData2";
            this.DetailData2.Padding = new DevExpress.XtraPrinting.PaddingInfo(6, 6, 0, 0, 100F);
            this.DetailData2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // DetailData3_Odd
            // 
            this.DetailData3_Odd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(231)))), ((int)(((byte)(231)))));
            this.DetailData3_Odd.BorderColor = System.Drawing.Color.Transparent;
            this.DetailData3_Odd.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.DetailData3_Odd.BorderWidth = 1F;
            this.DetailData3_Odd.Font = new DevExpress.Drawing.DXFont("Arial", 8.25F);
            this.DetailData3_Odd.ForeColor = System.Drawing.Color.Black;
            this.DetailData3_Odd.Name = "DetailData3_Odd";
            this.DetailData3_Odd.Padding = new DevExpress.XtraPrinting.PaddingInfo(6, 6, 0, 0, 100F);
            this.DetailData3_Odd.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // PageInfo
            // 
            this.PageInfo.Font = new DevExpress.Drawing.DXFont("Arial", 8.25F, DevExpress.Drawing.DXFontStyle.Bold);
            this.PageInfo.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(75)))), ((int)(((byte)(75)))), ((int)(((byte)(75)))));
            this.PageInfo.Name = "PageInfo";
            this.PageInfo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // TopMargin
            // 
            this.TopMargin.HeightF = 10F;
            this.TopMargin.Name = "TopMargin";
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 8.900769F;
            this.BottomMargin.Name = "BottomMargin";
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable3,
            this.xrTable2,
            this.xrPictureBox2});
            this.ReportHeader.HeightF = 209.5576F;
            this.ReportHeader.Name = "ReportHeader";
            // 
            // xrTable3
            // 
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(511.9242F, 0F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3,
            this.xrTableRow4,
            this.xrTableRow8,
            this.xrTableRow9,
            this.xrTableRow10});
            this.xrTable3.SizeF = new System.Drawing.SizeF(289.5833F, 123.4375F);
            this.xrTable3.StylePriority.UseTextAlignment = false;
            this.xrTable3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell13});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Weight = 17.478596757766979D;
            // 
            // xrTableCell13
            // 
            this.xrTableCell13.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[CompanyName]")});
            this.xrTableCell13.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell13.Multiline = true;
            this.xrTableCell13.Name = "xrTableCell13";
            this.xrTableCell13.StylePriority.UseFont = false;
            this.xrTableCell13.StylePriority.UseTextAlignment = false;
            this.xrTableCell13.Text = "xrTableCell13";
            this.xrTableCell13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell13.Weight = 1.8461538461538458D;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Weight = 17.478596757766979D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[CommercialBook]")});
            this.xrTableCell1.Multiline = true;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Text = "xrTableCell2";
            this.xrTableCell1.Weight = 1.8461538461538458D;
            // 
            // xrTableRow8
            // 
            this.xrTableRow8.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell5});
            this.xrTableRow8.Name = "xrTableRow8";
            this.xrTableRow8.Weight = 17.478596757766979D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[CompanyTaxCard]")});
            this.xrTableCell5.Multiline = true;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.Text = "xrTableCell3";
            this.xrTableCell5.Weight = 1.8461538461538458D;
            // 
            // xrTableRow9
            // 
            this.xrTableRow9.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell14});
            this.xrTableRow9.Name = "xrTableRow9";
            this.xrTableRow9.Weight = 17.478596757766979D;
            // 
            // xrTableCell14
            // 
            this.xrTableCell14.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[CompanyAddress]")});
            this.xrTableCell14.Multiline = true;
            this.xrTableCell14.Name = "xrTableCell14";
            this.xrTableCell14.Text = "xrTableCell1";
            this.xrTableCell14.Weight = 1.8461538461538458D;
            // 
            // xrTableRow10
            // 
            this.xrTableRow10.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell15});
            this.xrTableRow10.Name = "xrTableRow10";
            this.xrTableRow10.Weight = 17.478596757766979D;
            // 
            // xrTableCell15
            // 
            this.xrTableCell15.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[CompanyPhone]")});
            this.xrTableCell15.Multiline = true;
            this.xrTableCell15.Name = "xrTableCell15";
            this.xrTableCell15.Text = "xrTableCell6";
            this.xrTableCell15.Weight = 1.8461538461538458D;
            // 
            // xrTable2
            // 
            this.xrTable2.AnchorHorizontal = ((DevExpress.XtraReports.UI.HorizontalAnchorStyles)((DevExpress.XtraReports.UI.HorizontalAnchorStyles.Left | DevExpress.XtraReports.UI.HorizontalAnchorStyles.Right)));
            this.xrTable2.AnchorVertical = ((DevExpress.XtraReports.UI.VerticalAnchorStyles)((DevExpress.XtraReports.UI.VerticalAnchorStyles.Top | DevExpress.XtraReports.UI.VerticalAnchorStyles.Bottom)));
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 123.4375F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5,
            this.xrTableRow6,
            this.xrTableRow7});
            this.xrTable2.SizeF = new System.Drawing.SizeF(806.9168F, 86.12007F);
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_ReportName});
            this.xrTableRow5.Name = "xrTableRow5";
            this.xrTableRow5.Weight = 1D;
            // 
            // Cell_ReportName
            // 
            this.Cell_ReportName.BackColor = System.Drawing.Color.Empty;
            this.Cell_ReportName.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.Cell_ReportName.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.Cell_ReportName.BorderWidth = 1F;
            this.Cell_ReportName.CanGrow = false;
            this.Cell_ReportName.Font = new DevExpress.Drawing.DXFont("Traditional Arabic", 21.75F, DevExpress.Drawing.DXFontStyle.Bold, DevExpress.Drawing.DXGraphicsUnit.Point, new DevExpress.Drawing.DXFontAdditionalProperty[] {
            new DevExpress.Drawing.DXFontAdditionalProperty("GdiCharSet", ((byte)(0)))});
            this.Cell_ReportName.Multiline = true;
            this.Cell_ReportName.Name = "Cell_ReportName";
            this.Cell_ReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 5, 5, 100F);
            this.Cell_ReportName.RowSpan = 2;
            this.Cell_ReportName.StylePriority.UseBackColor = false;
            this.Cell_ReportName.StylePriority.UseBorderDashStyle = false;
            this.Cell_ReportName.StylePriority.UseBorders = false;
            this.Cell_ReportName.StylePriority.UseBorderWidth = false;
            this.Cell_ReportName.StylePriority.UseFont = false;
            this.Cell_ReportName.StylePriority.UsePadding = false;
            this.Cell_ReportName.StylePriority.UseTextAlignment = false;
            this.Cell_ReportName.Text = "Nom du rapport";
            this.Cell_ReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.Cell_ReportName.Weight = 3D;
            // 
            // xrTableRow6
            // 
            this.xrTableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell4});
            this.xrTableRow6.Name = "xrTableRow6";
            this.xrTableRow6.Weight = 1D;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.BackColor = System.Drawing.Color.WhiteSmoke;
            this.xrTableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell4.BorderWidth = 2F;
            this.xrTableCell4.CanGrow = false;
            this.xrTableCell4.Font = new DevExpress.Drawing.DXFont("Arial", 12.64F, DevExpress.Drawing.DXFontStyle.Bold, DevExpress.Drawing.DXGraphicsUnit.Point, new DevExpress.Drawing.DXFontAdditionalProperty[] {
            new DevExpress.Drawing.DXFontAdditionalProperty("GdiCharSet", ((byte)(0)))});
            this.xrTableCell4.Multiline = true;
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.Padding = new DevExpress.XtraPrinting.PaddingInfo(5, 5, 5, 5, 100F);
            this.xrTableCell4.StylePriority.UseBackColor = false;
            this.xrTableCell4.StylePriority.UseBorders = false;
            this.xrTableCell4.StylePriority.UseBorderWidth = false;
            this.xrTableCell4.StylePriority.UseFont = false;
            this.xrTableCell4.StylePriority.UsePadding = false;
            this.xrTableCell4.StylePriority.UseTextAlignment = false;
            this.xrTableCell4.Text = "xrTableCell4";
            this.xrTableCell4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell4.Weight = 3D;
            // 
            // xrTableRow7
            // 
            this.xrTableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_Filters});
            this.xrTableRow7.Name = "xrTableRow7";
            this.xrTableRow7.Weight = 1D;
            // 
            // Cell_Filters
            // 
            this.Cell_Filters.CanGrow = false;
            this.Cell_Filters.Multiline = true;
            this.Cell_Filters.Name = "Cell_Filters";
            this.Cell_Filters.StylePriority.UseTextAlignment = false;
            this.Cell_Filters.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.Cell_Filters.Weight = 3D;
            // 
            // xrPictureBox2
            // 
            this.xrPictureBox2.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "ImageSource", "[CompanyLogo]")});
            this.xrPictureBox2.LocationFloat = new DevExpress.Utils.PointFloat(40.5162F, 10.00001F);
            this.xrPictureBox2.Name = "xrPictureBox2";
            this.xrPictureBox2.SizeF = new System.Drawing.SizeF(99.99998F, 100F);
            this.xrPictureBox2.Sizing = DevExpress.XtraPrinting.ImageSizeMode.Squeeze;
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table1});
            this.Detail.HeightF = 56F;
            this.Detail.KeepTogether = true;
            this.Detail.Name = "Detail";
            // 
            // table1
            // 
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow1,
            this.tableRow2});
            this.table1.SizeF = new System.Drawing.SizeF(806.9167F, 56F);
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell1,
            this.tableCell2,
            this.tableCell3,
            this.tableCell4,
            this.tableCell5});
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 0.5D;
            // 
            // tableCell1
            // 
            this.tableCell1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(54)))), ((int)(((byte)(64)))));
            this.tableCell1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.tableCell1.Font = new DevExpress.Drawing.DXFont("Arial", 14F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.StyleName = "DetailCaption1";
            this.tableCell1.StylePriority.UseBackColor = false;
            this.tableCell1.StylePriority.UseBorders = false;
            this.tableCell1.StylePriority.UseFont = false;
            this.tableCell1.StylePriority.UseTextAlignment = false;
            this.tableCell1.Text = "Nom de l\'article";
            this.tableCell1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell1.Weight = 0.10332084063655188D;
            // 
            // tableCell2
            // 
            this.tableCell2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(54)))), ((int)(((byte)(64)))));
            this.tableCell2.Font = new DevExpress.Drawing.DXFont("Arial", 14F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.StyleName = "DetailCaption1";
            this.tableCell2.StylePriority.UseBackColor = false;
            this.tableCell2.StylePriority.UseFont = false;
            this.tableCell2.StylePriority.UseTextAlignment = false;
            this.tableCell2.Text = "Solde initial";
            this.tableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell2.Weight = 0.049094197753224968D;
            // 
            // tableCell3
            // 
            this.tableCell3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(54)))), ((int)(((byte)(64)))));
            this.tableCell3.Font = new DevExpress.Drawing.DXFont("Arial", 14F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell3.Name = "tableCell3";
            this.tableCell3.StyleName = "DetailCaption1";
            this.tableCell3.StylePriority.UseBackColor = false;
            this.tableCell3.StylePriority.UseFont = false;
            this.tableCell3.StylePriority.UseTextAlignment = false;
            this.tableCell3.Text = "Entrées";
            this.tableCell3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell3.Weight = 0.049020843047602021D;
            // 
            // tableCell4
            // 
            this.tableCell4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(54)))), ((int)(((byte)(64)))));
            this.tableCell4.Font = new DevExpress.Drawing.DXFont("Arial", 14F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.StyleName = "DetailCaption1";
            this.tableCell4.StylePriority.UseBackColor = false;
            this.tableCell4.StylePriority.UseFont = false;
            this.tableCell4.StylePriority.UseTextAlignment = false;
            this.tableCell4.Text = "Sorties";
            this.tableCell4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell4.Weight = 0.034502287434492795D;
            // 
            // tableCell5
            // 
            this.tableCell5.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(54)))), ((int)(((byte)(64)))));
            this.tableCell5.Font = new DevExpress.Drawing.DXFont("Arial", 14F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.StyleName = "DetailCaption1";
            this.tableCell5.StylePriority.UseBackColor = false;
            this.tableCell5.StylePriority.UseFont = false;
            this.tableCell5.StylePriority.UseTextAlignment = false;
            this.tableCell5.Text = "Solde Final";
            this.tableCell5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell5.Weight = 0.040712811100583945D;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell19,
            this.tableCell20,
            this.tableCellInQuantity,
            this.tableCellOutQuantity,
            this.tableCell23,
            this.tableCell30});
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 0.5D;
            // 
            // tableCell19
            // 
            this.tableCell19.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell19.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.tableCell19.BorderWidth = 1F;
            this.tableCell19.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[ProductName]")});
            this.tableCell19.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.StyleName = "DetailData1";
            this.tableCell19.StylePriority.UseBorderColor = false;
            this.tableCell19.StylePriority.UseBorderDashStyle = false;
            this.tableCell19.StylePriority.UseBorders = false;
            this.tableCell19.StylePriority.UseBorderWidth = false;
            this.tableCell19.StylePriority.UseFont = false;
            this.tableCell19.StylePriority.UseTextAlignment = false;
            this.tableCell19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell19.Weight = 0.10332084063655188D;
            // 
            // tableCell20
            // 
            this.tableCell20.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell20.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell20.BorderWidth = 1F;
            this.tableCell20.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[FirstQuantity]")});
            this.tableCell20.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.StyleName = "DetailData1";
            this.tableCell20.StylePriority.UseBorderColor = false;
            this.tableCell20.StylePriority.UseBorderDashStyle = false;
            this.tableCell20.StylePriority.UseBorderWidth = false;
            this.tableCell20.StylePriority.UseFont = false;
            this.tableCell20.StylePriority.UseTextAlignment = false;
            this.tableCell20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell20.TextFormatString = "{0:#.00}";
            this.tableCell20.Weight = 0.049094197753224968D;
            // 
            // tableCellInQuantity
            // 
            this.tableCellInQuantity.BorderColor = System.Drawing.Color.DimGray;
            this.tableCellInQuantity.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCellInQuantity.BorderWidth = 1F;
            this.tableCellInQuantity.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[InQuantity]")});
            this.tableCellInQuantity.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.tableCellInQuantity.Name = "tableCellInQuantity";
            this.tableCellInQuantity.StyleName = "DetailData1";
            this.tableCellInQuantity.StylePriority.UseBorderColor = false;
            this.tableCellInQuantity.StylePriority.UseBorderDashStyle = false;
            this.tableCellInQuantity.StylePriority.UseBorderWidth = false;
            this.tableCellInQuantity.StylePriority.UseFont = false;
            this.tableCellInQuantity.StylePriority.UseTextAlignment = false;
            this.tableCellInQuantity.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCellInQuantity.TextFormatString = "{0:#.00}";
            this.tableCellInQuantity.Weight = 0.049020843047602021D;
            // 
            // tableCellOutQuantity
            // 
            this.tableCellOutQuantity.BorderColor = System.Drawing.Color.DimGray;
            this.tableCellOutQuantity.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCellOutQuantity.BorderWidth = 1F;
            this.tableCellOutQuantity.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[OutQuantity]")});
            this.tableCellOutQuantity.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.tableCellOutQuantity.Name = "tableCellOutQuantity";
            this.tableCellOutQuantity.StyleName = "DetailData1";
            this.tableCellOutQuantity.StylePriority.UseBorderColor = false;
            this.tableCellOutQuantity.StylePriority.UseBorderDashStyle = false;
            this.tableCellOutQuantity.StylePriority.UseBorderWidth = false;
            this.tableCellOutQuantity.StylePriority.UseFont = false;
            this.tableCellOutQuantity.StylePriority.UseTextAlignment = false;
            this.tableCellOutQuantity.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCellOutQuantity.TextFormatString = "{0:#.00}";
            this.tableCellOutQuantity.Weight = 0.034502287434492795D;
            // 
            // tableCell23
            // 
            this.tableCell23.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell23.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell23.BorderWidth = 1F;
            this.tableCell23.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[LastQuantity]")});
            this.tableCell23.Font = new DevExpress.Drawing.DXFont("Arial", 10F);
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.StyleName = "DetailData1";
            this.tableCell23.StylePriority.UseBorderColor = false;
            this.tableCell23.StylePriority.UseBorderDashStyle = false;
            this.tableCell23.StylePriority.UseBorderWidth = false;
            this.tableCell23.StylePriority.UseFont = false;
            this.tableCell23.StylePriority.UseTextAlignment = false;
            this.tableCell23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell23.TextFormatString = "{0:#.00}";
            this.tableCell23.Weight = 0.03823448606787766D;
            // 
            // tableCell30
            // 
            this.tableCell30.Name = "tableCell30";
            this.tableCell30.StyleName = "DetailData1";
            this.tableCell30.Weight = 0.0024783250327062856D;
            // 
            // DetailReportQuantityIn
            // 
            this.DetailReportQuantityIn.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.GroupHeader1,
            this.Detail1});
            this.DetailReportQuantityIn.DataMember = "ProductContentsInventoryDetailsIn";
            this.DetailReportQuantityIn.DataSource = this.objectDataSource1;
            this.DetailReportQuantityIn.Level = 0;
            this.DetailReportQuantityIn.Name = "DetailReportQuantityIn";
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table2});
            this.GroupHeader1.GroupUnion = DevExpress.XtraReports.UI.GroupUnion.WithFirstDetail;
            this.GroupHeader1.HeightF = 56F;
            this.GroupHeader1.Name = "GroupHeader1";
            // 
            // table2
            // 
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(75.95837F, 0F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1,
            this.tableRow3});
            this.table2.SizeF = new System.Drawing.SizeF(731.0416F, 56F);
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell2});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(136)))), ((int)(((byte)(209)))));
            this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrTableCell2.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell2.Multiline = true;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StyleName = "DetailCaption2";
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.StylePriority.UseFont = false;
            this.xrTableCell2.StylePriority.UseTextAlignment = false;
            this.xrTableCell2.Text = "Détails des entrées";
            this.xrTableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell2.Weight = 1D;
            // 
            // tableRow3
            // 
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell37,
            this.tableCell38,
            this.tableCell39,
            this.tableCell40,
            this.tableCell41});
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.Weight = 1D;
            // 
            // tableCell37
            // 
            this.tableCell37.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(136)))), ((int)(((byte)(209)))));
            this.tableCell37.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.tableCell37.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell37.Name = "tableCell37";
            this.tableCell37.StyleName = "DetailCaption2";
            this.tableCell37.StylePriority.UseBackColor = false;
            this.tableCell37.StylePriority.UseBorders = false;
            this.tableCell37.StylePriority.UseFont = false;
            this.tableCell37.StylePriority.UseTextAlignment = false;
            this.tableCell37.Text = "N°";
            this.tableCell37.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell37.Weight = 0.095127942393703535D;
            // 
            // tableCell38
            // 
            this.tableCell38.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(136)))), ((int)(((byte)(209)))));
            this.tableCell38.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell38.Name = "tableCell38";
            this.tableCell38.StyleName = "DetailCaption2";
            this.tableCell38.StylePriority.UseBackColor = false;
            this.tableCell38.StylePriority.UseFont = false;
            this.tableCell38.StylePriority.UseTextAlignment = false;
            this.tableCell38.Text = "Date";
            this.tableCell38.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell38.Weight = 0.19127391262125348D;
            // 
            // tableCell39
            // 
            this.tableCell39.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(136)))), ((int)(((byte)(209)))));
            this.tableCell39.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell39.Name = "tableCell39";
            this.tableCell39.StyleName = "DetailCaption2";
            this.tableCell39.StylePriority.UseBackColor = false;
            this.tableCell39.StylePriority.UseFont = false;
            this.tableCell39.StylePriority.UseTextAlignment = false;
            this.tableCell39.Text = "Quantité";
            this.tableCell39.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell39.Weight = 0.18805909186343131D;
            // 
            // tableCell40
            // 
            this.tableCell40.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(136)))), ((int)(((byte)(209)))));
            this.tableCell40.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell40.Name = "tableCell40";
            this.tableCell40.StyleName = "DetailCaption2";
            this.tableCell40.StylePriority.UseBackColor = false;
            this.tableCell40.StylePriority.UseFont = false;
            this.tableCell40.StylePriority.UseTextAlignment = false;
            this.tableCell40.Text = "Opération";
            this.tableCell40.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell40.Weight = 0.20208664602211121D;
            // 
            // tableCell41
            // 
            this.tableCell41.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(35)))), ((int)(((byte)(136)))), ((int)(((byte)(209)))));
            this.tableCell41.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell41.Name = "tableCell41";
            this.tableCell41.StyleName = "DetailCaption2";
            this.tableCell41.StylePriority.UseBackColor = false;
            this.tableCell41.StylePriority.UseFont = false;
            this.tableCell41.StylePriority.UseTextAlignment = false;
            this.tableCell41.Text = "Partie Impliquée";
            this.tableCell41.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell41.Weight = 0.32345240709950046D;
            // 
            // Detail1
            // 
            this.Detail1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table3});
            this.Detail1.HeightF = 25F;
            this.Detail1.Name = "Detail1";
            // 
            // table3
            // 
            this.table3.LocationFloat = new DevExpress.Utils.PointFloat(75.95837F, 0F);
            this.table3.Name = "table3";
            this.table3.OddStyleName = "DetailData3_Odd";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow4});
            this.table3.SizeF = new System.Drawing.SizeF(731.0416F, 25F);
            // 
            // tableRow4
            // 
            this.tableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell42,
            this.tableCell43,
            this.tableCell44,
            this.tableCell45,
            this.tableCell46});
            this.tableRow4.Name = "tableRow4";
            this.tableRow4.Weight = 11.5D;
            // 
            // tableCell42
            // 
            this.tableCell42.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell42.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell42.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell42.BorderWidth = 1F;
            this.tableCell42.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Number]")});
            this.tableCell42.Name = "tableCell42";
            this.tableCell42.StyleName = "DetailData2";
            this.tableCell42.StylePriority.UseBorderColor = false;
            this.tableCell42.StylePriority.UseBorderDashStyle = false;
            this.tableCell42.StylePriority.UseBorders = false;
            this.tableCell42.StylePriority.UseBorderWidth = false;
            this.tableCell42.StylePriority.UseTextAlignment = false;
            this.tableCell42.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell42.Weight = 0.095127942393703535D;
            // 
            // tableCell43
            // 
            this.tableCell43.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell43.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell43.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell43.BorderWidth = 1F;
            this.tableCell43.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Date]")});
            this.tableCell43.Name = "tableCell43";
            this.tableCell43.StyleName = "DetailData2";
            this.tableCell43.StylePriority.UseBorderColor = false;
            this.tableCell43.StylePriority.UseBorderDashStyle = false;
            this.tableCell43.StylePriority.UseBorders = false;
            this.tableCell43.StylePriority.UseBorderWidth = false;
            this.tableCell43.StylePriority.UseTextAlignment = false;
            this.tableCell43.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell43.Weight = 0.19127391262125348D;
            // 
            // tableCell44
            // 
            this.tableCell44.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell44.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell44.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell44.BorderWidth = 1F;
            this.tableCell44.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Quantity]")});
            this.tableCell44.Name = "tableCell44";
            this.tableCell44.StyleName = "DetailData2";
            this.tableCell44.StylePriority.UseBorderColor = false;
            this.tableCell44.StylePriority.UseBorderDashStyle = false;
            this.tableCell44.StylePriority.UseBorders = false;
            this.tableCell44.StylePriority.UseBorderWidth = false;
            this.tableCell44.StylePriority.UseTextAlignment = false;
            this.tableCell44.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell44.TextFormatString = "{0:#.00}";
            this.tableCell44.Weight = 0.18805909186343131D;
            // 
            // tableCell45
            // 
            this.tableCell45.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell45.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell45.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell45.BorderWidth = 1F;
            this.tableCell45.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Action]")});
            this.tableCell45.Name = "tableCell45";
            this.tableCell45.StyleName = "DetailData2";
            this.tableCell45.StylePriority.UseBorderColor = false;
            this.tableCell45.StylePriority.UseBorderDashStyle = false;
            this.tableCell45.StylePriority.UseBorders = false;
            this.tableCell45.StylePriority.UseBorderWidth = false;
            this.tableCell45.StylePriority.UseTextAlignment = false;
            this.tableCell45.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell45.Weight = 0.20208664602211121D;
            // 
            // tableCell46
            // 
            this.tableCell46.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell46.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell46.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell46.BorderWidth = 1F;
            this.tableCell46.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[UserName]")});
            this.tableCell46.Name = "tableCell46";
            this.tableCell46.StyleName = "DetailData2";
            this.tableCell46.StylePriority.UseBorderColor = false;
            this.tableCell46.StylePriority.UseBorderDashStyle = false;
            this.tableCell46.StylePriority.UseBorders = false;
            this.tableCell46.StylePriority.UseBorderWidth = false;
            this.tableCell46.StylePriority.UseTextAlignment = false;
            this.tableCell46.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell46.Weight = 0.32345244491558239D;
            // 
            // DetailReportQuantityOut
            // 
            this.DetailReportQuantityOut.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.GroupHeader2,
            this.Detail2});
            this.DetailReportQuantityOut.DataMember = "ProductContentsInventoryDetailsOut";
            this.DetailReportQuantityOut.DataSource = this.objectDataSource1;
            this.DetailReportQuantityOut.Level = 1;
            this.DetailReportQuantityOut.Name = "DetailReportQuantityOut";
            // 
            // GroupHeader2
            // 
            this.GroupHeader2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table4});
            this.GroupHeader2.GroupUnion = DevExpress.XtraReports.UI.GroupUnion.WithFirstDetail;
            this.GroupHeader2.HeightF = 56F;
            this.GroupHeader2.Name = "GroupHeader2";
            // 
            // table4
            // 
            this.table4.LocationFloat = new DevExpress.Utils.PointFloat(75.95837F, 0F);
            this.table4.Name = "table4";
            this.table4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2,
            this.tableRow5});
            this.table4.SizeF = new System.Drawing.SizeF(731.0416F, 56F);
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell3});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.Weight = 1D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(144)))), ((int)(((byte)(156)))));
            this.xrTableCell3.BorderColor = System.Drawing.Color.DimGray;
            this.xrTableCell3.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrTableCell3.BorderWidth = 1F;
            this.xrTableCell3.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell3.Multiline = true;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StyleName = "DetailCaption2";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorderColor = false;
            this.xrTableCell3.StylePriority.UseBorderDashStyle = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.StylePriority.UseBorderWidth = false;
            this.xrTableCell3.StylePriority.UseFont = false;
            this.xrTableCell3.StylePriority.UseTextAlignment = false;
            this.xrTableCell3.Text = "Détails des sorties";
            this.xrTableCell3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTableCell3.Weight = 1D;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell47,
            this.tableCell48,
            this.tableCell49,
            this.tableCell50,
            this.tableCell51});
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.Weight = 1D;
            // 
            // tableCell47
            // 
            this.tableCell47.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(144)))), ((int)(((byte)(156)))));
            this.tableCell47.BorderColor = System.Drawing.Color.White;
            this.tableCell47.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.tableCell47.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.tableCell47.BorderWidth = 2F;
            this.tableCell47.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell47.Name = "tableCell47";
            this.tableCell47.StyleName = "DetailCaption2";
            this.tableCell47.StylePriority.UseBackColor = false;
            this.tableCell47.StylePriority.UseBorderColor = false;
            this.tableCell47.StylePriority.UseBorderDashStyle = false;
            this.tableCell47.StylePriority.UseBorders = false;
            this.tableCell47.StylePriority.UseBorderWidth = false;
            this.tableCell47.StylePriority.UseFont = false;
            this.tableCell47.StylePriority.UseTextAlignment = false;
            this.tableCell47.Text = "N°";
            this.tableCell47.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell47.Weight = 0.095127942393703535D;
            // 
            // tableCell48
            // 
            this.tableCell48.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(144)))), ((int)(((byte)(156)))));
            this.tableCell48.BorderColor = System.Drawing.Color.White;
            this.tableCell48.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.tableCell48.BorderWidth = 2F;
            this.tableCell48.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell48.Name = "tableCell48";
            this.tableCell48.StyleName = "DetailCaption2";
            this.tableCell48.StylePriority.UseBackColor = false;
            this.tableCell48.StylePriority.UseBorderColor = false;
            this.tableCell48.StylePriority.UseBorderDashStyle = false;
            this.tableCell48.StylePriority.UseBorderWidth = false;
            this.tableCell48.StylePriority.UseFont = false;
            this.tableCell48.StylePriority.UseTextAlignment = false;
            this.tableCell48.Text = "Date";
            this.tableCell48.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell48.Weight = 0.19127391262125348D;
            // 
            // tableCell49
            // 
            this.tableCell49.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(144)))), ((int)(((byte)(156)))));
            this.tableCell49.BorderColor = System.Drawing.Color.White;
            this.tableCell49.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.tableCell49.BorderWidth = 2F;
            this.tableCell49.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell49.Name = "tableCell49";
            this.tableCell49.StyleName = "DetailCaption2";
            this.tableCell49.StylePriority.UseBackColor = false;
            this.tableCell49.StylePriority.UseBorderColor = false;
            this.tableCell49.StylePriority.UseBorderDashStyle = false;
            this.tableCell49.StylePriority.UseBorderWidth = false;
            this.tableCell49.StylePriority.UseFont = false;
            this.tableCell49.StylePriority.UseTextAlignment = false;
            this.tableCell49.Text = "Quantité";
            this.tableCell49.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell49.Weight = 0.18805909186343131D;
            // 
            // tableCell50
            // 
            this.tableCell50.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(144)))), ((int)(((byte)(156)))));
            this.tableCell50.BorderColor = System.Drawing.Color.White;
            this.tableCell50.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.tableCell50.BorderWidth = 2F;
            this.tableCell50.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell50.Name = "tableCell50";
            this.tableCell50.StyleName = "DetailCaption2";
            this.tableCell50.StylePriority.UseBackColor = false;
            this.tableCell50.StylePriority.UseBorderColor = false;
            this.tableCell50.StylePriority.UseBorderDashStyle = false;
            this.tableCell50.StylePriority.UseBorderWidth = false;
            this.tableCell50.StylePriority.UseFont = false;
            this.tableCell50.StylePriority.UseTextAlignment = false;
            this.tableCell50.Text = "Opération";
            this.tableCell50.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell50.Weight = 0.20208664602211121D;
            // 
            // tableCell51
            // 
            this.tableCell51.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(120)))), ((int)(((byte)(144)))), ((int)(((byte)(156)))));
            this.tableCell51.BorderColor = System.Drawing.Color.White;
            this.tableCell51.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Solid;
            this.tableCell51.BorderWidth = 2F;
            this.tableCell51.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.tableCell51.Name = "tableCell51";
            this.tableCell51.StyleName = "DetailCaption2";
            this.tableCell51.StylePriority.UseBackColor = false;
            this.tableCell51.StylePriority.UseBorderColor = false;
            this.tableCell51.StylePriority.UseBorderDashStyle = false;
            this.tableCell51.StylePriority.UseBorderWidth = false;
            this.tableCell51.StylePriority.UseFont = false;
            this.tableCell51.StylePriority.UseTextAlignment = false;
            this.tableCell51.Text = "Partie Impliquée";
            this.tableCell51.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell51.Weight = 0.32345240709950046D;
            // 
            // Detail2
            // 
            this.Detail2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table5});
            this.Detail2.HeightF = 25F;
            this.Detail2.Name = "Detail2";
            // 
            // table5
            // 
            this.table5.LocationFloat = new DevExpress.Utils.PointFloat(75.95837F, 0F);
            this.table5.Name = "table5";
            this.table5.OddStyleName = "DetailData3_Odd";
            this.table5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow6});
            this.table5.SizeF = new System.Drawing.SizeF(731.0416F, 25F);
            // 
            // tableRow6
            // 
            this.tableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell52,
            this.tableCell53,
            this.tableCell54,
            this.tableCell55,
            this.tableCell56});
            this.tableRow6.Name = "tableRow6";
            this.tableRow6.Weight = 11.5D;
            // 
            // tableCell52
            // 
            this.tableCell52.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell52.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell52.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell52.BorderWidth = 1F;
            this.tableCell52.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Number]")});
            this.tableCell52.Name = "tableCell52";
            this.tableCell52.StyleName = "DetailData2";
            this.tableCell52.StylePriority.UseBorderColor = false;
            this.tableCell52.StylePriority.UseBorderDashStyle = false;
            this.tableCell52.StylePriority.UseBorders = false;
            this.tableCell52.StylePriority.UseBorderWidth = false;
            this.tableCell52.StylePriority.UseTextAlignment = false;
            this.tableCell52.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell52.Weight = 0.095127942393703535D;
            // 
            // tableCell53
            // 
            this.tableCell53.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell53.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell53.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell53.BorderWidth = 1F;
            this.tableCell53.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Date]")});
            this.tableCell53.Name = "tableCell53";
            this.tableCell53.StyleName = "DetailData2";
            this.tableCell53.StylePriority.UseBorderColor = false;
            this.tableCell53.StylePriority.UseBorderDashStyle = false;
            this.tableCell53.StylePriority.UseBorders = false;
            this.tableCell53.StylePriority.UseBorderWidth = false;
            this.tableCell53.StylePriority.UseTextAlignment = false;
            this.tableCell53.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell53.Weight = 0.19127391262125348D;
            // 
            // tableCell54
            // 
            this.tableCell54.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell54.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell54.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell54.BorderWidth = 1F;
            this.tableCell54.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Quantity]")});
            this.tableCell54.Name = "tableCell54";
            this.tableCell54.StyleName = "DetailData2";
            this.tableCell54.StylePriority.UseBorderColor = false;
            this.tableCell54.StylePriority.UseBorderDashStyle = false;
            this.tableCell54.StylePriority.UseBorders = false;
            this.tableCell54.StylePriority.UseBorderWidth = false;
            this.tableCell54.StylePriority.UseTextAlignment = false;
            this.tableCell54.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell54.TextFormatString = "{0:#.00}";
            this.tableCell54.Weight = 0.18805909186343131D;
            // 
            // tableCell55
            // 
            this.tableCell55.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell55.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell55.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell55.BorderWidth = 1F;
            this.tableCell55.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Action]")});
            this.tableCell55.Name = "tableCell55";
            this.tableCell55.StyleName = "DetailData2";
            this.tableCell55.StylePriority.UseBorderColor = false;
            this.tableCell55.StylePriority.UseBorderDashStyle = false;
            this.tableCell55.StylePriority.UseBorders = false;
            this.tableCell55.StylePriority.UseBorderWidth = false;
            this.tableCell55.StylePriority.UseTextAlignment = false;
            this.tableCell55.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell55.Weight = 0.20208664602211121D;
            // 
            // tableCell56
            // 
            this.tableCell56.BorderColor = System.Drawing.Color.DimGray;
            this.tableCell56.BorderDashStyle = DevExpress.XtraPrinting.BorderDashStyle.Dash;
            this.tableCell56.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell56.BorderWidth = 1F;
            this.tableCell56.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[UserName]")});
            this.tableCell56.Name = "tableCell56";
            this.tableCell56.StyleName = "DetailData2";
            this.tableCell56.StylePriority.UseBorderColor = false;
            this.tableCell56.StylePriority.UseBorderDashStyle = false;
            this.tableCell56.StylePriority.UseBorders = false;
            this.tableCell56.StylePriority.UseBorderWidth = false;
            this.tableCell56.StylePriority.UseTextAlignment = false;
            this.tableCell56.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.tableCell56.Weight = 0.32345244491558239D;
            // 
            // ProductContentsInventoryReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.TopMargin,
            this.BottomMargin,
            this.ReportHeader,
            this.Detail,
            this.DetailReportQuantityIn,
            this.DetailReportQuantityOut});
            this.ComponentStorage.AddRange(new System.ComponentModel.IComponent[] {
            this.objectDataSource1});
            this.DataSource = this.objectDataSource1;
            this.Font = new DevExpress.Drawing.DXFont("Arial", 9.75F);
            this.Margins = new DevExpress.Drawing.DXMargins(10F, 10F, 10F, 8.900769F);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = DevExpress.Drawing.Printing.DXPaperKind.A4;
            this.StyleSheet.AddRange(new DevExpress.XtraReports.UI.XRControlStyle[] {
            this.Title,
            this.DetailCaption1,
            this.DetailData1,
            this.DetailCaption2,
            this.DetailData2,
            this.DetailData3_Odd,
            this.PageInfo});
            this.Version = "23.1";
            ((System.ComponentModel.ISupportInitialize)(this.objectDataSource1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

		}

		// Token: 0x04001B01 RID: 6913
		private IContainer components = null;

		// Token: 0x04001B02 RID: 6914
		private ObjectDataSource objectDataSource1;

		// Token: 0x04001B03 RID: 6915
		private XRControlStyle Title;

		// Token: 0x04001B04 RID: 6916
		private XRControlStyle DetailCaption1;

		// Token: 0x04001B05 RID: 6917
		private XRControlStyle DetailData1;

		// Token: 0x04001B06 RID: 6918
		private XRControlStyle DetailCaption2;

		// Token: 0x04001B07 RID: 6919
		private XRControlStyle DetailData2;

		// Token: 0x04001B08 RID: 6920
		private XRControlStyle DetailData3_Odd;

		// Token: 0x04001B09 RID: 6921
		private XRControlStyle PageInfo;

		// Token: 0x04001B0A RID: 6922
		private TopMarginBand TopMargin;

		// Token: 0x04001B0B RID: 6923
		private BottomMarginBand BottomMargin;

		// Token: 0x04001B0C RID: 6924
		private ReportHeaderBand ReportHeader;

		// Token: 0x04001B0D RID: 6925
		private DetailBand Detail;

		// Token: 0x04001B0E RID: 6926
		private DetailReportBand DetailReportQuantityIn;

		// Token: 0x04001B0F RID: 6927
		private GroupHeaderBand GroupHeader1;

		// Token: 0x04001B10 RID: 6928
		private XRTable table2;

		// Token: 0x04001B11 RID: 6929
		private XRTableRow tableRow3;

		// Token: 0x04001B12 RID: 6930
		private XRTableCell tableCell37;

		// Token: 0x04001B13 RID: 6931
		private XRTableCell tableCell38;

		// Token: 0x04001B14 RID: 6932
		private XRTableCell tableCell39;

		// Token: 0x04001B15 RID: 6933
		private XRTableCell tableCell40;

		// Token: 0x04001B16 RID: 6934
		private XRTableCell tableCell41;

		// Token: 0x04001B17 RID: 6935
		private DetailBand Detail1;

		// Token: 0x04001B18 RID: 6936
		private XRTable table3;

		// Token: 0x04001B19 RID: 6937
		private XRTableRow tableRow4;

		// Token: 0x04001B1A RID: 6938
		private XRTableCell tableCell42;

		// Token: 0x04001B1B RID: 6939
		private XRTableCell tableCell43;

		// Token: 0x04001B1C RID: 6940
		private XRTableCell tableCell44;

		// Token: 0x04001B1D RID: 6941
		private XRTableCell tableCell45;

		// Token: 0x04001B1E RID: 6942
		private XRTableCell tableCell46;

		// Token: 0x04001B1F RID: 6943
		private DetailReportBand DetailReportQuantityOut;

		// Token: 0x04001B20 RID: 6944
		private GroupHeaderBand GroupHeader2;

		// Token: 0x04001B21 RID: 6945
		private XRTable table4;

		// Token: 0x04001B22 RID: 6946
		private XRTableRow tableRow5;

		// Token: 0x04001B23 RID: 6947
		private XRTableCell tableCell47;

		// Token: 0x04001B24 RID: 6948
		private XRTableCell tableCell48;

		// Token: 0x04001B25 RID: 6949
		private XRTableCell tableCell49;

		// Token: 0x04001B26 RID: 6950
		private XRTableCell tableCell50;

		// Token: 0x04001B27 RID: 6951
		private XRTableCell tableCell51;

		// Token: 0x04001B28 RID: 6952
		private DetailBand Detail2;

		// Token: 0x04001B29 RID: 6953
		private XRTable table5;

		// Token: 0x04001B2A RID: 6954
		private XRTableRow tableRow6;

		// Token: 0x04001B2B RID: 6955
		private XRTableCell tableCell52;

		// Token: 0x04001B2C RID: 6956
		private XRTableCell tableCell53;

		// Token: 0x04001B2D RID: 6957
		private XRTableCell tableCell54;

		// Token: 0x04001B2E RID: 6958
		private XRTableCell tableCell55;

		// Token: 0x04001B2F RID: 6959
		private XRTableCell tableCell56;

		// Token: 0x04001B30 RID: 6960
		private XRTable table1;

		// Token: 0x04001B31 RID: 6961
		private XRTableRow tableRow1;

		// Token: 0x04001B32 RID: 6962
		private XRTableCell tableCell1;

		// Token: 0x04001B33 RID: 6963
		private XRTableCell tableCell2;

		// Token: 0x04001B34 RID: 6964
		private XRTableCell tableCell3;

		// Token: 0x04001B35 RID: 6965
		private XRTableCell tableCell4;

		// Token: 0x04001B36 RID: 6966
		private XRTableCell tableCell5;

		// Token: 0x04001B37 RID: 6967
		private XRTableRow tableRow2;

		// Token: 0x04001B38 RID: 6968
		private XRTableCell tableCell19;

		// Token: 0x04001B39 RID: 6969
		private XRTableCell tableCell20;

		// Token: 0x04001B3A RID: 6970
		private XRTableCell tableCellInQuantity;

		// Token: 0x04001B3B RID: 6971
		private XRTableCell tableCellOutQuantity;

		// Token: 0x04001B3C RID: 6972
		private XRTableCell tableCell23;

		// Token: 0x04001B3D RID: 6973
		private XRTableCell tableCell30;

		// Token: 0x04001B3E RID: 6974
		public XRTable xrTable3;

		// Token: 0x04001B3F RID: 6975
		public XRTableRow xrTableRow3;

		// Token: 0x04001B40 RID: 6976
		public XRTableCell xrTableCell13;

		// Token: 0x04001B41 RID: 6977
		public XRTableRow xrTableRow4;

		// Token: 0x04001B42 RID: 6978
		public XRTableCell xrTableCell1;

		// Token: 0x04001B43 RID: 6979
		public XRTableRow xrTableRow8;

		// Token: 0x04001B44 RID: 6980
		public XRTableCell xrTableCell5;

		// Token: 0x04001B45 RID: 6981
		public XRTableRow xrTableRow9;

		// Token: 0x04001B46 RID: 6982
		public XRTableCell xrTableCell14;

		// Token: 0x04001B47 RID: 6983
		public XRTableRow xrTableRow10;

		// Token: 0x04001B48 RID: 6984
		public XRTableCell xrTableCell15;

		// Token: 0x04001B49 RID: 6985
		public XRTable xrTable2;

		// Token: 0x04001B4A RID: 6986
		public XRTableRow xrTableRow5;

		// Token: 0x04001B4B RID: 6987
		public XRTableCell Cell_ReportName;

		// Token: 0x04001B4C RID: 6988
		public XRTableRow xrTableRow6;

		// Token: 0x04001B4D RID: 6989
		public XRTableCell xrTableCell4;

		// Token: 0x04001B4E RID: 6990
		public XRTableRow xrTableRow7;

		// Token: 0x04001B4F RID: 6991
		public XRTableCell Cell_Filters;

		// Token: 0x04001B50 RID: 6992
		public XRPictureBox xrPictureBox2;

		// Token: 0x04001B51 RID: 6993
		private XRTableRow xrTableRow1;

		// Token: 0x04001B52 RID: 6994
		private XRTableCell xrTableCell2;

		// Token: 0x04001B53 RID: 6995
		private XRTableRow xrTableRow2;

		// Token: 0x04001B54 RID: 6996
		private XRTableCell xrTableCell3;
	}
}
