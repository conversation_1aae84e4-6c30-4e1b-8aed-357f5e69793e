﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class InvoiceDetailReportModel : ProductTransactionReportModel
    {
        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Remise %")]
        public double DiscountPercentage { get; set; }

        [Display(Name = "Total HT")]
        public double Net
        {
            get
            {
                return base.Total + this.Tax - this.Discount;
            }
        }

        [Display(Name = "Taxe")]
        public double Tax { get; set; }

        [Display(Name = "Taxe %")]
        public double TaxPercentage { get; set; }
    }
}
