﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class TotalPurchaseInvoicesReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Catégorie Fournisseur")]
        public string VendCat { get; set; }

        [Display(Name = "Dépôt")]
        public string InvStore { get; set; }

        [Display(Name = "Fournisseur")]
        public string Vendor { get; set; }

        [Display(Name = "Numéro de facture")]
        public int InvoiceID { get; set; }

        [Display(Name = "Date")]
        public DateTime InvoiceDate { get; set; }

        [Display(Name = "État du paiement")]
        public InvoicesPayStatus PaidFlag
        {
            get
            {
                bool flag = this.PaidAmount == 0.0;
                InvoicesPayStatus result;
                if (flag)
                {
                    result = InvoicesPayStatus.NotPaid;
                }
                else
                {
                    bool flag2 = this.PaidAmount > 0.0 && this.Remaining > 0.0;
                    if (flag2)
                    {
                        result = InvoicesPayStatus.PartialyPaid;
                    }
                    else
                    {
                        result = InvoicesPayStatus.Paid;
                    }
                }
                return result;
            }
        }

        [Display(Name = "Total")]
        public double TotalAmount { get; set; }

        [Display(Name = "Frais supplémentaires")]
        public double Expences { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Taxes")]
        public double Taxes { get; set; }

        [Display(Name = "Net")]
        public double NetAmount { get; set; }

        [Display(Name = "Montant payé")]
        public double PaidAmount { get; set; }

        [Display(Name = "Reste à payer")]
        public double Remaining
        {
            get
            {
                return this.NetAmount - this.PaidAmount;
            }
        }

        [Display(Name = "Date d'échéance")]
        public DateTime? DueDate { get; set; }
    }
}
