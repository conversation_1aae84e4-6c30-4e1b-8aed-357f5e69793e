﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class JournalDetailReportModel
    {
        [Display(Name = "Déclaration")]
        public string Statement { get; set; }

        [Display(Name = "Compte")]
        public string Account { get; set; }

        [Display(Name = "Débit")]
        public double Debit { get; set; }

        [Display(Name = "Crédit")]
        public double Credit { get; set; }

        [Display(Name = "Débit Local")]
        public double LocalDebit
        {
            get
            {
                return this.Debit * this.CurrancyRate;
            }
        }

        [Display(Name = "Crédit Local")]
        public double LocalCredit
        {
            get
            {
                return this.Credit * this.CurrancyRate;
            }
        }

        [Display(Name = "Devise")]
        public string Currancy { get; set; }

        [Display(Name = "Taux de Change")]
        public double CurrancyRate { get; set; }
    }

}
