﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class PersonalInfoSummaryModel
    {
        [Display(Name = "Code")]
        public int ID { get; set; }

        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Ville")]
        public string City { get; set; }

        [Display(Name = "Adresse")]
        public string Address { get; set; }

        [Display(Name = "Téléphone")]
        public string Phone { get; set; }

        [Display(Name = "Mobile")]
        public string Mobile { get; set; }

        [Display(Name = "Total des factures")]
        public double TotalInvoices { get; set; }

        [Display(Name = "Total des factures de retour")]
        public double TotalReturnInvoices { get; set; }

        [Display(Name = "Total des reçus")]
        public double TotalCashIn { get; set; }

        [Display(Name = "Total des paiements")]
        public double TotalCashOut { get; set; }

        [Display(Name = "Créditeur")]
        public double BalanceCredit { get; set; }

        [Display(Name = "Débiteur")]
        public double BalanceDebit { get; set; }
    }
}
