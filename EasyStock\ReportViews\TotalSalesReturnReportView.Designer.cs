﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000353 RID: 851
	public partial class TotalSalesReturnReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x06001446 RID: 5190 RVA: 0x0016AC8C File Offset: 0x00168E8C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001447 RID: 5191 RVA: 0x0016ACC4 File Offset: 0x00168EC4
		private void InitializeComponent()
		{
			base.SuspendLayout();
			this.documentViewer1.Location = new global::System.Drawing.Point(0, 49);
			this.documentViewer1.Size = new global::System.Drawing.Size(800, 379);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			base.Name = "TotalSalesReturnReportView";
			this.Text = "Total des factures de retour des ventes";
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x040019CA RID: 6602
		private global::System.ComponentModel.IContainer components = null;
	}
}
