﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{039224D9-0485-4303-842A-06B5EEFDF284}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>EasyStock.HR</RootNamespace>
    <AssemblyName>EasyStock.HR</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BLL\AbsenceRegulationBLL.cs" />
    <Compile Include="BLL\DepartmentBLL.cs" />
    <Compile Include="BLL\EmpAttendanceBLL.cs" />
    <Compile Include="BLL\EmployeeBLL.cs" />
    <Compile Include="BLL\ExternalBLL.cs" />
    <Compile Include="BLL\GroupBLL.cs" />
    <Compile Include="BLL\JobBLL.cs" />
    <Compile Include="BLL\OvertimeAndDelayRegulationBLL.cs" />
    <Compile Include="BLL\PenaltyRewardBLL.cs" />
    <Compile Include="BLL\ReferenceBLL.cs" />
    <Compile Include="BLL\SalaryExtensionBLL.cs" />
    <Compile Include="BLL\ShiftBLL.cs" />
    <Compile Include="BLL\WorkLeaveReturnBLL.cs" />
    <Compile Include="CalculationType.cs" />
    <Compile Include="Classes\Settings.cs" />
    <Compile Include="Class\Static.cs" />
    <Compile Include="ContractTypes.cs" />
    <Compile Include="CustomModels\CustomAccount.cs" />
    <Compile Include="CustomModels\CustomBranch.cs" />
    <Compile Include="EAbsencePaid.cs" />
    <Compile Include="EAbsenceType.cs" />
    <Compile Include="ECalculationType.cs" />
    <Compile Include="EDeductionType.cs" />
    <Compile Include="EMethod.cs" />
    <Compile Include="EmpState.cs" />
    <Compile Include="EOvertimeDelay.cs" />
    <Compile Include="ESalaryCalculation.cs" />
    <Compile Include="ESalaryPeriod.cs" />
    <Compile Include="EShiftType.cs" />
    <Compile Include="EStatus.cs" />
    <Compile Include="EType.cs" />
    <Compile Include="EVacationType.cs" />
    <Compile Include="ExtensionType.cs" />
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="GenderType.cs" />
    <Compile Include="HRDataContext.cs" />
    <Compile Include="MainViews\MasterView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainViews\MasterView.Designer.cs">
      <DependentUpon>MasterView.cs</DependentUpon>
    </Compile>
    <Compile Include="MaritalStatus.cs" />
    <Compile Include="Migrations\addempphoto.cs" />
    <Compile Include="Migrations\addtableWorkLeaveReturn.cs" />
    <Compile Include="Migrations\allowEmpPropertyExpensesAccNotRequired.cs" />
    <Compile Include="Migrations\allowEmpPropertyNull.cs" />
    <Compile Include="Migrations\changesEmployee.cs" />
    <Compile Include="Migrations\changesEmployeeexpensesAndAcuredAccountToId.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Migrations\initial.cs" />
    <Compile Include="Migrations\penaltyrewardChangepropEmp.cs" />
    <Compile Include="Migrations\penaltyrewardTable.cs" />
    <Compile Include="Migrations\tbReferenceandEmpProperties.cs" />
    <Compile Include="Migrations\UpdateEmpSalaryExtientionTable.cs" />
    <Compile Include="Migrations\UpdateLoanTable.cs" />
    <Compile Include="MilitarilyStatus.cs" />
    <Compile Include="Models\AbsenceRegulation.cs" />
    <Compile Include="Models\AbsenceRegulations.cs" />
    <Compile Include="Models\Department.cs" />
    <Compile Include="Models\Departments.cs" />
    <Compile Include="Models\EmpAbsence.cs" />
    <Compile Include="Models\EmpAbsences.cs" />
    <Compile Include="Models\EmpAttendance.cs" />
    <Compile Include="Models\EmpAttendances.cs" />
    <Compile Include="Models\EmpLoan.cs" />
    <Compile Include="Models\EmpLoanDetails.cs" />
    <Compile Include="Models\EmpLoans.cs" />
    <Compile Include="Models\Employee.cs" />
    <Compile Include="Models\Employees.cs" />
    <Compile Include="Models\EmpMission.cs" />
    <Compile Include="Models\EmpMissions.cs" />
    <Compile Include="Models\EmpOvertimeDelay.cs" />
    <Compile Include="Models\EmpOvertimeDelays.cs" />
    <Compile Include="Models\EmpSalaryExtension.cs" />
    <Compile Include="Models\EmpSalaryExtensions.cs" />
    <Compile Include="Models\EmpShift.cs" />
    <Compile Include="Models\EmpShifts.cs" />
    <Compile Include="Models\EmpVacation.cs" />
    <Compile Include="Models\EmpVacations.cs" />
    <Compile Include="Models\Group.cs" />
    <Compile Include="Models\Groups.cs" />
    <Compile Include="Models\Job.cs" />
    <Compile Include="Models\Jobs.cs" />
    <Compile Include="Models\OfficialVacation.cs" />
    <Compile Include="Models\OfficialVacations.cs" />
    <Compile Include="Models\OvertimeAndDelayRegulation.cs" />
    <Compile Include="Models\OvertimeAndDelayRegulationMinutesTable.cs" />
    <Compile Include="Models\OvertimeAndDelayRegulationMinutesTables.cs" />
    <Compile Include="Models\OvertimeAndDelayRegulations.cs" />
    <Compile Include="Models\PenaltyReward.cs" />
    <Compile Include="Models\PenaltyRewards.cs" />
    <Compile Include="Models\Reference.cs" />
    <Compile Include="Models\References.cs" />
    <Compile Include="Models\SalaryExtension.cs" />
    <Compile Include="Models\SalaryExtensions.cs" />
    <Compile Include="Models\SalaryRegulation.cs" />
    <Compile Include="Models\SalaryRegulationExtension.cs" />
    <Compile Include="Models\SalaryRegulationExtensions.cs" />
    <Compile Include="Models\SalaryRegulations.cs" />
    <Compile Include="Models\Shift.cs" />
    <Compile Include="Models\ShiftDay.cs" />
    <Compile Include="Models\ShiftDays.cs" />
    <Compile Include="Models\Shifts.cs" />
    <Compile Include="Models\TimeTable.cs" />
    <Compile Include="Models\TimeTables.cs" />
    <Compile Include="Models\WorkLeaveReturn.cs" />
    <Compile Include="Models\WorkLeaveReturns.cs" />
    <Compile Include="NavigationObjects.cs" />
    <Compile Include="PenaltyRewardType.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="ReferenceType.cs" />
    <Compile Include="Views\AbcenceRegulationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\AbcenceRegulationView.Designer.cs">
      <DependentUpon>AbcenceRegulationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\DepartmentView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\DepartmentView.Designer.cs">
      <DependentUpon>DepartmentView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpAbsenceListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpAbsenceListView.Designer.cs">
      <DependentUpon>EmpAbsenceListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpAbsenceView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpAbsenceView.Designer.cs">
      <DependentUpon>EmpAbsenceView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpAttendenceView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpAttendenceView.Designer.cs">
      <DependentUpon>EmpAttendenceView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpDelayListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpDelayListView.Designer.cs">
      <DependentUpon>EmpDelayListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpDelayView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpDelayView.Designer.cs">
      <DependentUpon>EmpDelayView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpListView.Designer.cs">
      <DependentUpon>EmpListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpLoanListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpLoanListView.Designer.cs">
      <DependentUpon>EmpLoanListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpLoanView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpLoanView.Designer.cs">
      <DependentUpon>EmpLoanView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmployeeView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmployeeView.Designer.cs">
      <DependentUpon>EmployeeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpMissionListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpMissionListView.Designer.cs">
      <DependentUpon>EmpMissionListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpMissionView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpMissionView.Designer.cs">
      <DependentUpon>EmpMissionView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpOvertimeListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpOvertimeListView.Designer.cs">
      <DependentUpon>EmpOvertimeListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpOvertimeView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpOvertimeView.Designer.cs">
      <DependentUpon>EmpOvertimeView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpShiftListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpShiftListView.Designer.cs">
      <DependentUpon>EmpShiftListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpShiftView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpShiftView.Designer.cs">
      <DependentUpon>EmpShiftView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpVacationListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpVacationListView.Designer.cs">
      <DependentUpon>EmpVacationListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpVacationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpVacationView.Designer.cs">
      <DependentUpon>EmpVacationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\EmpView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\EmpView.Designer.cs">
      <DependentUpon>EmpView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\GroupView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\GroupView.Designer.cs">
      <DependentUpon>GroupView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\JobView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\JobView.Designer.cs">
      <DependentUpon>JobView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\OfficialVacationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\OfficialVacationView.Designer.cs">
      <DependentUpon>OfficialVacationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\OvertimeAndDelayRegulationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\OvertimeAndDelayRegulationView.Designer.cs">
      <DependentUpon>OvertimeAndDelayRegulationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyListView.Designer.cs">
      <DependentUpon>PenaltyListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\PenaltyView.Designer.cs">
      <DependentUpon>PenaltyView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardListView.Designer.cs">
      <DependentUpon>RewardListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\PenaltyRewardView\RewardView.Designer.cs">
      <DependentUpon>RewardView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalaryExtensionView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalaryExtensionView.Designer.cs">
      <DependentUpon>SalaryExtensionView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\SalaryRegulationView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\SalaryRegulationView.Designer.cs">
      <DependentUpon>SalaryRegulationView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\ShiftView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\ShiftView.Designer.cs">
      <DependentUpon>ShiftView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\TimeTableView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\TimeTableView.Designer.cs">
      <DependentUpon>TimeTableView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveListView.Designer.cs">
      <DependentUpon>WorkLeaveListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkLeaveView.Designer.cs">
      <DependentUpon>WorkLeaveView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkReturnListView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkReturnListView.Designer.cs">
      <DependentUpon>WorkReturnListView.cs</DependentUpon>
    </Compile>
    <Compile Include="Views\Work\WorkReturnView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Views\Work\WorkReturnView.Designer.cs">
      <DependentUpon>WorkReturnView.cs</DependentUpon>
    </Compile>
    <Compile Include="WorkLeaveReturnType.cs" />
    <Compile Include="XtraForm1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="XtraForm1.Designer.cs">
      <DependentUpon>XtraForm1.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\addempphoto.resx">
      <DependentUpon>addempphoto.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\addtableWorkLeaveReturn.resx">
      <DependentUpon>addtableWorkLeaveReturn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\allowEmpPropertyExpensesAccNotRequired.resx">
      <DependentUpon>allowEmpPropertyExpensesAccNotRequired.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\allowEmpPropertyNull.resx">
      <DependentUpon>allowEmpPropertyNull.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\changesEmployee.resx">
      <DependentUpon>changesEmployee.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\changesEmployeeexpensesAndAcuredAccountToId.resx">
      <DependentUpon>changesEmployeeexpensesAndAcuredAccountToId.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\initial.resx">
      <DependentUpon>initial.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\penaltyrewardChangepropEmp.resx">
      <DependentUpon>penaltyrewardChangepropEmp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\penaltyrewardTable.resx">
      <DependentUpon>penaltyrewardTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\tbReferenceandEmpProperties.resx">
      <DependentUpon>tbReferenceandEmpProperties.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\UpdateEmpSalaryExtientionTable.resx">
      <DependentUpon>UpdateEmpSalaryExtientionTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Migrations\UpdateLoanTable.resx">
      <DependentUpon>UpdateLoanTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\TimeTableView.resx">
      <DependentUpon>TimeTableView.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <EmbeddedResource Include="Views\AbcenceRegulationView.resx">
      <DependentUpon>AbcenceRegulationView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\DepartmentView.resx">
      <DependentUpon>DepartmentView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpAbsenceListView.resx">
      <DependentUpon>EmpAbsenceListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpAbsenceView.resx">
      <DependentUpon>EmpAbsenceView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpDelayListView.resx">
      <DependentUpon>EmpDelayListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpDelayView.resx">
      <DependentUpon>EmpDelayView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpLoanListView.resx">
      <DependentUpon>EmpLoanListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpLoanView.resx">
      <DependentUpon>EmpLoanView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmployeeView.resx">
      <DependentUpon>EmployeeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpMissionListView.resx">
      <DependentUpon>EmpMissionListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpMissionView.resx">
      <DependentUpon>EmpMissionView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpOvertimeListView.resx">
      <DependentUpon>EmpOvertimeListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpOvertimeView.resx">
      <DependentUpon>EmpOvertimeView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpShiftListView.resx">
      <DependentUpon>EmpShiftListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpShiftView.resx">
      <DependentUpon>EmpShiftView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpVacationListView.resx">
      <DependentUpon>EmpVacationListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpVacationView.resx">
      <DependentUpon>EmpVacationView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\EmpView.resx">
      <DependentUpon>EmpView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\GroupView.resx">
      <DependentUpon>GroupView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\JobView.resx">
      <DependentUpon>JobView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\OfficialVacationView.resx">
      <DependentUpon>OfficialVacationView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\OvertimeAndDelayRegulationView.resx">
      <DependentUpon>OvertimeAndDelayRegulationView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PenaltyRewardView\PenaltyListView.resx">
      <DependentUpon>PenaltyListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PenaltyRewardView\PenaltyView.resx">
      <DependentUpon>PenaltyView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PenaltyRewardView\RewardListView.resx">
      <DependentUpon>RewardListView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\PenaltyRewardView\RewardView.resx">
      <DependentUpon>RewardView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalaryExtensionView.resx">
      <DependentUpon>SalaryExtensionView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\SalaryRegulationView.resx">
      <DependentUpon>SalaryRegulationView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Views\ShiftView.resx">
      <DependentUpon>ShiftView.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.AbsenceRegulation.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.AbsenceRegulations.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Department.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Departments.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpAbsence.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpAbsences.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpAttendance.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpAttendances.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpLoan.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpLoanDetails.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpLoans.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Employee.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Employees.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpMission.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpMissions.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpOvertimeDelay.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpOvertimeDelays.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpSalaryExtension.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpSalaryExtensions.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpShift.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpShifts.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpVacation.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.EmpVacations.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Group.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Groups.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Job.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Jobs.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.OfficialVacation.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.OfficialVacations.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulation.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulationMinutesTable.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulationMinutesTables.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.OvertimeAndDelayRegulations.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.PenaltyReward.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.PenaltyRewards.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Reference.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.References.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.SalaryExtension.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.SalaryExtensions.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.SalaryRegulation.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.SalaryRegulationExtension.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.SalaryRegulationExtensions.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.SalaryRegulations.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Shift.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.ShiftDay.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.ShiftDays.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.Shifts.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.TimeTable+LeaveDayType.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.TimeTable.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.TimeTables.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.WorkLeaveReturn.datasource" />
    <None Include="Properties\DataSources\EasyStock.HR.Models.WorkLeaveReturns.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\EasyStock.Common\EasyStock.Common.csproj">
      <Project>{09d55fd4-2e33-4bf6-965c-f7f7fe7fe0b2}</Project>
      <Name>EasyStock.Common</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\edit_32x32.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\edit_32x321.png" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.Desktop.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Drawing.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v19.2.Core, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraBars.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v19.2, Version=19.2.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\delete.svg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\imageimport.svg" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
  </Target>
  <Import Project="..\packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('..\packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
</Project>