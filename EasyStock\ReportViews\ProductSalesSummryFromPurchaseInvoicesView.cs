﻿using DevExpress.Utils;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Controls;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Views;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace EasyStock.ReportViews
{
    public partial class ProductSalesSummryFromPurchaseInvoicesView : ReportForm
    {
        public ProductSalesSummryFromPurchaseInvoicesView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Vendor,
            ReportFilter.Product
        }, typeof(SalesSummeryOfPurchaseInvoicesReportModel))
        {
            this.InitializeComponent();
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.gridView1.DataSourceChanged += this.GridView1_DataSourceChanged;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            bool flag = this.gridView1.Columns.Count <= 0;
            if (!flag)
            {
                this.gridView1.Columns["Price"].DisplayFormat.FormatType = FormatType.Numeric;
                this.gridView1.Columns["Price"].DisplayFormat.FormatString = "N2";
                this.gridView1.Columns["Total"].DisplayFormat.FormatType = FormatType.Numeric;
                this.gridView1.Columns["Total"].DisplayFormat.FormatString = "N2";
                this.gridView1.Columns["TotalSold"].DisplayFormat.FormatType = FormatType.Numeric;
                this.gridView1.Columns["TotalSold"].DisplayFormat.FormatString = "N2";
            }
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            if ((info.InRow || info.InRowCell) && view.GetFocusedRowCellValue("ID") is int id)
            {
                PurchaseInvoiceForm.Instance.GoTo(id);
                HomeForm.OpenForm(PurchaseInvoiceForm.Instance);
            }
        }

        internal override bool ValidateFilters()
        {
            return true;
        }

        private int[] SelectedProducts
        {
            get
            {
                GridPopupContainerControl products = base.FiltersForm.Products;
                int[] result;
                if (products == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = products.EditValue;
                    result = ((editValue != null) ? editValue.ToArray<int>() : null);
                }
                return result;
            }
        }

        private int[] SelectedVendors
        {
            get
            {
                GridPopupContainerControl vendors = base.FiltersForm.Vendors;
                int[] result;
                if (vendors == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = vendors.EditValue;
                    result = ((editValue != null) ? editValue.ToArray<int>() : null);
                }
                return result;
            }
        }

        public override IQueryable GetQuery()
        {
            IQueryable<Vendor> vendors = db.Vendors.AsQueryable();
            IQueryable<PurchaseInvoice> purchaseInvoices = db.PurchaseInvoices.AsQueryable();
            IQueryable<Product> products = db.Products.AsQueryable();
            if (base.FiltersForm.hasProducts)
            {
                products = products.Where((Product x) => SelectedProducts.Contains(x.ID));
            }
            if (base.FiltersForm.hasVendors)
            {
                vendors = vendors.Where((Vendor x) => SelectedVendors.Contains(x.ID));
            }
            if (base.FiltersForm.hasStartDate)
            {
                purchaseInvoices = purchaseInvoices.Where((PurchaseInvoice x) => DbFunctions.TruncateTime(x.Date) >= FiltersForm.startDate);
            }
            if (base.FiltersForm.hasEndtDate)
            {
                purchaseInvoices = purchaseInvoices.Where((PurchaseInvoice x) => DbFunctions.TruncateTime(x.Date) <= FiltersForm.endtDate);
            }
            return from x in db.ProductTransactions
                   where (int)x.TransactionState == 2
                   select x into pt
                   join pr in products on pt.ProductID equals pr.ID
                   join pi in purchaseInvoices on pt.BillID equals pi.ID
                   join v in vendors on pi.VendorID equals v.ID
                   join u in db.ProductUnits.Include((ProductUnit x) => x.UnitName) on pt.UnitID equals u.ID
                   from size in db.ProductSizes.Where((ProductSize x) => (int?)x.ID == pt.SizeID).DefaultIfEmpty()
                   from color in db.ProductColors.Where((ProductColor x) => (int?)x.ID == pt.ColorID).DefaultIfEmpty()
                   select new SalesSummeryOfPurchaseInvoicesReportModel
                   {
                       VendorID = v.ID,
                       VendorName = v.Name,
                       DueDate = pi.DueDate,
                       ID = pi.ID,
                       Code = pi.Code,
                       Date = pi.Date,
                       ProductID = pt.ProductID,
                       ProductName = pr.Name,
                       Serial = pt.Serial,
                       Color = ((color == null) ? "" : color.Name),
                       Size = ((size == null) ? "" : size.Name),
                       Expire = pt.Expire,
                       Price = pt.Price,
                       Quantity = pt.Quantity * pt.Factor,
                       QuantitySold = ((from x in db.ProductTransactions
                                        where (int)x.TransactionState == 2
                                        where x.ParentTransactionID == (int?)pt.ID
                                        select x).Sum((ProductTransaction x) => (double?)x.Quantity * (double?)x.Factor) ?? 0.0),
                       TotalSold = ((from x in db.ProductTransactions
                                     where (int)x.TransactionState == 2
                                     where x.ParentTransactionID == (int?)pt.ID
                                     select x).Sum((ProductTransaction x) => (double?)x.Price * ((double?)x.Quantity * (double?)x.Factor)) ?? 0.0)
                   };
        }


    }
}
