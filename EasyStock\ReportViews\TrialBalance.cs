﻿using DevExpress.Data;
using DevExpress.Utils;
using DevExpress.Utils.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;

namespace EasyStock.ReportViews
{
    public partial class TrialBalance : ReportForm
    {
        public TrialBalance() : base(new ReportFilter[]
        {
            ReportFilter.Date
        }, typeof(ProductBalanceInStoreReportModel))
        {
            this.InitializeComponent();
            base.Load += this.TrialBalance_Load;
            DateTime date = DateTime.Now.AddMonths(-1);
            int lastDayOfMonth = DateTime.DaysInMonth(date.Year, date.Month);
            base.FiltersForm.FromDate.EditValue = new DateTime(date.Year, date.Month, 1);
            base.FiltersForm.ToDate.EditValue = new DateTime(date.Year, date.Month, lastDayOfMonth);
            this.bandedGridView = new BandedGridView();
            this.gridControl1.ViewCollection.Clear();
            this.gridControl1.MainView = this.bandedGridView;
            this.gridControl1.ViewCollection.Add(this.bandedGridView);
        }

        private void TrialBalance_Load(object sender, EventArgs e)
        {
        }

        private void AddTextBoxes()
        {
            this.BalanceTextEdit.Properties.ReadOnly = true;
            this.TotalBuyTextEdit.Properties.ReadOnly = true;
            this.TotalSellTextEdit.Properties.ReadOnly = true;
            this.CountTextEdit.Properties.ReadOnly = true;
            this.layoutForTotal = new LayoutControl();
            this.layoutForTotal.OptionsPrint.OldPrinting = true;
            this.layoutForTotal.MaximumSize = new Size(300, 0);
            this.layoutForTotal.Padding = new System.Windows.Forms.Padding(0);
            LayoutControlGroup totalGroup = this.Root.AddGroup("Totaux");
            totalGroup.Padding = new DevExpress.XtraLayout.Utils.Padding(0);
            totalGroup.AddItem(" ", this.layoutForTotal);
            this.Root.Add(totalGroup);
            this.Root.AddItem(new EmptySpaceItem(), totalGroup, InsertType.Left);
            this.layoutForTotal.AddItem("Nombre d'articles", this.CountTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Solde total", this.BalanceTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Total des prix de vente", this.TotalSellTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Total des prix d'achat", this.TotalBuyTextEdit).TextLocation = Locations.Right;
            this.SetTextAlignment();
        }

        private void SetTextAlignment()
        {
            this.layoutForTotal.Items.ForEach(delegate (BaseLayoutItem x)
            {
                x.AppearanceItemCaption.TextOptions.HAlignment = HorzAlignment.Far;
            });
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void Print()
        {
            GridReportP.Print(this.layoutControl1, this.Text, this.labelControl1.Text, false);
        }

        public override void RefreshDataSource()
        {
            if (!ValidateFilters())
            {
                return;
            }
            IQueryable<JournalDetail> BeforeEntries = from x in db.JournalDetails.Include(x => x.Account).Include(x => x.Journal)
                                                      where DbFunctions.TruncateTime(x.Journal.Date) < FiltersForm.FromDate.DateTime.Date
                                                      select x;
            IQueryable<JournalDetail> TransEntries = from x in db.JournalDetails.Include(x => x.Account).Include(x => x.Journal)
                                                     where DbFunctions.TruncateTime(x.Journal.Date) >= FiltersForm.FromDate.DateTime.Date && DbFunctions.TruncateTime(x.Journal.Date) <= FiltersForm.ToDate.DateTime.Date
                                                     select x;
            var testQ1 = from be in BeforeEntries
                         group be by be.Account.Number into g
                         select new
                         {
                             ID = g.Key,
                             Credit = 0.0,
                             Debit = 0.0,
                             BeforeCredit = g.Sum(x => (double?)(x.Credit * x.CurrencyRate) ?? 0.00),
                             BeforeDebit = g.Sum(x => (double?)(x.Debit * x.CurrencyRate) ?? 0.00)
                         };
            var testQ2 = from be in TransEntries
                         group be by be.Account.Number into g
                         select new
                         {
                             ID = g.Key,
                             Credit = g.Sum(x => (double?)(x.Credit * x.CurrencyRate) ?? 0.00),
                             Debit = g.Sum(x => (double?)(x.Debit * x.CurrencyRate) ?? 0.00),
                             BeforeCredit = 0.0,
                             BeforeDebit = 0.0
                         };
            testQ1 = testQ1.Concat(testQ2);
            var testQ3 = (from t1 in testQ1.ToList()
                          group t1 by t1.ID into g
                          select new
                          {
                              ID = g.Key,
                              IsParent = false,
                              Credit = g.Sum(x => (double?)(x.Credit) ?? 0.00),
                              Debit = g.Sum(x => (double?)(x.Debit) ?? 0.00),
                              BeforeCredit = g.Sum(x => (double?)(x.BeforeCredit) ?? 0.00),
                              BeforeDebit = g.Sum(x => (double?)(x.BeforeDebit) ?? 0.00)
                          }).ToList();
            IQueryable<string> ParentAccounts = from p in db.Accounts
                                                where db.Accounts.Where((Account c) => c.ParentID == (int?)p.ID).Count() > 0
                                                select p into x
                                                select x.Number;
            var testQ4 = (from be in ParentAccounts.ToList()
                          select new
                          {
                              ID = be,
                              IsParent = true,
                              Credit = testQ3.Where(x => x.ID.ToString().StartsWith(be.ToString())).Sum(x => x.Credit),
                              Debit = testQ3.Where(x => x.ID.ToString().StartsWith(be.ToString())).Sum(x => x.Debit),
                              BeforeCredit = testQ3.Where(x => x.ID.ToString().StartsWith(be.ToString())).Sum(x => x.BeforeCredit),
                              BeforeDebit = testQ3.Where(x => x.ID.ToString().StartsWith(be.ToString())).Sum(x => x.BeforeDebit)
                          }).ToList();
            var accountsList = (from ac in db.Accounts
                                from pac in (from x in db.Accounts
                                             where (int?)x.ID == ac.ParentID
                                             select x.Number).DefaultIfEmpty("0")
                                select new
                                {
                                    ID = ac.Number,
                                    ParentID = pac,
                                    Name = ac.Name,
                                    Level = ac.Number.Length - 3
                                }).ToList();
            testQ4.AddRange(testQ3);
            IEnumerable<TrialBalanceReportModel> query5 = from t in testQ4
                                                          from a in accountsList.Where(x => x.ID == t.ID).DefaultIfEmpty()
                                                          select new TrialBalanceReportModel
                                                          {
                                                              ID = t.ID.ToString(),
                                                              Name = ((a != null) ? (".".PadLeft(a?.Level ?? 0) + a.Name) : "# Account Is Missing  #"),
                                                              IsParent = t.IsParent,
                                                              Parent = ((a != null) ? a.ParentID : "0"),
                                                              Level = (a?.Level ?? 0),
                                                              Credit = t.Credit,
                                                              Debit = t.Debit,
                                                              BeforeCredit = t.BeforeCredit,
                                                              BeforeDebit = t.BeforeDebit,
                                                              TotalCredit = t.Credit + t.BeforeCredit,
                                                              TotalDebit = t.Debit + t.BeforeDebit,
                                                              BalanceCredit = ((t.Credit + t.BeforeCredit > t.Debit + t.BeforeDebit) ? Math.Abs(t.Credit + t.BeforeCredit - (t.Debit + t.BeforeDebit)) : 0.0),
                                                              BalanceDebit = ((t.Debit + t.BeforeDebit > t.Credit + t.BeforeCredit) ? Math.Abs(t.Debit + t.BeforeDebit - (t.Credit + t.BeforeCredit)) : 0.0)
                                                          };
            List<TrialBalanceReportModel> data = (from x in query5
                                                  where x.BeforeCredit > 0.0 || x.BeforeDebit > 0.0 || x.Credit > 0.0 || x.Debit > 0.0
                                                  orderby x.ID
                                                  select x).ToList();
            gridControl1.DataSource = data;
            IEnumerable<TrialBalanceReportModel> ChildsOnly = data.Where((TrialBalanceReportModel x) => !x.IsParent);
            double sumCredit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.Credit);
            double sumDebit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.Debit);
            double sumBeforeCredit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.BeforeCredit);
            double sumBeforeDebit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.BeforeDebit);
            double sumTotalCredit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.TotalCredit);
            double sumTotalDebit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.TotalDebit);
            double sumBalanceCredit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.BalanceCredit);
            double sumBalanceDebit = ChildsOnly.Sum((TrialBalanceReportModel x) => x.BalanceDebit);
            IsBeforeBalanced = Math.Abs(sumBeforeCredit - sumBeforeDebit) < 0.1;
            IsTranceBalaced = Math.Abs(sumCredit - sumDebit) < 0.1;
            IsTotalBalaced = Math.Abs(sumTotalDebit - sumTotalCredit) < 0.1;
            IsBalanceBalanced = Math.Abs(sumBalanceCredit - sumBalanceDebit) < 0.1;
            bandedGridView.Columns["Credit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumCredit.ToString());
            bandedGridView.Columns["Debit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumDebit.ToString());
            bandedGridView.Columns["BeforeCredit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumBeforeCredit.ToString());
            bandedGridView.Columns["BeforeDebit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumBeforeDebit.ToString());
            bandedGridView.Columns["TotalCredit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumTotalCredit.ToString());
            bandedGridView.Columns["TotalDebit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumTotalDebit.ToString());
            bandedGridView.Columns["BalanceCredit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumBalanceCredit.ToString());
            bandedGridView.Columns["BalanceDebit"].SummaryItem.SetSummary(SummaryItemType.Custom, sumBalanceDebit.ToString());
            if (!IsGridInitialized)
            {
                IsGridInitialized = true;
                InitializeGrid();
            }
        }
        private void InitializeGrid()
        {
            this.bandedGridView.OptionsView.ShowFooter = true;
            int i = 3;
            foreach (object obj in this.bandedGridView.Columns)
            {
                BandedGridColumn clm = (BandedGridColumn)obj;
                bool flag = clm.FieldName.Contains("Credit") || clm.FieldName.Contains("Debit");
                if (flag)
                {
                    bool flag2 = clm.FieldName.Contains("Credit");
                    if (flag2)
                    {
                        clm.Caption = "Crédit";
                    }
                    else
                    {
                        bool flag3 = clm.FieldName.Contains("Debit");
                        if (flag3)
                        {
                            clm.Caption = "Débit";
                        }
                    }
                    clm.VisibleIndex = i++;
                    clm.DisplayFormat.FormatString = "N2";
                    clm.DisplayFormat.FormatType = FormatType.Numeric;
                }

            }
            this.bandedGridView.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView.Appearance.BandPanel.TextOptions.HAlignment = HorzAlignment.Center;
            this.bandedGridView.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView.Appearance.HeaderPanel.TextOptions.HAlignment = HorzAlignment.Center;
            this.bandedGridView.OptionsBehavior.Editable = false;
            this.bandedGridView.OptionsBehavior.FocusLeaveOnTab = true;
            this.bandedGridView.OptionsNavigation.EnterMoveNextColumn = true;
            this.bandedGridView.OptionsView.EnableAppearanceEvenRow = true;
            this.bandedGridView.OptionsView.RowAutoHeight = true;
            this.bandedGridView.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView.OptionsView.ShowFooter = true;
            this.bandedGridView.OptionsView.ShowGroupPanel = false;
            this.bandedGridView.Columns["ID"].Visible = false;
            this.bandedGridView.Columns["Name"].VisibleIndex = 1;
            this.bandedGridView.Columns["Name"].Caption = "Compte";
            this.bandedGridView.Columns["Level"].VisibleIndex = -1;
            BandedGridColumn indexColumn = new BandedGridColumn
            {
                Name = "clmIndx",
                FieldName = "index",
                Caption = "N°",
                Width = 35,
                UnboundType = UnboundColumnType.Integer,
                VisibleIndex = 0
            };
            this.bandedGridView.Columns.Add(indexColumn);
            GridBand FirstBand = new GridBand();
            FirstBand.Columns.Add(indexColumn);
            FirstBand.Columns.Add(this.bandedGridView.Columns["ID"]);
            FirstBand.Columns.Add(this.bandedGridView.Columns["Name"]);
            FirstBand.Columns.Add(this.bandedGridView.Columns["Level"]);
            this.bandedGridView.Columns["Name"].AppearanceCell.TextOptions.HAlignment = HorzAlignment.Near;
            GridBand BeforeBand = new GridBand
            {
                Name = "BeforeBand",
                Caption = "Précédent",
                VisibleIndex = 1
            };
            GridBand TransBand = new GridBand
            {
                Name = "TransBand",
                Caption = "Transaction",
                VisibleIndex = 2
            };
            GridBand TotalBand = new GridBand
            {
                Name = "TotalBand",
                Caption = "Total",
                VisibleIndex = 3
            };
            GridBand BalanceBand = new GridBand
            {
                Name = "BalanceBand",
                Caption = "Solde",
                VisibleIndex = 4
            };
            this.bandedGridView.Bands.Clear();
            this.bandedGridView.Bands.AddRange(new GridBand[]
            {
    FirstBand,
    BeforeBand,
    TransBand,
    TotalBand,
    BalanceBand
            });

            BeforeBand.Columns.Add(this.bandedGridView.Columns["BeforeCredit"]);
            BeforeBand.Columns.Add(this.bandedGridView.Columns["BeforeDebit"]);
            TransBand.Columns.Add(this.bandedGridView.Columns["Credit"]);
            TransBand.Columns.Add(this.bandedGridView.Columns["Debit"]);
            TotalBand.Columns.Add(this.bandedGridView.Columns["TotalCredit"]);
            TotalBand.Columns.Add(this.bandedGridView.Columns["TotalDebit"]);
            BalanceBand.Columns.Add(this.bandedGridView.Columns["BalanceCredit"]);
            BalanceBand.Columns.Add(this.bandedGridView.Columns["BalanceDebit"]);
            this.bandedGridView.CustomColumnDisplayText += this.bandedGridView_CustomColumnDisplayText;
            this.bandedGridView.CustomUnboundColumnData += this.bandedGridView_CustomUnboundColumnData;
            this.bandedGridView.RowStyle += this.bandedGridView_RowStyle;
            this.bandedGridView.CustomDrawFooterCell += this.bandedGridView_CustomDrawFooterCell;
        }

        private void bandedGridView_CustomDrawFooterCell(object sender, FooterCellCustomDrawEventArgs e)
        {
            bool flag = (e.Column.FieldName == "Credit" || e.Column.FieldName == "Debit") && !this.IsTranceBalaced;
            if (flag)
            {
                e.Appearance.BackColor = Color.Red;
            }
            else
            {
                e.Appearance.BackColor = Color.Green;
            }
            bool flag2 = e.Column.FieldName.StartsWith("Balance") && !this.IsBalanceBalanced;
            if (flag2)
            {
                e.Appearance.BackColor = Color.Red;
            }
            else
            {
                e.Appearance.BackColor = Color.Green;
            }
            bool flag3 = e.Column.FieldName.StartsWith("Before") && !this.IsBeforeBalanced;
            if (flag3)
            {
                e.Appearance.BackColor = Color.Red;
            }
            else
            {
                e.Appearance.BackColor = Color.Green;
            }
            bool flag4 = e.Column.FieldName.StartsWith("Total") && !this.IsTotalBalaced;
            if (flag4)
            {
                e.Appearance.BackColor = Color.Red;
            }
            else
            {
                e.Appearance.BackColor = Color.Green;
            }
            e.Appearance.Options.UseBackColor = true;
        }

        private void bandedGridView_RowStyle(object sender, RowStyleEventArgs e)
        {
            object row = this.bandedGridView.GetRowCellValue(e.RowHandle, "IsParent");
            bool flag = row != null && row.GetType() == typeof(bool);
            if (flag)
            {
                bool flag2 = row.Equals(true);
                if (flag2)
                {
                    e.Appearance.BackColor = Color.LightGray;
                    e.Appearance.Font = new Font(e.Appearance.Font, FontStyle.Bold);
                    e.Appearance.TextOptions.HAlignment = HorzAlignment.Near;
                    e.HighPriority = true;
                }
            }
        }

        private void bandedGridView_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            bool flag = e.ListSourceRowIndex < 0;
            if (!flag)
            {
                e.Value = e.ListSourceRowIndex + 1;
            }
        }

        private void bandedGridView_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            bool flag = !(e.DisplayText.Trim() == "0");
            if (!flag)
            {
                e.DisplayText = string.Empty;
            }
        }

        private LayoutControl layoutForTotal;

        private TextEdit CountTextEdit = new TextEdit();

        private TextEdit BalanceTextEdit = new TextEdit();

        private TextEdit TotalSellTextEdit = new TextEdit();

        private TextEdit TotalBuyTextEdit = new TextEdit();

        private BandedGridView bandedGridView;
        private bool IsGridInitialized;

        private bool IsBeforeBalanced;

        private bool IsTranceBalaced;

        private bool IsTotalBalaced;

        private bool IsBalanceBalanced;
    }
}
