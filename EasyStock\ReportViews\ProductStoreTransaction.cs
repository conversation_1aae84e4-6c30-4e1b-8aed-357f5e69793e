﻿using DevExpress.Data;
using DevExpress.LookAndFeel;
using DevExpress.Utils;
using DevExpress.Utils.Extensions;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controls;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;

namespace EasyStock.ReportViews
{
    public partial class ProductStoreTransaction : ReportForm
    {
        public ProductStoreTransaction() : base(new ReportFilter[]
        {
            ReportFilter.Product,
            ReportFilter.Store,
            ReportFilter.Date
        }, typeof(ProductStoreTransactionReportModel))
        {
            this.InitializeComponent();
            this.gridView1.RowCellStyle += this.GridView1_RowCellStyle;
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
            this.gridView1.DataSourceChanged += this.GridView1_DataSourceChanged;
            this.AddTextBoxes();
        }

        private void AddTextBoxes()
        {
            this.OpenBalanceTextEdit.Properties.ReadOnly = true;
            this.TotalInTextEdit.Properties.ReadOnly = true;
            this.TotalOutTextEdit.Properties.ReadOnly = true;
            this.MovmentBalanceTextEdit.Properties.ReadOnly = true;
            this.FianlBalanceTextEdit.Properties.ReadOnly = true;
            this.CurrentBalanceTextEdit.Properties.ReadOnly = true;
            this.layoutForTotal = new LayoutControl();
            this.layoutForTotal.OptionsPrint.OldPrinting = true;
            this.layoutForTotal.MaximumSize = new Size(300, 0);
            this.layoutForTotal.Padding = new System.Windows.Forms.Padding(0);
            LayoutControlGroup totalGroup = this.Root.AddGroup("Totaux");
            totalGroup.Padding = new DevExpress.XtraLayout.Utils.Padding(0);
            totalGroup.AddItem(" ", this.layoutForTotal);
            this.Root.Add(totalGroup);
            this.Root.AddItem(new EmptySpaceItem(), totalGroup, InsertType.Left);
            this.layoutForTotal.AddItem("Solde d'ouverture", this.OpenBalanceTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Total Entrées", this.TotalInTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Total Sorties", this.TotalOutTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Écart de mouvement", this.MovmentBalanceTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Solde de clôture", this.FianlBalanceTextEdit).TextLocation = Locations.Right;
            this.layoutForTotal.AddItem("Solde actuel", this.CurrentBalanceTextEdit).TextLocation = Locations.Right;

            this.SetTextAlignment();
        }

        private void GridView1_DataSourceChanged(object sender, EventArgs e)
        {
            this.gridView1.ClearSorting();
            GridColumn colDate = this.gridView1.Columns["Date"];
            bool flag = colDate != null;
            if (flag)
            {
                colDate.SortOrder = ColumnSortOrder.Ascending;
            }
            this.gridView1.BestFitColumns(true);
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            if ((info.InRow || info.InRowCell) && view.GetFocusedRowCellValue("BillID") is int id && view.GetFocusedRowCellValue("ProcessType") is TransactionType process)
            {
                Utilities.OpenProcess((SystemProcess)process, id);
            }
        }
        private void GridView1_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            GridView view = sender as GridView;
            if (e.Column.FieldName == "Balance")
            {
                if (e.CellValue is double value)
                {
                    Color color = Color.FromArgb(200, DXSkinColors.FillColors.Warning);
                    if (value > 0.0)
                    {
                        color = Color.FromArgb(200, DXSkinColors.FillColors.Success);
                    }
                    e.Appearance.BackColor = color;
                }
            }
            else if (e.Column.FieldName == "Quantity" && view.GetRowCellValue(e.RowHandle, "TransactionType") is ProductTransactionType value)
            {
                Color color = Color.FromArgb(200, DXSkinColors.FillColors.Warning);
                if (value == ProductTransactionType.In)
                {
                    color = Color.FromArgb(200, DXSkinColors.FillColors.Success);
                }
                e.Appearance.BackColor = color;
            }
            if (view.GetRowCellValue(e.RowHandle, "TransactionType") is ProductTransactionType transactionType && transactionType == ProductTransactionType.In)
            {
                Font f = new Font(e.Appearance.Font, FontStyle.Bold);
                e.Appearance.Font = f;
            }
        }
        private void SetTextAlignment()
        {
            this.layoutForTotal.Items.ForEach(delegate (BaseLayoutItem x)
            {
                x.AppearanceItemCaption.TextOptions.HAlignment = HorzAlignment.Far;
            });
        }

        private int[] SelectedProducts
        {
            get
            {
                GridPopupContainerControl products = base.FiltersForm.Products;
                int[] result;
                if (products == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = products.EditValue;
                    result = ((editValue != null) ? editValue.ToArray<int>() : null);
                }
                return result;
            }
        }

        private int[] SelectedStores
        {
            get
            {
                GridPopupContainerControl stores = base.FiltersForm.Stores;
                int[] result;
                if (stores == null)
                {
                    result = null;
                }
                else
                {
                    IList<int> editValue = stores.EditValue;
                    result = ((editValue != null) ? editValue.ToArray<int>() : null);
                }
                return result;
            }
        }

        private IQueryable<ProductTransaction> ProductTransactions
        {
            get
            {
                IQueryable<ProductTransaction> q1 = from x in db.ProductTransactions
                                                    where (int)x.TransactionState == 2
                                                    select x into jd
                                                    join product in db.Products on jd.ProductID equals product.ID
                                                    where (int)product.Type != 1
                                                    select jd;
                if (base.FiltersForm.hasProducts)
                {
                    q1 = q1.Where(x => SelectedProducts.Contains(x.ProductID));
                }
                if (base.FiltersForm.hasStores)
                {
                    q1 = q1.Where(x => SelectedStores.Contains(x.StoreID));
                }
                return q1;
            }
        }

        private IQueryable<ProductTransaction> ProductTransactionsInPeriod
        {
            get
            {
                return from t in this.ProductTransactions
                       where DbFunctions.TruncateTime((DateTime?)t.Date) >= (DateTime?)this.startDate && DbFunctions.TruncateTime((DateTime?)t.Date) <= (DateTime?)this.endtDate
                       select t;
            }
        }

        private DateTime endtDate
        {
            get
            {
                bool hasEndtDate = base.FiltersForm.hasEndtDate;
                DateTime date;
                if (hasEndtDate)
                {
                    date = base.FiltersForm.ToDate.DateTime.Date;
                }
                else
                {
                    date = (this.ProductTransactions.Max((ProductTransaction x) => (DateTime?)x.Date) ?? DateTime.Now).Date;
                }
                return date;
            }
        }

        private DateTime startDate
        {
            get
            {
                bool hasStartDate = base.FiltersForm.hasStartDate;
                DateTime date;
                if (hasStartDate)
                {
                    date = base.FiltersForm.FromDate.DateTime.Date;
                }
                else
                {
                    date = (this.ProductTransactions.Min((ProductTransaction x) => (DateTime?)x.Date) ?? DateTime.Now).Date;
                }
                return date;
            }
        }

        internal override bool ValidateFilters()
        {
            return true;
        }

        public override IQueryable GetQuery()
        {
            var query = from t in ProductTransactionsInPeriod
                        join br in db.Stores on t.StoreID equals br.ID
                        join pr in db.Products on t.ProductID equals pr.ID
                        join u in db.ProductUnits.Include(x => x.UnitName) on t.UnitID equals u.ID
                        from size in db.ProductSizes.Where(x => x.ID == t.SizeID).DefaultIfEmpty()
                        from color in db.ProductColors.Where(x => x.ID == t.ColorID).DefaultIfEmpty()
                        select new ProductStoreTransactionReportModel
                        {
                            ID = t.ID,
                            StoreID = br.ID,
                            BillID = t.BillID,
                            StoreName = br.Name,
                            Expire = t.Expire,
                            ParentTransactionID = t.ParentTransactionID,
                            Price = t.Price,
                            ProductID = t.ProductID,
                            UnitName = u.UnitName.Name,
                            ProductName = pr.Name,
                            Quantity = t.Quantity,
                            TransactionType = t.TransactionType,
                            Color = color != null ? color.Name : "",
                            Size = size != null ? size.Name : "",
                            Serial = t.Serial,
                            ProcessType = t.Type,
                            Date = t.Date,
                            PartID = GetPartID(t),
                            PartName = GetPartName(t, br),
                            Balance = GetBalance(t)
                        };

            return query;
        }

        private int GetPartID(ProductTransaction t)
        {
            var partIDQuery = t.Type switch
            {
                (TransactionType)4 => (from sl in db.SalesInvoices
                      join cu in db.Customers on sl.CustomerID equals cu.ID
                      where sl.ID == t.BillID
                      select cu.ID).FirstOrDefault(),
                (TransactionType)5 => (from sl in db.SalesReturnInvoices
                      join cu in db.Customers on sl.CustomerID equals cu.ID
                      where sl.ID == t.BillID
                      select cu.ID).FirstOrDefault(),
                (TransactionType)2 => (from pr in db.PurchaseInvoices
                      join v in db.Vendors on pr.VendorID equals v.ID
                      where pr.ID == t.BillID
                      select v.ID).FirstOrDefault(),
                (TransactionType)3 => (from pu in db.PurchaseReturnInvoices
                      join v in db.Vendors on pu.VendorID equals v.ID
                      where pu.ID == t.BillID
                      select v.ID).FirstOrDefault(),
                (TransactionType)11 when t.TransactionType == 0 => (from bl in db.StockTransferBills
                                                   join bib in db.Stores on bl.StoreID equals bib.ID
                                                   where bl.ID == t.BillID
                                                   select bib.ID).FirstOrDefault(),
                (TransactionType)11 when t.TransactionType == (ProductTransactionType)1 => (from bl in db.StockTransferBills
                                                   join bib in db.Stores on bl.ToStoreID equals bib.ID
                                                   where bl.ID == t.BillID
                                                   select bib.ID).FirstOrDefault(),
                (TransactionType)1 or (TransactionType)10 or (TransactionType)16 => t.StoreID,
                _ => 0
            };

            return partIDQuery;
        }

        private string GetPartName(ProductTransaction t, Store br)
        {
            var partNameQuery = t.Type switch
            {
                (TransactionType)4 => (from sl in db.SalesInvoices
                      join cu in db.Customers on sl.CustomerID equals cu.ID
                      where sl.ID == t.BillID
                      select cu.Name).FirstOrDefault(),
                (TransactionType)5 => (from sl in db.SalesReturnInvoices
                      join cu in db.Customers on sl.CustomerID equals cu.ID
                      where sl.ID == t.BillID
                      select cu.Name).FirstOrDefault(),
                (TransactionType)2 => (from pr in db.PurchaseInvoices
                      join v in db.Vendors on pr.VendorID equals v.ID
                      where pr.ID == t.BillID
                      select v.Name).FirstOrDefault(),
                (TransactionType)3 => (from pu in db.PurchaseReturnInvoices
                      join v in db.Vendors on pu.VendorID equals v.ID
                      where pu.ID == t.BillID
                      select v.Name).FirstOrDefault(),
                (TransactionType)11 when (int)t.TransactionType == 0 => (from bl in db.StockTransferBills
                                                   join bib in db.Stores on bl.StoreID equals bib.ID
                                                   where bl.ID == t.BillID
                                                   select bib.Name).FirstOrDefault(),
                (TransactionType)11 when (int)t.TransactionType == 1 => (from bl in db.StockTransferBills
                                                   join bib in db.Stores on bl.ToStoreID equals bib.ID
                                                   where bl.ID == t.BillID
                                                   select bib.Name).FirstOrDefault(),
                (TransactionType)1 or (TransactionType)10 or (TransactionType)16 => br.Name,
                _ => "Not Implemented"
            };

            return partNameQuery;
        }

        private double GetBalance(ProductTransaction t)
        {
            double totalIn = (from x in db.ProductTransactions
                              where x.TransactionState == (TransactionState)2
                                    && x.ProductID == t.ProductID
                                    && x.StoreID == t.StoreID
                                    && x.Date <= t.Date
                                    && x.TransactionType == 0
                              select (double?)(x.Quantity * x.Factor) ?? 0.0).Sum();

            double totalOut = (from x in db.ProductTransactions
                               where x.TransactionState == (TransactionState)2
                                     && x.ProductID == t.ProductID
                                     && x.StoreID == t.StoreID
                                     && x.Date <= t.Date
                                     && x.TransactionType == (ProductTransactionType)1
                               select (double?)(x.Quantity * x.Factor) ?? 0.0).Sum();

            return totalIn - totalOut;
        }

        public override void Print()
        {
            this.layoutForTotal.Flip();
            GridReportP.Print(this.layoutControl1, this.Text, this.labelControl1.Text, true);
            this.layoutForTotal.Flip();
        }

        public override void RefreshDataSource()
        {
            base.RefreshDataSource();
            this.OpenBalanceTextEdit.Text = ((from x in this.ProductTransactions
                                              where x.Date <= this.startDate.Date
                                              where (int)x.TransactionType == 0
                                              select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault() - (from x in this.ProductTransactions
                                                                                                                                               where x.Date <= this.startDate.Date
                                                                                                                                               where (int)x.TransactionType == 1
                                                                                                                                               select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault()).ToString();
            double totalIn = (from x in this.ProductTransactionsInPeriod
                              where (int)x.TransactionType == 0
                              select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault();
            double totalOut = (from x in this.ProductTransactionsInPeriod
                               where (int)x.TransactionType == 1
                               select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault();
            this.TotalInTextEdit.Text = totalIn.ToString();
            this.TotalOutTextEdit.Text = totalOut.ToString();
            double balance = totalIn - totalOut;
            this.MovmentBalanceTextEdit.Text = string.Format("{0:N}  ", balance);
            this.FianlBalanceTextEdit.Text = ((from x in this.ProductTransactions
                                               where (DateTime?)x.Date < DbFunctions.AddDays((DateTime?)this.endtDate, (int?)1)
                                               where (int)x.TransactionType == 0
                                               select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault() - (from x in this.ProductTransactions
                                                                                                                                                where (DateTime?)x.Date < DbFunctions.AddDays((DateTime?)this.endtDate, (int?)1)
                                                                                                                                                where (int)x.TransactionType == 1
                                                                                                                                                select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault()).ToString();
            this.CurrentBalanceTextEdit.Text = ((from x in this.ProductTransactions
                                                 where (int)x.TransactionType == 0
                                                 select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault() - (from x in this.ProductTransactions
                                                                                                                                                  where (int)x.TransactionType == 1
                                                                                                                                                  select x).Sum((ProductTransaction x) => (double?)(x.Quantity * x.Factor)).GetValueOrDefault()).ToString();
        }

        private LayoutControl layoutForTotal;

        private TextEdit CurrentBalanceTextEdit = new TextEdit();

        private TextEdit FianlBalanceTextEdit = new TextEdit();

        private TextEdit MovmentBalanceTextEdit = new TextEdit();

        private TextEdit OpenBalanceTextEdit = new TextEdit();

        private TextEdit TotalOutTextEdit = new TextEdit();

        private TextEdit TotalInTextEdit = new TextEdit();
    }
}
