﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class SalesCustomerProductsModel : CompanyInfoReportModel
    {
        [Display(Name = "Dépôt")]
        public string InvoiceStoreName { get; set; }

        [Display(Name = "Client")]
        public string Customer { get; set; }

        [Display(Name = "Article")]
        public string Product { get; set; }

        [Display(Name = "Numéro de facture")]
        public string InvoiceNum { get; set; }

        [Display(Name = "Date")]
        public DateTime InvoiceDate { get; set; }

        [Display(Name = "Unité")]
        public string Unit { get; set; }

        [Display(Name = "Quantité")]
        public double Qty { get; set; }

        [Display(Name = "Prix de vente")]
        public double Price { get; set; }

        [Display(Name = "Montant total")]
        public double TotalAmount { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Total net")]
        public double NetTotal { get; set; }
    }
}
