﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class StockBalanceCorrectionDetailReportModel : ProductTransactionReportModel
    {
        [Display(Name = "Quantité")]
        [Range(0.001, double.MaxValue, ErrorMessage = "La quantité doit être supérieure à 0.001")]
        public new double Quantity { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Pourcentage de remise")]
        public double DiscountPercentage { get; set; }

        [Display(Name = "Net")]
        public double Net
        {
            get
            {
                return base.Total + this.Tax - this.Discount;
            }
        }

        [Display(Name = "Taxe")]
        public double Tax { get; set; }

        [Display(Name = "Pourcentage de Taxe")]
        public double TaxPercentage { get; set; }
    }
}
