﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class SalesProductCostModel : CompanyInfoReportModel
    {
        [Display(Name = "Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Article")]
        public string Product { get; set; }

        [Display(Name = "Quantité")]
        public double Qty { get; set; }

        [Display(Name = "Coût total")]
        public double TotalCost { get; set; }

        [Display(Name = "Coût unitaire")]
        public double UnitCost
        {
            get
            {
                return this.TotalCost / this.Qty;
            }
        }

        [Display(Name = "Total des ventes")]
        public double TotalSales { get; set; }

        [Display(Name = "Prix de vente unitaire")]
        public double UnitPrice
        {
            get
            {
                return this.TotalSales / this.Qty;
            }
        }

        [Display(Name = "Marge bénéficiaire")]
        public double ProfitMargin
        {
            get
            {
                return this.TotalSales - this.TotalCost;
            }
        }
    }
}
