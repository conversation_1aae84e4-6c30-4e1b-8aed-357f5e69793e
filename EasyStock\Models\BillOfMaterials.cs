﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("BillOfMaterials")]
    [DisplayColumn("Liste des matières premières")]
    public class BillOfMaterials : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Nom")]
        [StringLength(150)]
        [Required(ErrorMessage = "Ce champ est requis")]
        public string Name
        {
            get
            {
                return this.name;
            }
            set
            {
                base.SetProperty<string>(ref this.name, value, "Name");
            }
        }

        [Display(Name = "Article")]
        [Required(ErrorMessage ="Ce champ est requis")]
        public int ProductID
        {
            get
            {
                return this.productID;
            }
            set
            {
                base.SetProperty<int>(ref this.productID, value, "ProductID");
            }
        }

        [Display(Name = "Unité")]
        [Required(ErrorMessage ="Ce champ est requis")]
        public int UintID
        {
            get
            {
                return this.uintID;
            }
            set
            {
                base.SetProperty<int>(ref this.uintID, value, "UintID");
            }
        }

        [Display(Name = "Qté")]
        [Required(ErrorMessage ="Ce champ est requis")]
        public double Quantity
        {
            get
            {
                return this.quantity;
            }
            set
            {
                base.SetProperty<double>(ref this.quantity, value, "Quantity");
            }
        }

        [Display(Name = "Notes")]
        [StringLength(250)]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        public BindingList<BOMDetails> BOMDetails { get; set; }

        public BindingList<BOMExpenses> BOMExpenses { get; set; }

        private int id;

        private string name;

        private int productID;

        private int uintID;

        private double quantity;

        private string notes;
    }
}
