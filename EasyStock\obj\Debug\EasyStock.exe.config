﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value>Skin/Metropolis</value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>System</value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value />
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
  </startup>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <connectionStrings>
    <add name="DataContext" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Controller.DataContext;integrated security=True;MultipleActiveResultSets=True;pooling=true;connection lifetime=120;max pool size=2500;;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="PurchaseTaxesReportModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.PurchaseTaxesReportModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SalesCustomerProductsModle" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.SalesCustomerProductsModle;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SalesCustomerProductsModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.SalesCustomerProductsModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="TotalSalesCustomerProductsModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.TotalSalesCustomerProductsModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SalesProductCostModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.SalesProductCostModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SalesProductCategoriesCostModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.SalesProductCategoriesCostModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="TotalSalesProductCategoriesModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.TotalSalesProductCategoriesModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SalesProductCategoriesModel.cs" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.SalesProductCategoriesModel.cs;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SalesProductCategoriesMainModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.SalesProductCategoriesMainModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="DueInvoices" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Charts.Models.DueInvoices;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="DueInvoices1" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Charts.DueInvoices;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="DefaultExpenses" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.DefaultExpenses;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="Model1" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.Model1;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="BillOfMaterials" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.BillOfMaterials;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="BOMDetails" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.BOMDetails;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="BOMExpenses" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.BOMExpenses;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="AcualMaterialConsumptions" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.AcualMaterialConsumptions;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="WorkOrder" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.WorkOrder;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="WorkOrderDetails" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.WorkOrderDetails;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="BillOfMaterialsReportModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.BillOfMaterialsReportModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="WorkOrderReportModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.WorkOrderReportModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="TaxDeclarationReportModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.TaxDeclarationReportModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="PettyCashHolder" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.PettyCashHolder;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="PettyCash" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.PettyCash;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="PettyCashCloseOut" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.PettyCashCloseOut;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="PettyCashReportModel.cs" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.PettyCashReportModel.cs;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="PettyCashCloseOutReportModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.PettyCashCloseOutReportModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="ContractorAbstractModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.ContractorAbstractModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="WorkType" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.WorkType;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="ContractorAbstractReportModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.ContractorAbstractReportModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="VendorInvoiceModel" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.Models.VendorInvoiceModel;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="VendorInvoiceReportModel.cs" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=EasyStock.ReportModels.VendorInvoiceReportModel.cs;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>