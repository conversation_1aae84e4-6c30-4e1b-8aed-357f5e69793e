﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;

namespace EasyStock.ReportViews
{
	// Token: 0x02000334 RID: 820
	public partial class SalesCustomerProductView : XtraReportForm
	{
		// Token: 0x060013F1 RID: 5105 RVA: 0x0000A637 File Offset: 0x00008837
		public SalesCustomerProductView() : base(new ReportFilter[]
		{
			ReportFilter.Date,
			ReportFilter.Store,
			ReportFilter.Customer,
			ReportFilter.Product
		})
		{
			this.InitializeComponent();
		}

		// Token: 0x060013F2 RID: 5106 RVA: 0x0014E4F4 File Offset: 0x0014C6F4
		internal override bool ValidateFilters()
		{
			bool flag = !base.FiltersForm.hasStartDate;
			bool result;
			if (flag)
			{
				base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
				result = false;
			}
			else
			{
				bool flag2 = !base.FiltersForm.hasEndtDate;
				if (flag2)
				{
					base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
					result = false;
				}
				else
				{
					result = true;
				}
			}
			return result;
		}

		// Token: 0x060013F3 RID: 5107 RVA: 0x0016168C File Offset: 0x0015F88C
		public override void RefreshDataSource()
		{
			bool flag = !this.ValidateFilters();
			if (!flag)
			{
				SalesCustomerProductsReport rpt = new SalesCustomerProductsReport();
				ICollection<SalesCustomerProductsModel> dataSource = this.GetData();
				rpt.DataSource = dataSource;
				rpt.SetCompanyInfo(dataSource.FirstOrDefault<SalesCustomerProductsModel>());
				rpt.Cell_ReportName.Text = this.Text;
				rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
				this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

		private ICollection<SalesCustomerProductsModel> GetData()
		{
			int[] CustomerIDs = base.FiltersForm.Customers.EditValue?.ToArray();
			int[] ProductIDs = base.FiltersForm.Products.EditValue?.ToArray();
			int[] StoreIDs = base.FiltersForm.Stores.EditValue?.ToArray();
			TransactionType TransType = TransactionType.Sales;
			using ERPDataContext context = new ERPDataContext();
			IQueryable<SalesInvoice> filterdInvoices = context.SalesInvoices.Where((SalesInvoice inv) => DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate);
			IQueryable<InvoiceDetail> filterdInvoiceDetails = context.InvoicesDetails.Where((InvoiceDetail invD) => (int)invD.Type == (int)TransType);
			if (ProductIDs != null && ProductIDs.Count() > 0)
			{
				filterdInvoiceDetails = filterdInvoiceDetails.Where((InvoiceDetail Prod) => ProductIDs.Contains(Prod.ProductID));
			}
			if (CustomerIDs != null && CustomerIDs.Count() > 0)
			{
				filterdInvoices = filterdInvoices.Where((SalesInvoice c) => CustomerIDs.Contains(c.CustomerID));
			}
			if (StoreIDs != null && StoreIDs.Count() > 0)
			{
				filterdInvoices = filterdInvoices.Where((SalesInvoice c) => StoreIDs.Contains(c.StoreID));
			}
			IQueryable<SalesCustomerProductsModel> data = from InvD in filterdInvoiceDetails
														  join inv in filterdInvoices on InvD.BillID equals inv.ID
														  join cus in context.Customers on inv.CustomerID equals cus.ID
														  join Prod in context.Products on InvD.ProductID equals Prod.ID
														  join Bran in context.Stores on inv.StoreID equals Bran.ID
														  join unitID in context.ProductUnits on InvD.UnitID equals unitID.ID
														  join unitName in context.UnitOfMeasurements on unitID.UnitNameID equals unitName.ID
														  select new SalesCustomerProductsModel
														  {
															  InvoiceStoreName = ((Bran != null) ? Bran.Name : ""),
															  Customer = ((cus != null) ? cus.Name : ""),
															  Product = Prod.Name,
															  InvoiceNum = inv.Code,
															  InvoiceDate = inv.Date,
															  Unit = unitName.Name,
															  TotalAmount = InvD.Price * InvD.Quantity,
															  Discount = InvD.Discount,
															  NetTotal = InvD.Price * InvD.Quantity - InvD.Discount,
															  Price = InvD.Price,
															  Qty = InvD.Quantity
														  };
			return data.ToList();
		}
	}
}
