﻿using DevExpress.XtraEditors;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.ReportViews
{
    public partial class MerchandisingAccountStatment : ReportForm
    {
        public MerchandisingAccountStatment() : base(new ReportFilter[]
        {
            ReportFilter.Date
        }, typeof(MerchandisingAccountStatmentModel))
        {
            this.InitializeComponent();
            this.AddTextBoxes();
        }

        private void AddTextBoxes()
        {
            this.FianlBalanceTextEdit.Properties.ReadOnly = true;
            LayoutControlItem item = this.Root.AddItem(" ", this.FianlBalanceTextEdit, this.lycGridControl, InsertType.Bottom);
            item.TextVisible = false;
            item.MaxSize = new Size(500, 35);
            item.MinSize = new Size(500, 35);
        }

        private DateTime endtDate
        {
            get
            {
                bool hasEndtDate = base.FiltersForm.hasEndtDate;
                DateTime date;
                if (hasEndtDate)
                {
                    date = base.FiltersForm.ToDate.DateTime.Date;
                }
                else
                {
                    date = (this.AccountJournals.Max(x=> (DateTime?)x.Journal.Date) ?? DateTime.Now).Date;
                }
                return date;
            }
        }

        private DateTime startDate
        {
            get
            {
                bool hasStartDate = base.FiltersForm.hasStartDate;
                DateTime date;
                if (hasStartDate)
                {
                    date = base.FiltersForm.FromDate.DateTime.Date;
                }
                else
                {
                    date = (this.AccountJournals.Min(x => (DateTime?)x.Journal.Date) ?? DateTime.Now).Date;
                }
                return date;
            }
        }

        private IQueryable<JournalDetail> AccountJournals
        {
            get
            {
                return from x in this.db.JournalDetails.Include(x => x.Account).Include(x => x.Journal).Include(x => x.Currency)
                       orderby x.Journal.Date
                       select x into jd
                       where jd.Account.Number.IndexOf(this.AccountNumber) == 0
                       select jd;
            }
        }

        private IQueryable<JournalDetail> AccountJournalsInPeriod
        {
            get
            {
                return from jd in this.AccountJournals
                       where DbFunctions.TruncateTime((DateTime?)jd.Journal.Date) >= (DateTime?)this.startDate && DbFunctions.TruncateTime((DateTime?)jd.Journal.Date) <= (DateTime?)this.endtDate
                       select jd;
            }
        }

        private Account Account
        {
            get
            {
                return this.db.Accounts.SingleOrDefault((Account x) => x.ID == this.AccountID);
            }
        }

        private int AccountID { get; set; }

        private string AccountNumber
        {
            get
            {
                Account account = this.Account;
                return (account != null) ? account.Number : null;
            }
        }

        public override void Print()
        {
            GridReportP.Print(this.layoutControl1, this.Text, this.labelControl1.Text, false);
        }

        public override void RefreshDataSource()
        {
            AccountID = ERPDataContext.SystemSettings.MerchandisingAccount;
            if (Account == null)
            {
                MessageBox.Show("Veuillez sélectionner d'abord le compte de trading depuis l'écran des paramètres.", "Avertissement", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                Close();
            }
            var q1 = (from jd in AccountJournalsInPeriod
                      group jd by jd.AccountID into g
                      select new
                      {
                          AccountID = g.Key,
                          TotalCredit = g.Sum(x => (double?)(x.Credit * x.CurrencyRate) ?? 0.0),
                          TotalDebit = g.Sum(x => (double?)(x.Debit * x.CurrencyRate) ?? 0.0)
                      }).ToList();
            List<Account> accounts = db.Accounts.Where((Account acc) => acc.Number.IndexOf(AccountNumber) == 0).ToList();
            var qurey = (from ac in accounts
                         from q in q1.Where(x => x.AccountID == ac.ID).DefaultIfEmpty()
                         select new
                         {
                             Name = ac.Name,
                             Number = ac.Number,
                             Credit = (q != null && q.TotalCredit > q.TotalDebit) ? (q.TotalCredit - q.TotalDebit) : 0.0,
                             Debit = (q != null && q.TotalDebit > q.TotalCredit) ? (q.TotalDebit - q.TotalCredit) : 0.0,
                             Level = ac.Number.Length - 3
                         }).ToList();
            int index = 1;
            List<MerchandisingAccountStatmentModel> q2 = (from x in qurey
                                                          orderby x.Number
                                                          select x into q
                                                          select new MerchandisingAccountStatmentModel
                                                          {
                                                              Index = index++,
                                                              Account = ".".PadLeft(q.Level) + q.Name,
                                                              Credit = q.Credit,
                                                              Debit = q.Debit
                                                          }).ToList();
            gridControl1.DataSource = q2;
            gridView1.PopulateColumns(q2);
            double balance = q2.Sum((MerchandisingAccountStatmentModel x) => x.Credit) - q2.Sum((MerchandisingAccountStatmentModel x) => x.Debit);
            FianlBalanceTextEdit.Text = ((balance >= 0.0) ? "Total des profits" : "Total des pertes") + " : " + $" {Math.Abs(balance):0.##}";
        }
        private TextEdit FianlBalanceTextEdit = new TextEdit();
    }
}
