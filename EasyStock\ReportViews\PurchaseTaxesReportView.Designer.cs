﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000330 RID: 816
	public partial class PurchaseTaxesReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x060013EA RID: 5098 RVA: 0x001614FC File Offset: 0x0015F6FC
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060013EB RID: 5099 RVA: 0x00161534 File Offset: 0x0015F734
		private void InitializeComponent()
		{
			base.SuspendLayout();
			this.documentViewer1.Location = new global::System.Drawing.Point(0, 49);
			this.documentViewer1.Size = new global::System.Drawing.Size(800, 379);
			base.AutoScaleDimensions = new global::System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = global::System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new global::System.Drawing.Size(800, 450);
			base.Name = "PurchaseTaxesReportView";
			this.Text = "Déclaration fiscale des factures d'achats";
			base.Load += new global::System.EventHandler(this.PurchaseTaxesReportView_Load);
			base.ResumeLayout(false);
			base.PerformLayout();
		}

		// Token: 0x04001993 RID: 6547
		private global::System.ComponentModel.IContainer components = null;
	}
}
