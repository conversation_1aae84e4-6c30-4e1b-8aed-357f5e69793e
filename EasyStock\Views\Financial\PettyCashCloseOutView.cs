﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.Views.Financial
{
    public partial class PettyCashCloseOutView : MasterForm
    {
        public static PettyCashCloseOutView Instance
        {
            get
            {
                bool flag = PettyCashCloseOutView.instance == null || PettyCashCloseOutView.instance.IsDisposed;
                if (flag)
                {
                    PettyCashCloseOutView.instance = new PettyCashCloseOutView();
                }
                return PettyCashCloseOutView.instance;
            }
        }

        public PettyCashCloseOut CloseOut
        {
            get
            {
                return this.pettyCashCloseOutBindingSource.Current as PettyCashCloseOut;
            }
            set
            {
                this.pettyCashCloseOutBindingSource.DataSource = value;
            }
        }

        public PettyCashCloseOutView()
        {
            this.InitializeComponent();
            this.PettyCashIDLookUpEdit.Properties.ValueMember = "ID";
            this.PettyCashIDLookUpEdit.Properties.DisplayMember = "Name";
            this.PettyCashIDLookUpEdit.Properties.BestFitMode = BestFitMode.BestFitResizePopup;
            this.PettyCashIDLookUpEdit.Properties.NullText = "";
            this.PettyCashIDLookUpEdit.EditValueChanged += this.PettyCashIDLookUpEdit_EditValueChanged;
            this.db = new ERPDataContext();
            base.DisableValidation(this.dataLayoutControl1);
            this.db = new ERPDataContext();
            base.Shown += this.GroupView_Shown;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
            this.PettyCashIDLookUpEdit.CustomDisplayText += this.PettyCashIDLookUpEdit_CustomDisplayText;
            this.CloseAccountTypeImageComboBoxEdit.EditValueChanged += this.CloseAccountTypeImageComboBoxEdit_EditValueChanged;
        }

        private void PettyCashIDLookUpEdit_EditValueChanged(object sender, EventArgs e)
        {
            object selectedDataRow = this.PettyCashIDLookUpEdit.GetSelectedDataRow();
            PettyCashCloseOutView.PettyCashViewForLookup pettyCashView = selectedDataRow as PettyCashCloseOutView.PettyCashViewForLookup;
            bool flag = pettyCashView != null;
            if (flag)
            {
                this.AmountTextEdit.EditValue = pettyCashView.Amount;
                double Expences = (from x in this.db.RevExpEntries
                                   where (int)x.EntryType == 9 && (int)x.MethodType == 4 && x.MethodID == pettyCashView.ID
                                   select x).Sum((RevExpEntry x) => x.Total - x.Total * x.DiscountPersentage + x.Total * x.TaxPersentage);
                this.AmountSpentTextEdit.EditValue = Expences;
                this.RemainigAmountTextEdit.EditValue = pettyCashView.Amount - Expences;
                this.PettyCashIDLookUpEdit.EditValue = (this.CloseOut.PettyCashID = pettyCashView.ID);
            }
        }

        private void CloseAccountTypeImageComboBoxEdit_EditValueChanged(object sender, EventArgs e)
        {
            if (!(CloseAccountTypeImageComboBoxEdit.EditValue is CloseAccountType type))
            {
                return;
            }
            switch (type)
            {
                case CloseAccountType.Account:
                    // Lier les données des comptes au contrôle LookUpEdit
                    CloseAccountIDLookUpEdit.BindToDataSource(db.Accounts.Select(x => new { x.ID, x.Name }).ToList());
                    ItemForCloseAccountID.Text = "Depuis un compte "; 
                    break;
                case CloseAccountType.Bank:
                    // Lier les données des banques au contrôle LookUpEdit
                    CloseAccountIDLookUpEdit.BindToDataSource(db.Banks.Select(x => new { x.ID, x.Name }).ToList());
                    ItemForCloseAccountID.Text = "Depuis une banque ";
                    break;
                case CloseAccountType.Drawer:
                    // Lier les données des tiroirs au contrôle LookUpEdit
                    CloseAccountIDLookUpEdit.BindToDataSource(db.Drawers.Select(x => new { x.ID, x.Name }).ToList());
                    ItemForCloseAccountID.Text = "Depuis une caisse ";
                    break;
            }
        }

        private void PettyCashIDLookUpEdit_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
        {
            PettyCashCloseOutView.PettyCashViewForLookup cash = this.PettyCashIDLookUpEdit.GetSelectedDataRow() as PettyCashCloseOutView.PettyCashViewForLookup;
            bool flag = cash == null;
            if (!flag)
            {
                e.DisplayText = string.Format("{0}-{1}", cash.ID, cash.Name);
            }
        }

        private void GroupView_Shown(object sender, EventArgs e)
        {
            bool flag = this.CloseOut == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            PettyCashCloseOut sourceDepr = this.db.PettyCashCloses.SingleOrDefault((PettyCashCloseOut x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.CloseOut = sourceDepr;
                this.RefreshData();
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                XtraMessageBox.Show("Le document n'existe pas");
            }
        }

        public override void New()
        {
            PettyCashCloseOut pettyCashCloseOut = new PettyCashCloseOut();
            pettyCashCloseOut.DateClosed = DateTime.Now;
            pettyCashCloseOut.CloseAccountType = CloseAccountType.Drawer;
            Drawer defualtDrawer = CurrentSession.DefualtDrawer;
            pettyCashCloseOut.CloseAccountID = ((defualtDrawer != null) ? defualtDrawer.ID : 0);
            this.CloseOut = pettyCashCloseOut;
            this.PettyCashIDLookUpEdit.Enabled = true;
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                PettyCashCloseOutView.PettyCashViewForLookup pettyCashView = this.PettyCashIDLookUpEdit.GetSelectedDataRow() as PettyCashCloseOutView.PettyCashViewForLookup;
                bool flag2 = pettyCashView == null;
                if (flag2)
                {
                    this.PettyCashIDLookUpEdit.ErrorText = "*";
                }
                else
                {
                    PettyCash pettyCash = this.db.PettyCashes.SingleOrDefault((PettyCash x) => x.ID == pettyCashView.ID);
                    bool flag3 = pettyCash == null;
                    if (flag3)
                    {
                        this.PettyCashIDLookUpEdit.ErrorText = "*";
                    }
                    else
                    {
                        this.db.PettyCashCloses.AddOrUpdate(new PettyCashCloseOut[]
                        {
                            this.CloseOut
                        });
                        this.db.SaveChanges();
                        int accountID = 0;
                        PettyCashHolder holder = this.db.PettyCashHolders.Single((PettyCashHolder x) => x.ID == pettyCash.HolderID);
                        bool flag4 = pettyCash.Type == PettyCash.PettyCashType.Permanent;
                        if (flag4)
                        {
                            accountID = holder.PermenantAccountID;
                        }
                        else
                        {
                            bool flag5 = pettyCash.Type == PettyCash.PettyCashType.Temporary;
                            if (flag5)
                            {
                                accountID = holder.TemporaryAccountID;
                            }
                        }
                        string statment = string.Concat(new string[]
                        {
                          "Régularisation de caisse financière",
(pettyCash.Type == PettyCash.PettyCashType.Temporary) ? " Temporaire " : " Permanente ",
" Numéro ",
pettyCash.ID.ToString(),
" Par employé ",
holder.Name

                        });
                        Journal journal = this.db.Journals.Include((Journal x) => x.Details).SingleOrDefault((Journal x) => (int)x.ProcessType == 21 && x.ProcessID == this.CloseOut.ID);
                        bool flag6 = journal == null;
                        if (flag6)
                        {
                            journal = new Journal();
                        }
                        journal.BranchID = CurrentSession.CurrentBranch.ID;
                        journal.Date = this.CloseOut.DateClosed;
                        journal.ProcessType = SystemProcess.PettyCashCloseOut;
                        journal.ProcessID = this.CloseOut.ID;
                        journal.Note = statment;
                        this.db.Journals.AddOrUpdate(new Journal[]
                        {
                            journal
                        });
                        this.db.SaveChanges();
                        bool flag7 = journal.Details != null;
                        if (flag7)
                        {
                            this.db.JournalDetails.RemoveRange(journal.Details);
                        }
                        this.db.SaveChanges();
                        pettyCash.IsClosed = true;
                        journal.Details = new BindingList<JournalDetail>();
                        int payAccountID = 0;
                        CloseAccountType closeAccountType = this.CloseOut.CloseAccountType;
                        CloseAccountType closeAccountType2 = closeAccountType;
                        if (closeAccountType2 != CloseAccountType.Account)
                        {
                            if (closeAccountType2 - CloseAccountType.Drawer <= 1)
                            {
                                payAccountID = this.db.BillingDetails.Include((BillingDetail x) => x.Account).Single((BillingDetail x) => x.ID == this.CloseOut.CloseAccountID).AccountID;
                            }
                        }
                        else
                        {
                            payAccountID = this.CloseOut.CloseAccountID;
                        }
                        this.db.JournalDetails.AddRange(new JournalDetail[]
                        {
                            new JournalDetail
                            {
                                AccountID = accountID,
                                Debit = ((pettyCash.Type == PettyCash.PettyCashType.Temporary) ? ((this.CloseOut.RemainigAmount > 0.0) ? 0.0 : Math.Abs(this.CloseOut.RemainigAmount)) : this.CloseOut.AmountSpent),
                                Credit = ((pettyCash.Type == PettyCash.PettyCashType.Temporary) ? ((this.CloseOut.RemainigAmount < 0.0) ? 0.0 : Math.Abs(this.CloseOut.RemainigAmount)) : 0.0),
                                Currency = this.db.Currencies.First<Currency>(),
                                Statement = statment,
                                CurrencyRate = 1.0,
                                JournalID = journal.ID
                            },
                            new JournalDetail
                            {
                                AccountID = payAccountID,
                                Credit = ((pettyCash.Type == PettyCash.PettyCashType.Temporary) ? ((this.CloseOut.RemainigAmount > 0.0) ? 0.0 : Math.Abs(this.CloseOut.RemainigAmount)) : this.CloseOut.AmountSpent),
                                Debit = ((pettyCash.Type == PettyCash.PettyCashType.Temporary) ? ((this.CloseOut.RemainigAmount < 0.0) ? 0.0 : Math.Abs(this.CloseOut.RemainigAmount)) : 0.0),
                                Currency = this.db.Currencies.First<Currency>(),
                                Statement = statment,
                                CurrencyRate = 1.0,
                                JournalID = journal.ID
                            }
                        });
                        this.db.SaveChanges();
                        base.Save();
                        this.RefreshData();
                    }
                }
            }
        }

        public override void RefreshData()
        {
            List<PettyCashViewForLookup> q1 = (from x in (from x in db.PettyCashes
                                                          where x.IsClosed == false
                                                          select x into i
                                                          where db.CashNotes.Any((CashNote x) => (int)x.LinkType == 20 && x.LinkID == (int?)i.ID)
                                                          select i).AsNoTracking().Include((PettyCash x) => x.Holder)
                                               select new PettyCashViewForLookup
                                               {
                                                   ID = x.ID,
                                                   Amount = x.Amount,
                                                   Date = x.Date,
                                                   Name = x.Holder.Name
                                               }).ToList();
            int PettyCashID = CloseOut?.PettyCashID ?? 0;
            q1.AddRange((from x in db.PettyCashes
                         where x.ID == PettyCashID
                         select new PettyCashViewForLookup
                         {
                             ID = x.ID,
                             Amount = x.Amount,
                             Date = x.Date,
                             Name = x.Holder.Name
                         }).ToList());
            PettyCashIDLookUpEdit.Properties.DataSource = q1;
            base.RefreshData();
        }
        public override void Delete()
        {
            bool flag = this.CloseOut == null || this.CloseOut.ID <= 0;
            if (!flag)
            {
                PettyCashCloseOutView.Delete(this.CloseOut.ID);
                this.New();
                base.Delete();
            }
        }

        public static void Delete(int id)
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                PettyCashCloseOut CloseOut = db.PettyCashCloses.SingleOrDefault((PettyCashCloseOut x) => x.ID == id);
                PettyCash pettyCash = db.PettyCashes.SingleOrDefault((PettyCash x) => x.ID == CloseOut.PettyCashID);
                pettyCash.IsClosed = false;
                Journal journal = db.Journals.Include((Journal x) => x.Details).SingleOrDefault((Journal x) => (int)x.ProcessType == 21 && x.ProcessID == CloseOut.ID);
                bool flag = journal != null;
                if (flag)
                {
                    db.JournalDetails.RemoveRange(journal.Details);
                    db.Journals.Remove(journal);
                }
                db.PettyCashCloses.Remove(CloseOut);
                db.SaveChanges();
            }
        }

        public static List<PettyCashCloseOutReportModel> GetPrintDataSource(int Id)
        {
            return PettyCashCloseOutView.GetPrintDataSource(new int[]
            {
                Id
            });
        }

        public static List<PettyCashCloseOutReportModel> GetPrintDataSource(IList<int> Ids)
        {
            ERPDataContext context = new ERPDataContext();
            try
            {
                List<PettyCashCloseOut> q1 = (from i in context.PettyCashCloses.AsNoTracking()
                                              where Ids.Contains(i.ID)
                                              select i).ToList();
                return (from i in q1
                        join P in context.PettyCashes on i.PettyCashID equals P.ID
                        join H in context.PettyCashHolders on P.HolderID equals H.ID
                        from b in context.Banks.Where((Bank x) => x.ID == i.CloseAccountID).DefaultIfEmpty()
                        from a in context.Accounts.Where((Account x) => x.ID == i.CloseAccountID).DefaultIfEmpty()
                        from d in context.Drawers.Where((Drawer x) => x.ID == i.CloseAccountID).DefaultIfEmpty()
                        select new PettyCashCloseOutReportModel
                        {
                            ID = i.ID,
                            CloseDate = i.DateClosed,
                            CloseType = i.CloseAccountType,
                            PettycashName = P.ID + " | " + H.Name,
                            AmountSpent = i.AmountSpent,
                            Amount = i.Amount,
                            AccountName = ((i.CloseAccountType == CloseAccountType.Account) ? a.Name : ((i.CloseAccountType == CloseAccountType.Bank) ? b.Name : d.Name))
                        }).ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }

        public override void Print()
        {
            PettyCashCloseOut petty = this.CloseOut;
            bool flag = petty == null || petty.ID == 0;
            if (!flag)
            {
                PettyCashCloseOutReport.Print(PettyCashCloseOutView.GetPrintDataSource(petty.ID));
                base.Print();
            }
        }

        public static void Print(IList<int> Ids)
        {
            PettyCashCloseOutReport.Print(PettyCashCloseOutView.GetPrintDataSource(Ids));
        }

        private static PettyCashCloseOutView instance;

        private ERPDataContext db;

        private class PettyCashViewForLookup
        {
            [DisplayName("Numééro")]
            public int ID { get; set; }

            [DisplayName("Nom")]
            public string Name { get; set; }

            [DisplayName("Montant")]
            public double Amount { get; set; }

            [DisplayName("Date")]
            public DateTime Date { get; set; }
        }
    }
}
