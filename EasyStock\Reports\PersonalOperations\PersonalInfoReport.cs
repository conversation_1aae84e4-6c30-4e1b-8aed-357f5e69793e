﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using EasyStock.ReportModels;

namespace EasyStock.Reports.PersonalOperations
{
	// Token: 0x020003A0 RID: 928
	public class PersonalInfoReport : XtraReport
	{
		// Token: 0x060015B5 RID: 5557 RVA: 0x0000B03E File Offset: 0x0000923E
		public PersonalInfoReport(PersonalInfoSummaryModel model)
		{
			this.InitializeComponent();
			this.objectDataSource1.DataSource = model;
		}

		// Token: 0x060015B6 RID: 5558 RVA: 0x001E0058 File Offset: 0x001DE258
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060015B7 RID: 5559 RVA: 0x001E0090 File Offset: 0x001DE290
		private void InitializeComponent()
		{
			this.components = new Container();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.Detail = new DetailBand();
			this.xrTable2 = new XRTable();
			this.xrTableRow7 = new XRTableRow();
			this.xrTableCell14 = new XRTableCell();
			this.xrTableRow8 = new XRTableRow();
			this.xrTableCell16 = new XRTableCell();
			this.xrTableRow9 = new XRTableRow();
			this.xrTableCell18 = new XRTableCell();
			this.xrTableRow10 = new XRTableRow();
			this.xrTableCell20 = new XRTableCell();
			this.xrTableRow11 = new XRTableRow();
			this.xrTableCell22 = new XRTableCell();
			this.xrTableRow12 = new XRTableRow();
			this.xrTableCell24 = new XRTableCell();
			this.xrTableRow13 = new XRTableRow();
			this.xrTableCell13 = new XRTableCell();
			this.xrTableCell23 = new XRTableCell();
			this.xrTableRow14 = new XRTableRow();
			this.xrTableCell25 = new XRTableCell();
			this.xrTableCell26 = new XRTableCell();
			this.xrLabel1 = new XRLabel();
			this.xrLabel2 = new XRLabel();
			this.xrLabel3 = new XRLabel();
			this.xrLabel4 = new XRLabel();
			this.xrLabel5 = new XRLabel();
			this.xrLabel6 = new XRLabel();
			this.xrLabel7 = new XRLabel();
			this.xrLabel8 = new XRLabel();
			this.xrLabel9 = new XRLabel();
			this.xrLabel10 = new XRLabel();
			this.xrLabel11 = new XRLabel();
			this.xrLabel12 = new XRLabel();
			this.xrTableCell1 = new XRTableCell();
			this.xrTableCell2 = new XRTableCell();
			this.xrTableCell3 = new XRTableCell();
			this.xrTableCell4 = new XRTableCell();
			this.objectDataSource1 = new ObjectDataSource(this.components);
			((ISupportInitialize)this.xrTable2).BeginInit();
			((ISupportInitialize)this.objectDataSource1).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.TopMargin.Dpi = 254f;
			this.TopMargin.HeightF = 0f;
			this.TopMargin.Name = "TopMargin";
			this.BottomMargin.Dpi = 254f;
			this.BottomMargin.HeightF = 0f;
			this.BottomMargin.Name = "BottomMargin";
			this.Detail.BorderColor = Color.White;
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.xrTable2,
				this.xrLabel1,
				this.xrLabel2,
				this.xrLabel3,
				this.xrLabel4,
				this.xrLabel5,
				this.xrLabel6,
				this.xrLabel7,
				this.xrLabel8,
				this.xrLabel9,
				this.xrLabel10,
				this.xrLabel11,
				this.xrLabel12
			});
			this.Detail.Dpi = 254f;
			this.Detail.HeightF = 574.5f;
			this.Detail.Name = "Detail";
			this.Detail.StylePriority.UseBorderColor = false;
			this.xrTable2.AnchorHorizontal = HorizontalAnchorStyles.Left;
			this.xrTable2.BackColor = Color.White;
			this.xrTable2.Borders = BorderSide.None;
			this.xrTable2.BorderWidth = 1f;
			this.xrTable2.Dpi = 254f;
			this.xrTable2.LocationFloat = new PointFloat(1248.76f, 0f);
			this.xrTable2.Name = "xrTable2";
			this.xrTable2.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable2.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow7,
				this.xrTableRow8,
				this.xrTableRow9,
				this.xrTableRow10,
				this.xrTableRow11,
				this.xrTableRow12,
				this.xrTableRow13,
				this.xrTableRow14
			});
			this.xrTable2.SizeF = new SizeF(723.2396f, 571.9722f);
			this.xrTable2.StylePriority.UseBackColor = false;
			this.xrTable2.StylePriority.UseBorders = false;
			this.xrTable2.StylePriority.UseBorderWidth = false;
			this.xrTable2.StylePriority.UseTextAlignment = false;
			this.xrTable2.TextAlignment = TextAlignment.MiddleRight;
			this.xrTableRow7.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell14
			});
			this.xrTableRow7.Dpi = 254f;
			this.xrTableRow7.Name = "xrTableRow7";
			this.xrTableRow7.Weight = 0.5609756097560976;
			this.xrTableCell14.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell14.BorderColor = Color.White;
			this.xrTableCell14.Borders = BorderSide.All;
			this.xrTableCell14.Dpi = 254f;
			this.xrTableCell14.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell14.ForeColor = Color.White;
			this.xrTableCell14.Multiline = true;
			this.xrTableCell14.Name = "xrTableCell14";
			this.xrTableCell14.StylePriority.UseBackColor = false;
			this.xrTableCell14.StylePriority.UseBorderColor = false;
			this.xrTableCell14.StylePriority.UseBorders = false;
			this.xrTableCell14.StylePriority.UseFont = false;
			this.xrTableCell14.StylePriority.UseForeColor = false;
			this.xrTableCell14.StylePriority.UseTextAlignment = false;
            this.xrTableCell14.Text = "Données du compte";
            this.xrTableCell14.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell14.Weight = 22.41176470588235;
			this.xrTableRow8.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell1,
				this.xrTableCell16
			});
			this.xrTableRow8.Dpi = 254f;
			this.xrTableRow8.Name = "xrTableRow8";
			this.xrTableRow8.Weight = 0.5609756097560976;
			this.xrTableCell16.BackColor = Color.Empty;
			this.xrTableCell16.BorderColor = Color.White;
			this.xrTableCell16.Borders = BorderSide.All;
			this.xrTableCell16.Dpi = 254f;
			this.xrTableCell16.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalInvoices]")
			});
			this.xrTableCell16.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell16.ForeColor = Color.Black;
			this.xrTableCell16.Multiline = true;
			this.xrTableCell16.Name = "xrTableCell16";
			this.xrTableCell16.StylePriority.UseBackColor = false;
			this.xrTableCell16.StylePriority.UseBorderColor = false;
			this.xrTableCell16.StylePriority.UseBorders = false;
			this.xrTableCell16.StylePriority.UseFont = false;
			this.xrTableCell16.StylePriority.UseForeColor = false;
			this.xrTableCell16.StylePriority.UseTextAlignment = false;
            this.xrTableCell16.Text = "Montant total des factures";
            this.xrTableCell16.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell16.Weight = 16.764360866293465;
			this.xrTableRow9.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell2,
				this.xrTableCell18
			});
			this.xrTableRow9.Dpi = 254f;
			this.xrTableRow9.Name = "xrTableRow9";
			this.xrTableRow9.Weight = 0.5609756097560976;
			this.xrTableCell18.BackColor = Color.Empty;
			this.xrTableCell18.BorderColor = Color.White;
			this.xrTableCell18.Borders = BorderSide.All;
			this.xrTableCell18.Dpi = 254f;
			this.xrTableCell18.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalReturnInvoices]")
			});
			this.xrTableCell18.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell18.ForeColor = Color.Black;
			this.xrTableCell18.Multiline = true;
			this.xrTableCell18.Name = "xrTableCell18";
			this.xrTableCell18.StylePriority.UseBackColor = false;
			this.xrTableCell18.StylePriority.UseBorderColor = false;
			this.xrTableCell18.StylePriority.UseBorders = false;
			this.xrTableCell18.StylePriority.UseFont = false;
			this.xrTableCell18.StylePriority.UseForeColor = false;
			this.xrTableCell18.StylePriority.UseTextAlignment = false;
            this.xrTableCell18.Text = "Montant total des retours";
            this.xrTableCell18.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell18.Weight = 16.764360866293465;
			this.xrTableRow10.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell3,
				this.xrTableCell20
			});
			this.xrTableRow10.Dpi = 254f;
			this.xrTableRow10.Name = "xrTableRow10";
			this.xrTableRow10.Weight = 0.5609756097560976;
			this.xrTableCell20.BackColor = Color.Empty;
			this.xrTableCell20.BorderColor = Color.White;
			this.xrTableCell20.Borders = BorderSide.All;
			this.xrTableCell20.Dpi = 254f;
			this.xrTableCell20.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalCashIn]")
			});
			this.xrTableCell20.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell20.ForeColor = Color.Black;
			this.xrTableCell20.Multiline = true;
			this.xrTableCell20.Name = "xrTableCell20";
			this.xrTableCell20.StylePriority.UseBackColor = false;
			this.xrTableCell20.StylePriority.UseBorderColor = false;
			this.xrTableCell20.StylePriority.UseBorders = false;
			this.xrTableCell20.StylePriority.UseFont = false;
			this.xrTableCell20.StylePriority.UseForeColor = false;
			this.xrTableCell20.StylePriority.UseTextAlignment = false;
			this.xrTableCell20.Text = "Montant total des encaissements en espèces";
			this.xrTableCell20.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell20.Weight = 16.764360866293465;
			this.xrTableRow11.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell4,
				this.xrTableCell22
			});
			this.xrTableRow11.Dpi = 254f;
			this.xrTableRow11.Name = "xrTableRow11";
			this.xrTableRow11.Weight = 0.5609756097560976;
			this.xrTableCell22.BackColor = Color.Empty;
			this.xrTableCell22.BorderColor = Color.White;
			this.xrTableCell22.Borders = BorderSide.All;
			this.xrTableCell22.Dpi = 254f;
			this.xrTableCell22.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalCashOut]")
			});
			this.xrTableCell22.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell22.ForeColor = Color.Black;
			this.xrTableCell22.Multiline = true;
			this.xrTableCell22.Name = "xrTableCell22";
			this.xrTableCell22.StylePriority.UseBackColor = false;
			this.xrTableCell22.StylePriority.UseBorderColor = false;
			this.xrTableCell22.StylePriority.UseBorders = false;
			this.xrTableCell22.StylePriority.UseFont = false;
			this.xrTableCell22.StylePriority.UseForeColor = false;
			this.xrTableCell22.StylePriority.UseTextAlignment = false;
			this.xrTableCell22.Text = "Montant total des paiements en espèces";
			this.xrTableCell22.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell22.Weight = 16.764360866293465;
			this.xrTableRow12.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell24
			});
			this.xrTableRow12.Dpi = 254f;
			this.xrTableRow12.Name = "xrTableRow12";
			this.xrTableRow12.Weight = 0.5609756097560976;
			this.xrTableCell24.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell24.BorderColor = Color.White;
			this.xrTableCell24.Borders = BorderSide.All;
			this.xrTableCell24.Dpi = 254f;
			this.xrTableCell24.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell24.ForeColor = Color.White;
			this.xrTableCell24.Multiline = true;
			this.xrTableCell24.Name = "xrTableCell24";
			this.xrTableCell24.StylePriority.UseBackColor = false;
			this.xrTableCell24.StylePriority.UseBorderColor = false;
			this.xrTableCell24.StylePriority.UseBorders = false;
			this.xrTableCell24.StylePriority.UseFont = false;
			this.xrTableCell24.StylePriority.UseForeColor = false;
			this.xrTableCell24.StylePriority.UseTextAlignment = false;
			this.xrTableCell24.Text = "Solde";
			this.xrTableCell24.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell24.Weight = 22.411764705882355;
			this.xrTableRow13.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell13,
				this.xrTableCell23
			});
			this.xrTableRow13.Dpi = 254f;
			this.xrTableRow13.Name = "xrTableRow13";
			this.xrTableRow13.Weight = 0.5609756097560976;
			this.xrTableCell13.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell13.BorderColor = Color.White;
			this.xrTableCell13.Borders = BorderSide.All;
			this.xrTableCell13.Dpi = 254f;
			this.xrTableCell13.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell13.ForeColor = Color.White;
			this.xrTableCell13.Multiline = true;
			this.xrTableCell13.Name = "xrTableCell13";
			this.xrTableCell13.StylePriority.UseBackColor = false;
			this.xrTableCell13.StylePriority.UseBorderColor = false;
			this.xrTableCell13.StylePriority.UseBorders = false;
			this.xrTableCell13.StylePriority.UseFont = false;
			this.xrTableCell13.StylePriority.UseForeColor = false;
			this.xrTableCell13.StylePriority.UseTextAlignment = false;
			this.xrTableCell13.Text = "Créditeur";
			this.xrTableCell13.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell13.Weight = 16.8532865355921;
			this.xrTableCell23.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell23.BorderColor = Color.White;
			this.xrTableCell23.Borders = BorderSide.All;
			this.xrTableCell23.Dpi = 254f;
			this.xrTableCell23.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell23.ForeColor = Color.White;
			this.xrTableCell23.Multiline = true;
			this.xrTableCell23.Name = "xrTableCell23";
			this.xrTableCell23.StylePriority.UseBackColor = false;
			this.xrTableCell23.StylePriority.UseBorderColor = false;
			this.xrTableCell23.StylePriority.UseBorders = false;
			this.xrTableCell23.StylePriority.UseFont = false;
			this.xrTableCell23.StylePriority.UseForeColor = false;
			this.xrTableCell23.StylePriority.UseTextAlignment = false;
			this.xrTableCell23.Text = "Débit";
			this.xrTableCell23.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell23.Weight = 16.76436052323143;
			this.xrTableRow14.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell25,
				this.xrTableCell26
			});
			this.xrTableRow14.Dpi = 254f;
			this.xrTableRow14.Name = "xrTableRow14";
			this.xrTableRow14.Weight = 0.5609756097560976;
			this.xrTableCell25.BackColor = Color.Empty;
			this.xrTableCell25.BorderColor = Color.White;
			this.xrTableCell25.Borders = BorderSide.All;
			this.xrTableCell25.Dpi = 254f;
			this.xrTableCell25.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[BalanceCredit]")
			});
			this.xrTableCell25.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell25.ForeColor = Color.Black;
			this.xrTableCell25.Multiline = true;
			this.xrTableCell25.Name = "xrTableCell25";
			this.xrTableCell25.StylePriority.UseBackColor = false;
			this.xrTableCell25.StylePriority.UseBorderColor = false;
			this.xrTableCell25.StylePriority.UseBorders = false;
			this.xrTableCell25.StylePriority.UseFont = false;
			this.xrTableCell25.StylePriority.UseForeColor = false;
			this.xrTableCell25.StylePriority.UseTextAlignment = false;
			this.xrTableCell25.Text = "xrTableCell25";
			this.xrTableCell25.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell25.Weight = 16.89009001184586;
			this.xrTableCell26.BackColor = Color.Empty;
			this.xrTableCell26.BorderColor = Color.White;
			this.xrTableCell26.Borders = BorderSide.All;
			this.xrTableCell26.Dpi = 254f;
			this.xrTableCell26.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[BalanceDebit]")
			});
			this.xrTableCell26.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell26.ForeColor = Color.Black;
			this.xrTableCell26.Multiline = true;
			this.xrTableCell26.Name = "xrTableCell26";
			this.xrTableCell26.StylePriority.UseBackColor = false;
			this.xrTableCell26.StylePriority.UseBorderColor = false;
			this.xrTableCell26.StylePriority.UseBorders = false;
			this.xrTableCell26.StylePriority.UseFont = false;
			this.xrTableCell26.StylePriority.UseForeColor = false;
			this.xrTableCell26.StylePriority.UseTextAlignment = false;
			this.xrTableCell26.Text = "xrTableCell26";
			this.xrTableCell26.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell26.TextFormatString = "{0:0.##}";
			this.xrTableCell26.Weight = 16.72755704697767;
			this.xrLabel1.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel1.BackColor = Color.White;
			this.xrLabel1.BorderColor = Color.White;
			this.xrLabel1.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel1.Borders = BorderSide.None;
			this.xrLabel1.BorderWidth = 1f;
			this.xrLabel1.Dpi = 254f;
			this.xrLabel1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ID]")
			});
			this.xrLabel1.Font = new Font("Arial", 9.75f);
			this.xrLabel1.ForeColor = Color.Black;
			this.xrLabel1.LocationFloat = new PointFloat(269.3021f, 2.527893f);
			this.xrLabel1.Multiline = true;
			this.xrLabel1.Name = "xrLabel1";
			this.xrLabel1.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel1.SizeF = new SizeF(505.1354f, 71.49653f);
			this.xrLabel1.StylePriority.UseBackColor = false;
			this.xrLabel1.StylePriority.UseBorderColor = false;
			this.xrLabel1.StylePriority.UseBorderDashStyle = false;
			this.xrLabel1.StylePriority.UseBorders = false;
			this.xrLabel1.StylePriority.UseBorderWidth = false;
			this.xrLabel1.StylePriority.UseFont = false;
			this.xrLabel1.StylePriority.UseForeColor = false;
			this.xrLabel1.StylePriority.UsePadding = false;
			this.xrLabel1.StylePriority.UseTextAlignment = false;
			this.xrLabel1.Text = "xrTableCell4";
			this.xrLabel1.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel2.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel2.BackColor = Color.FromArgb(2, 119, 189);
			this.xrLabel2.BorderColor = Color.White;
			this.xrLabel2.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel2.Borders = BorderSide.All;
			this.xrLabel2.BorderWidth = 1f;
			this.xrLabel2.Dpi = 254f;
			this.xrLabel2.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel2.ForeColor = Color.White;
			this.xrLabel2.LocationFloat = new PointFloat(0f, 2.527893f);
			this.xrLabel2.Multiline = true;
			this.xrLabel2.Name = "xrLabel2";
			this.xrLabel2.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel2.SizeF = new SizeF(269.3024f, 71.49653f);
			this.xrLabel2.StylePriority.UseBackColor = false;
			this.xrLabel2.StylePriority.UseBorderColor = false;
			this.xrLabel2.StylePriority.UseBorderDashStyle = false;
			this.xrLabel2.StylePriority.UseBorders = false;
			this.xrLabel2.StylePriority.UseBorderWidth = false;
			this.xrLabel2.StylePriority.UseFont = false;
			this.xrLabel2.StylePriority.UseForeColor = false;
			this.xrLabel2.StylePriority.UsePadding = false;
			this.xrLabel2.StylePriority.UseTextAlignment = false;
			this.xrLabel2.Text = "Code";
			this.xrLabel2.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel3.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel3.BackColor = Color.White;
			this.xrLabel3.BorderColor = Color.White;
			this.xrLabel3.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel3.Borders = BorderSide.None;
			this.xrLabel3.BorderWidth = 1f;
			this.xrLabel3.Dpi = 254f;
			this.xrLabel3.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Name]")
			});
			this.xrLabel3.Font = new Font("Arial", 9.75f);
			this.xrLabel3.ForeColor = Color.Black;
			this.xrLabel3.LocationFloat = new PointFloat(269.3024f, 74.02441f);
			this.xrLabel3.Multiline = true;
			this.xrLabel3.Name = "xrLabel3";
			this.xrLabel3.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel3.SizeF = new SizeF(505.1351f, 71.49652f);
			this.xrLabel3.StylePriority.UseBackColor = false;
			this.xrLabel3.StylePriority.UseBorderColor = false;
			this.xrLabel3.StylePriority.UseBorderDashStyle = false;
			this.xrLabel3.StylePriority.UseBorders = false;
			this.xrLabel3.StylePriority.UseBorderWidth = false;
			this.xrLabel3.StylePriority.UseFont = false;
			this.xrLabel3.StylePriority.UseForeColor = false;
			this.xrLabel3.StylePriority.UsePadding = false;
			this.xrLabel3.StylePriority.UseTextAlignment = false;
			this.xrLabel3.Text = "xrTableCell11";
			this.xrLabel3.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel4.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel4.BackColor = Color.FromArgb(2, 119, 189);
			this.xrLabel4.BorderColor = Color.White;
			this.xrLabel4.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel4.Borders = BorderSide.All;
			this.xrLabel4.BorderWidth = 1f;
			this.xrLabel4.Dpi = 254f;
			this.xrLabel4.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel4.ForeColor = Color.White;
			this.xrLabel4.LocationFloat = new PointFloat(0f, 74.02441f);
			this.xrLabel4.Multiline = true;
			this.xrLabel4.Name = "xrLabel4";
			this.xrLabel4.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel4.SizeF = new SizeF(269.3025f, 71.49652f);
			this.xrLabel4.StylePriority.UseBackColor = false;
			this.xrLabel4.StylePriority.UseBorderColor = false;
			this.xrLabel4.StylePriority.UseBorderDashStyle = false;
			this.xrLabel4.StylePriority.UseBorders = false;
			this.xrLabel4.StylePriority.UseBorderWidth = false;
			this.xrLabel4.StylePriority.UseFont = false;
			this.xrLabel4.StylePriority.UseForeColor = false;
			this.xrLabel4.StylePriority.UsePadding = false;
			this.xrLabel4.StylePriority.UseTextAlignment = false;
			this.xrLabel4.Text = "Nom";
			this.xrLabel4.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel5.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel5.BackColor = Color.White;
			this.xrLabel5.BorderColor = Color.White;
			this.xrLabel5.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel5.Borders = BorderSide.None;
			this.xrLabel5.BorderWidth = 1f;
			this.xrLabel5.Dpi = 254f;
			this.xrLabel5.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Phone]")
			});
			this.xrLabel5.Font = new Font("Arial", 9.75f);
			this.xrLabel5.ForeColor = Color.Black;
			this.xrLabel5.LocationFloat = new PointFloat(269.3024f, 145.5209f);
			this.xrLabel5.Multiline = true;
			this.xrLabel5.Name = "xrLabel5";
			this.xrLabel5.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel5.SizeF = new SizeF(505.1351f, 71.49654f);
			this.xrLabel5.StylePriority.UseBackColor = false;
			this.xrLabel5.StylePriority.UseBorderColor = false;
			this.xrLabel5.StylePriority.UseBorderDashStyle = false;
			this.xrLabel5.StylePriority.UseBorders = false;
			this.xrLabel5.StylePriority.UseBorderWidth = false;
			this.xrLabel5.StylePriority.UseFont = false;
			this.xrLabel5.StylePriority.UseForeColor = false;
			this.xrLabel5.StylePriority.UsePadding = false;
			this.xrLabel5.StylePriority.UseTextAlignment = false;
			this.xrLabel5.Text = "xrTableCell1";
			this.xrLabel5.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel6.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel6.BackColor = Color.FromArgb(2, 119, 189);
			this.xrLabel6.BorderColor = Color.White;
			this.xrLabel6.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel6.Borders = BorderSide.All;
			this.xrLabel6.BorderWidth = 1f;
			this.xrLabel6.Dpi = 254f;
			this.xrLabel6.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel6.ForeColor = Color.White;
			this.xrLabel6.LocationFloat = new PointFloat(0f, 145.5209f);
			this.xrLabel6.Multiline = true;
			this.xrLabel6.Name = "xrLabel6";
			this.xrLabel6.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel6.SizeF = new SizeF(269.3025f, 71.49654f);
			this.xrLabel6.StylePriority.UseBackColor = false;
			this.xrLabel6.StylePriority.UseBorderColor = false;
			this.xrLabel6.StylePriority.UseBorderDashStyle = false;
			this.xrLabel6.StylePriority.UseBorders = false;
			this.xrLabel6.StylePriority.UseBorderWidth = false;
			this.xrLabel6.StylePriority.UseFont = false;
			this.xrLabel6.StylePriority.UseForeColor = false;
			this.xrLabel6.StylePriority.UsePadding = false;
			this.xrLabel6.StylePriority.UseTextAlignment = false;
			this.xrLabel6.Text = "Téléphone";
			this.xrLabel6.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel7.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel7.BackColor = Color.White;
			this.xrLabel7.BorderColor = Color.White;
			this.xrLabel7.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel7.Borders = BorderSide.None;
			this.xrLabel7.BorderWidth = 1f;
			this.xrLabel7.Dpi = 254f;
			this.xrLabel7.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[City]")
			});
			this.xrLabel7.Font = new Font("Arial", 9.75f);
			this.xrLabel7.ForeColor = Color.Black;
			this.xrLabel7.LocationFloat = new PointFloat(269.3024f, 217.0175f);
			this.xrLabel7.Multiline = true;
			this.xrLabel7.Name = "xrLabel7";
			this.xrLabel7.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel7.SizeF = new SizeF(505.1351f, 71.49651f);
			this.xrLabel7.StylePriority.UseBackColor = false;
			this.xrLabel7.StylePriority.UseBorderColor = false;
			this.xrLabel7.StylePriority.UseBorderDashStyle = false;
			this.xrLabel7.StylePriority.UseBorders = false;
			this.xrLabel7.StylePriority.UseBorderWidth = false;
			this.xrLabel7.StylePriority.UseFont = false;
			this.xrLabel7.StylePriority.UseForeColor = false;
			this.xrLabel7.StylePriority.UsePadding = false;
			this.xrLabel7.StylePriority.UseTextAlignment = false;
			this.xrLabel7.Text = "xrTableCell3";
			this.xrLabel7.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel8.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel8.BackColor = Color.FromArgb(2, 119, 189);
			this.xrLabel8.BorderColor = Color.White;
			this.xrLabel8.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel8.Borders = BorderSide.All;
			this.xrLabel8.BorderWidth = 1f;
			this.xrLabel8.Dpi = 254f;
			this.xrLabel8.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel8.ForeColor = Color.White;
			this.xrLabel8.LocationFloat = new PointFloat(0f, 217.0175f);
			this.xrLabel8.Multiline = true;
			this.xrLabel8.Name = "xrLabel8";
			this.xrLabel8.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel8.SizeF = new SizeF(269.3025f, 71.49651f);
			this.xrLabel8.StylePriority.UseBackColor = false;
			this.xrLabel8.StylePriority.UseBorderColor = false;
			this.xrLabel8.StylePriority.UseBorderDashStyle = false;
			this.xrLabel8.StylePriority.UseBorders = false;
			this.xrLabel8.StylePriority.UseBorderWidth = false;
			this.xrLabel8.StylePriority.UseFont = false;
			this.xrLabel8.StylePriority.UseForeColor = false;
			this.xrLabel8.StylePriority.UsePadding = false;
			this.xrLabel8.StylePriority.UseTextAlignment = false;
			this.xrLabel8.Text = "Ville";
			this.xrLabel8.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel9.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel9.BackColor = Color.White;
			this.xrLabel9.BorderColor = Color.White;
			this.xrLabel9.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel9.Borders = BorderSide.None;
			this.xrLabel9.BorderWidth = 1f;
			this.xrLabel9.Dpi = 254f;
			this.xrLabel9.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Address]")
			});
			this.xrLabel9.Font = new Font("Arial", 9.75f);
			this.xrLabel9.ForeColor = Color.Black;
			this.xrLabel9.LocationFloat = new PointFloat(269.3024f, 360.0105f);
			this.xrLabel9.Multiline = true;
			this.xrLabel9.Name = "xrLabel9";
			this.xrLabel9.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel9.SizeF = new SizeF(505.1351f, 214.4895f);
			this.xrLabel9.StylePriority.UseBackColor = false;
			this.xrLabel9.StylePriority.UseBorderColor = false;
			this.xrLabel9.StylePriority.UseBorderDashStyle = false;
			this.xrLabel9.StylePriority.UseBorders = false;
			this.xrLabel9.StylePriority.UseBorderWidth = false;
			this.xrLabel9.StylePriority.UseFont = false;
			this.xrLabel9.StylePriority.UseForeColor = false;
			this.xrLabel9.StylePriority.UsePadding = false;
			this.xrLabel9.StylePriority.UseTextAlignment = false;
			this.xrLabel9.Text = "xrTableCell7";
			this.xrLabel9.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel10.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel10.BackColor = Color.FromArgb(2, 119, 189);
			this.xrLabel10.BorderColor = Color.White;
			this.xrLabel10.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel10.Borders = BorderSide.All;
			this.xrLabel10.BorderWidth = 1f;
			this.xrLabel10.Dpi = 254f;
			this.xrLabel10.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel10.ForeColor = Color.White;
			this.xrLabel10.LocationFloat = new PointFloat(0f, 360.0105f);
			this.xrLabel10.Multiline = true;
			this.xrLabel10.Name = "xrLabel10";
			this.xrLabel10.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel10.SizeF = new SizeF(269.3025f, 214.4895f);
			this.xrLabel10.StylePriority.UseBackColor = false;
			this.xrLabel10.StylePriority.UseBorderColor = false;
			this.xrLabel10.StylePriority.UseBorderDashStyle = false;
			this.xrLabel10.StylePriority.UseBorders = false;
			this.xrLabel10.StylePriority.UseBorderWidth = false;
			this.xrLabel10.StylePriority.UseFont = false;
			this.xrLabel10.StylePriority.UseForeColor = false;
			this.xrLabel10.StylePriority.UsePadding = false;
			this.xrLabel10.StylePriority.UseTextAlignment = false;
			this.xrLabel10.Text = "Adresse";
			this.xrLabel10.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel11.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel11.BackColor = Color.White;
			this.xrLabel11.BorderColor = Color.White;
			this.xrLabel11.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel11.Borders = BorderSide.None;
			this.xrLabel11.BorderWidth = 1f;
			this.xrLabel11.Dpi = 254f;
			this.xrLabel11.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Mobile]")
			});
			this.xrLabel11.Font = new Font("Arial", 9.75f);
			this.xrLabel11.ForeColor = Color.Black;
			this.xrLabel11.LocationFloat = new PointFloat(269.3024f, 288.514f);
			this.xrLabel11.Multiline = true;
			this.xrLabel11.Name = "xrLabel11";
			this.xrLabel11.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel11.SizeF = new SizeF(505.1351f, 71.49655f);
			this.xrLabel11.StylePriority.UseBackColor = false;
			this.xrLabel11.StylePriority.UseBorderColor = false;
			this.xrLabel11.StylePriority.UseBorderDashStyle = false;
			this.xrLabel11.StylePriority.UseBorders = false;
			this.xrLabel11.StylePriority.UseBorderWidth = false;
			this.xrLabel11.StylePriority.UseFont = false;
			this.xrLabel11.StylePriority.UseForeColor = false;
			this.xrLabel11.StylePriority.UsePadding = false;
			this.xrLabel11.StylePriority.UseTextAlignment = false;
			this.xrLabel11.Text = "xrTableCell9";
			this.xrLabel11.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLabel12.AnchorHorizontal = HorizontalAnchorStyles.Right;
			this.xrLabel12.BackColor = Color.FromArgb(2, 119, 189);
			this.xrLabel12.BorderColor = Color.White;
			this.xrLabel12.BorderDashStyle = BorderDashStyle.Solid;
			this.xrLabel12.Borders = BorderSide.All;
			this.xrLabel12.BorderWidth = 1f;
			this.xrLabel12.Dpi = 254f;
			this.xrLabel12.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrLabel12.ForeColor = Color.White;
			this.xrLabel12.LocationFloat = new PointFloat(0f, 288.514f);
			this.xrLabel12.Multiline = true;
			this.xrLabel12.Name = "xrLabel12";
			this.xrLabel12.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrLabel12.SizeF = new SizeF(269.3025f, 71.49655f);
			this.xrLabel12.StylePriority.UseBackColor = false;
			this.xrLabel12.StylePriority.UseBorderColor = false;
			this.xrLabel12.StylePriority.UseBorderDashStyle = false;
			this.xrLabel12.StylePriority.UseBorders = false;
			this.xrLabel12.StylePriority.UseBorderWidth = false;
			this.xrLabel12.StylePriority.UseFont = false;
			this.xrLabel12.StylePriority.UseForeColor = false;
			this.xrLabel12.StylePriority.UsePadding = false;
			this.xrLabel12.StylePriority.UseTextAlignment = false;
			this.xrLabel12.Text = "Mobile";
			this.xrLabel12.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell1.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell1.BorderColor = Color.White;
			this.xrTableCell1.Borders = BorderSide.All;
			this.xrTableCell1.Dpi = 254f;
			this.xrTableCell1.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell1.ForeColor = Color.White;
			this.xrTableCell1.Multiline = true;
			this.xrTableCell1.Name = "xrTableCell1";
			this.xrTableCell1.StylePriority.UseBackColor = false;
			this.xrTableCell1.StylePriority.UseBorderColor = false;
			this.xrTableCell1.StylePriority.UseBorders = false;
			this.xrTableCell1.StylePriority.UseFont = false;
			this.xrTableCell1.StylePriority.UseForeColor = false;
			this.xrTableCell1.StylePriority.UseTextAlignment = false;
            this.xrTableCell1.Text = "Montant total des factures";
            this.xrTableCell1.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell1.Weight = 16.853286486916975;
			this.xrTableCell2.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell2.BorderColor = Color.White;
			this.xrTableCell2.Borders = BorderSide.All;
			this.xrTableCell2.Dpi = 254f;
			this.xrTableCell2.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell2.ForeColor = Color.White;
			this.xrTableCell2.Multiline = true;
			this.xrTableCell2.Name = "xrTableCell2";
			this.xrTableCell2.StylePriority.UseBackColor = false;
			this.xrTableCell2.StylePriority.UseBorderColor = false;
			this.xrTableCell2.StylePriority.UseBorders = false;
			this.xrTableCell2.StylePriority.UseFont = false;
			this.xrTableCell2.StylePriority.UseForeColor = false;
			this.xrTableCell2.StylePriority.UseTextAlignment = false;
            this.xrTableCell2.Text = "Montant total des retours";
            this.xrTableCell2.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell2.Weight = 16.853286486916975;
			this.xrTableCell3.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell3.BorderColor = Color.White;
			this.xrTableCell3.Borders = BorderSide.All;
			this.xrTableCell3.Dpi = 254f;
			this.xrTableCell3.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell3.ForeColor = Color.White;
			this.xrTableCell3.Multiline = true;
			this.xrTableCell3.Name = "xrTableCell3";
			this.xrTableCell3.StylePriority.UseBackColor = false;
			this.xrTableCell3.StylePriority.UseBorderColor = false;
			this.xrTableCell3.StylePriority.UseBorders = false;
			this.xrTableCell3.StylePriority.UseFont = false;
			this.xrTableCell3.StylePriority.UseForeColor = false;
			this.xrTableCell3.StylePriority.UseTextAlignment = false;
			this.xrTableCell3.Text = "Montant total des encaissements en espèces";
			this.xrTableCell3.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell3.Weight = 16.853286486916975;
			this.xrTableCell4.BackColor = Color.FromArgb(2, 119, 189);
			this.xrTableCell4.BorderColor = Color.White;
			this.xrTableCell4.Borders = BorderSide.All;
			this.xrTableCell4.Dpi = 254f;
			this.xrTableCell4.Font = new Font("Arial", 9.75f, FontStyle.Bold);
			this.xrTableCell4.ForeColor = Color.White;
			this.xrTableCell4.Multiline = true;
			this.xrTableCell4.Name = "xrTableCell4";
			this.xrTableCell4.StylePriority.UseBackColor = false;
			this.xrTableCell4.StylePriority.UseBorderColor = false;
			this.xrTableCell4.StylePriority.UseBorders = false;
			this.xrTableCell4.StylePriority.UseFont = false;
			this.xrTableCell4.StylePriority.UseForeColor = false;
			this.xrTableCell4.StylePriority.UseTextAlignment = false;
			this.xrTableCell4.Text = "Montant total des paiements en espèces";
			this.xrTableCell4.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell4.Weight = 16.853286486916975;
			this.objectDataSource1.DataSource = typeof(PersonalInfoSummaryModel);
			this.objectDataSource1.Name = "objectDataSource1";
			base.Bands.AddRange(new Band[]
			{
				this.TopMargin,
				this.BottomMargin,
				this.Detail
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource1
			});
			base.DataSource = this.objectDataSource1;
			this.Dpi = 254f;
			this.Font = new Font("Arial", 9.75f);
			base.Margins = new Margins(64, 64, 0, 0);
			base.PageHeight = 2970;
			base.PageWidth = 2100;
			base.PaperKind = DevExpress.Drawing.Printing.DXPaperKind.A4;
			base.ReportUnit = ReportUnit.TenthsOfAMillimeter;
			this.RightToLeft = RightToLeft.Yes;
			base.RightToLeftLayout = RightToLeftLayout.Yes;
			base.SnapGridSize = 25f;
			base.Version = "20.1";
			((ISupportInitialize)this.xrTable2).EndInit();
			((ISupportInitialize)this.objectDataSource1).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x04002446 RID: 9286
		private IContainer components = null;

		// Token: 0x04002447 RID: 9287
		private TopMarginBand TopMargin;

		// Token: 0x04002448 RID: 9288
		private BottomMarginBand BottomMargin;

		// Token: 0x04002449 RID: 9289
		private DetailBand Detail;

		// Token: 0x0400244A RID: 9290
		public ObjectDataSource objectDataSource1;

		// Token: 0x0400244B RID: 9291
		private XRTable xrTable2;

		// Token: 0x0400244C RID: 9292
		private XRTableRow xrTableRow7;

		// Token: 0x0400244D RID: 9293
		private XRTableCell xrTableCell14;

		// Token: 0x0400244E RID: 9294
		private XRTableRow xrTableRow8;

		// Token: 0x0400244F RID: 9295
		private XRTableCell xrTableCell16;

		// Token: 0x04002450 RID: 9296
		private XRTableRow xrTableRow9;

		// Token: 0x04002451 RID: 9297
		private XRTableCell xrTableCell18;

		// Token: 0x04002452 RID: 9298
		private XRTableRow xrTableRow10;

		// Token: 0x04002453 RID: 9299
		private XRTableCell xrTableCell20;

		// Token: 0x04002454 RID: 9300
		private XRTableRow xrTableRow11;

		// Token: 0x04002455 RID: 9301
		private XRTableCell xrTableCell22;

		// Token: 0x04002456 RID: 9302
		private XRTableRow xrTableRow12;

		// Token: 0x04002457 RID: 9303
		private XRTableCell xrTableCell24;

		// Token: 0x04002458 RID: 9304
		private XRTableRow xrTableRow13;

		// Token: 0x04002459 RID: 9305
		private XRTableCell xrTableCell13;

		// Token: 0x0400245A RID: 9306
		private XRTableCell xrTableCell23;

		// Token: 0x0400245B RID: 9307
		private XRTableRow xrTableRow14;

		// Token: 0x0400245C RID: 9308
		private XRTableCell xrTableCell25;

		// Token: 0x0400245D RID: 9309
		private XRTableCell xrTableCell26;

		// Token: 0x0400245E RID: 9310
		private XRLabel xrLabel1;

		// Token: 0x0400245F RID: 9311
		private XRLabel xrLabel2;

		// Token: 0x04002460 RID: 9312
		private XRLabel xrLabel3;

		// Token: 0x04002461 RID: 9313
		private XRLabel xrLabel4;

		// Token: 0x04002462 RID: 9314
		private XRLabel xrLabel5;

		// Token: 0x04002463 RID: 9315
		private XRLabel xrLabel6;

		// Token: 0x04002464 RID: 9316
		private XRLabel xrLabel7;

		// Token: 0x04002465 RID: 9317
		private XRLabel xrLabel8;

		// Token: 0x04002466 RID: 9318
		private XRLabel xrLabel9;

		// Token: 0x04002467 RID: 9319
		private XRLabel xrLabel10;

		// Token: 0x04002468 RID: 9320
		private XRLabel xrLabel11;

		// Token: 0x04002469 RID: 9321
		private XRLabel xrLabel12;

		// Token: 0x0400246A RID: 9322
		private XRTableCell xrTableCell1;

		// Token: 0x0400246B RID: 9323
		private XRTableCell xrTableCell2;

		// Token: 0x0400246C RID: 9324
		private XRTableCell xrTableCell3;

		// Token: 0x0400246D RID: 9325
		private XRTableCell xrTableCell4;
	}
}
