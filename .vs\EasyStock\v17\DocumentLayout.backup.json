{"Version": 1, "WorkspaceRootPath": "F:\\Hass\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DF493385-AF09-4DF2-8E13-783CA1A2ED49}|EasyStock\\EasyStock.csproj|f:\\hass\\easystock\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DF493385-AF09-4DF2-8E13-783CA1A2ED49}|EasyStock\\EasyStock.csproj|solutionrelative:easystock\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DF493385-AF09-4DF2-8E13-783CA1A2ED49}|EasyStock\\EasyStock.csproj|f:\\hass\\easystock\\reportviews\\productstoretransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DF493385-AF09-4DF2-8E13-783CA1A2ED49}|EasyStock\\EasyStock.csproj|solutionrelative:easystock\\reportviews\\productstoretransaction.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 254, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "F:\\Hass\\EasyStock\\Program.cs", "RelativeDocumentMoniker": "EasyStock\\Program.cs", "ToolTip": "F:\\Hass\\EasyStock\\Program.cs", "RelativeToolTip": "EasyStock\\Program.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAAABoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T22:33:06.368Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProductStoreTransaction.cs", "DocumentMoniker": "F:\\Hass\\EasyStock\\ReportViews\\ProductStoreTransaction.cs", "RelativeDocumentMoniker": "EasyStock\\ReportViews\\ProductStoreTransaction.cs", "ToolTip": "F:\\Hass\\EasyStock\\ReportViews\\ProductStoreTransaction.cs", "RelativeToolTip": "EasyStock\\ReportViews\\ProductStoreTransaction.cs", "ViewState": "AgIAAFUBAAAAAAAAAAAswFoBAABMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T02:50:12.736Z", "EditorCaption": ""}]}]}]}