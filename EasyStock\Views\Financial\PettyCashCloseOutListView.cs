﻿using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.Properties;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Linq;

namespace EasyStock.Views.Financial
{
    public partial class PettyCashCloseOutListView : MasterForm
    {
        public static PettyCashCloseOutListView Instance
        {
            get
            {
                bool flag = PettyCashCloseOutListView.instance == null || PettyCashCloseOutListView.instance.IsDisposed;
                if (flag)
                {
                    PettyCashCloseOutListView.instance = new PettyCashCloseOutListView();
                }
                return PettyCashCloseOutListView.instance;
            }
        }

        public PettyCashCloseOutListView()
        {
            this.InitializeComponent();
            this.btn_Save.Visibility = BarItemVisibility.Never;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.FocusRectStyle = DrawFocusRectStyle.RowFocus;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.SetAlternatingColors();
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
        }

        private void GridView1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            bool flag = e.HitInfo.InRow || e.HitInfo.InRowCell;
            if (flag)
            {
                DXMenuItem dxButtonPrint = new DXMenuItem
                {
                    Caption = "Imprimer"
                };
                dxButtonPrint.Click += this.DxButtonPrint_Click;
                e.Menu.Items.Add(dxButtonPrint);
                DXMenuItem dxButtonDelete = new DXMenuItem
                {
                    Caption = "Supprimer"
                };
                dxButtonDelete.Click += this.DxButtonDelete_Click;
                e.Menu.Items.Add(dxButtonDelete);
            }
        }

        private void DxButtonDelete_Click(object sender, EventArgs e)
        {
            this.btn_Delete.PerformClick();
        }

        public override void Delete()
        {
            if (gridView1.SelectedRowsCount != 0 && (CurrentSession.CurrentUser.Type != UserType.User || CurrentSession.CurrentUser.AccessProfile.GetWindowProfile("PettyCashView").CheckAction(WindowActions.Delete)))
            {
                if (gridView1.GetFocusedRowCellValue("ID") is int id)
                {
                    PettyCashCloseOutView.Delete(id);
                }
                RefreshData();
            }
        }
        private void DxButtonPrint_Click(object sender, EventArgs e)
        {
            bool flag = CurrentSession.CurrentUser.Type == UserType.User && !CurrentSession.CurrentUser.AccessProfile.GetWindowProfile("PettyCashView").CheckAction(WindowActions.Print);
            if (!flag)
            {
                int[] handles = this.gridView1.GetSelectedRows();
                List<int> ids = new List<int>();
                foreach (int handel in handles)
                {
                    ids.Add(Convert.ToInt32(this.gridView1.GetRowCellValue(handel, "ID")));
                }
                bool flag2 = ids.Count == 0;
                if (flag2)
                {
                    XtraMessageBox.Show("Veuillez sélectionner au moins une caisse");
                }
            }
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            bool flag = info.InRow || info.InRowCell;
            if (flag)
            {
                this.SelectedBOM = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
                bool flag2 = !this.IsSelectionMode;
                if (flag2)
                {
                    this.OpenForm(this.SelectedBOM);
                }
                else
                {
                    base.Close();
                }
            }
        }

        public virtual void OpenForm(int id)
        {
            HomeForm.OpenForm(PettyCashCloseOutView.Instance, false, false, false);
            PettyCashCloseOutView.Instance.GoTo(id);
        }

        public override void New()
        {
            HomeForm.OpenForm(PettyCashCloseOutView.Instance, false, false, false);
            PettyCashCloseOutView.Instance.btn_New.PerformClick();
        }

        public override void Print()
        {
            GridReportP.Print(this.gridControl1, this.Text, "");
            base.Print();
        }

        public override void RefreshData()
        {
            ERPDataContext context = new ERPDataContext();
            try
            {
                List<PettyCashCloseOut> q1 = (from i in context.PettyCashCloses.AsNoTracking()
                                              select (i)).ToList();
                List<PettyCashCloseOutReportModel> q2 = (from i in q1
                                                         join P in context.PettyCashes on i.PettyCashID equals P.ID
                                                         join H in context.PettyCashHolders on P.HolderID equals H.ID
                                                         from b in context.Banks.Where((Bank x) => x.ID == i.CloseAccountID).DefaultIfEmpty()
                                                         from a in context.Accounts.Where((Account x) => x.ID == i.CloseAccountID).DefaultIfEmpty()
                                                         from d in context.Drawers.Where((Drawer x) => x.ID == i.CloseAccountID).DefaultIfEmpty()
                                                         select new PettyCashCloseOutReportModel
                                                         {
                                                             ID = i.ID,
                                                             CloseDate = i.DateClosed,
                                                             CloseType = i.CloseAccountType,
                                                             PettycashName = P.ID + " | " + H.Name,
                                                             AmountSpent = i.AmountSpent,
                                                             Amount = i.Amount,
                                                             AccountName = ((i.CloseAccountType == CloseAccountType.Account) ? a.Name : ((i.CloseAccountType == CloseAccountType.Bank) ? b.Name : d.Name))
                                                         }).ToList();
                gridControl1.DataSource = q2;
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
            base.RefreshData();
        }
        private static PettyCashCloseOutListView instance;

        public bool IsSelectionMode;

        private int SelectedBOM;
    }
}
