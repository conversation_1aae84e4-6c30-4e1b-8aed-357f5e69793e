﻿using EasyStock.Classes;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("BillingDetails")]
    public abstract class BillingDetail
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Required(ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        [Display(Name = "Nom du compte")]
        public virtual string Name { get; set; }

        [NotMapped]
        public abstract string DisplayName { get; }

        [Display(Name = "Compte")]
        public virtual Account Account { get; set; }

        [Display(Name = "Numéro de compte")]
        public virtual int AccountID { get; set; }

        [Display(Name = "Succursales")]
        public int? BranchID { get; set; }

        [Display(Name = "Notes")]
        public string Notes { get; set; }
    }
}
