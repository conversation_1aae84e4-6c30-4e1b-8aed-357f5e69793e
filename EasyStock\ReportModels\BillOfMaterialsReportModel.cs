﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class BillOfMaterialsReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Numéro", GroupName = "Données de la liste")]
        public int ID { get; set; }

        [Display(Name = "Nom", GroupName = "Données de la liste")]
        public string Name { get; set; }

        [Display(Name = "Produit", GroupName = "Données de la liste")]
        public string ProductName { get; set; }

        [Display(Name = "Unité", GroupName = "Données de la liste")]
        public string UnitName { get; set; }

        [Display(Name = "Notes", GroupName = "Données de la liste")]
        public string Notes { get; set; }

        [Display(Name = "Quantité", GroupName = "Données de la liste")]
        public double Quantity { get; set; }

        [Display(Name = "Articles")]
        public ICollection<BOMDEtails> Details { get; set; }
    }
}
