﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Caisses")]
    [Table("Drawers")]
    public class Drawer : BillingDetail
    {
        [Display(Name = "Nom de la caisse")]
        public override string Name
        {
            get
            {
                return base.Name;
            }
            set
            {
                base.Name = value;
            }
        }

        public override string DisplayName
        {
            get
            {
                return this.Name;
            }
        }
    }
}
