﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity.Migrations;
using System.Linq;

namespace EasyStock.Views.Financial
{
    public partial class PettyCashView : MasterForm
    {
        public static PettyCashView Instance
        {
            get
            {
                bool flag = PettyCashView.instance == null || PettyCashView.instance.IsDisposed;
                if (flag)
                {
                    PettyCashView.instance = new PettyCashView();
                }
                return PettyCashView.instance;
            }
        }

        public PettyCash Petty
        {
            get
            {
                return this.pettyCashBindingSource.Current as PettyCash;
            }
            set
            {
                this.pettyCashBindingSource.DataSource = value;
            }
        }

        public PettyCashView()
        {
            this.InitializeComponent();
            this.db = new ERPDataContext();
            base.DisableValidation(this.dataLayoutControl1);
            this.db = new ERPDataContext();
            base.Shown += this.GroupView_Shown;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
        }

        private void GroupView_Shown(object sender, EventArgs e)
        {
            bool flag = this.Petty == null;
            if (flag)
            {
                this.New();
            }
        }

        public void GoTo(int id)
        {
            PettyCash sourceDepr = this.db.PettyCashes.SingleOrDefault((PettyCash x) => x.ID == id);
            bool flag = sourceDepr != null;
            if (flag)
            {
                this.Petty = sourceDepr;
                this.DataChanged = false;
                base.IsNew = false;
            }
            else
            {
                Vip.Notification.Alert.ShowError("Le document est introuvable");
            }
        }

        public override void New()
        {
            PettyCash pettyCash = new PettyCash();
            Branch defaultBranch = CurrentSession.DefaultBranch;
            pettyCash.BranchID = ((defaultBranch != null) ? defaultBranch.ID : 0);
            pettyCash.Date = DateTime.Now;
            pettyCash.CurrencyID = 1;
            this.Petty = pettyCash;
            base.New();
        }

        public override void Save()
        {
            base.EnableValidation(this.dataLayoutControl1);
            bool flag = !this.ValidateChildren();
            if (!flag)
            {
                base.DisableValidation(this.dataLayoutControl1);
                this.db.PettyCashes.AddOrUpdate(new PettyCash[]
                {
                    this.Petty
                });
                this.db.SaveChanges();
                base.Save();
                this.RefreshData();
            }
        }

        public override void RefreshData()
        {
            this.BranchIDLookUpEdit.BindToDataSource((from x in this.db.Branches
                                                      select new
                                                      {
                                                          x.ID,
                                                          x.Name
                                                      }).ToList(), "ID", "Name");
            this.HolderIDLookUpEdit.BindToDataSource((from x in this.db.PettyCashHolders
                                                      where x.Disable == false
                                                      select new
                                                      {
                                                          x.ID,
                                                          x.Name
                                                      }).ToList(), "ID", "Name");
            this.CostCenterLookUpEdit.BindToDataSource((from x in this.db.CostCenters
                                                        select new
                                                        {
                                                            x.ID,
                                                            x.Name
                                                        }).ToList(), "ID", "Name");
            this.CurrencyIDLookUpEdit.BindToDataSource((from x in this.db.Currencies
                                                        select new
                                                        {
                                                            x.ID,
                                                            x.Name
                                                        }).ToList(), "ID", "Name");
            base.RefreshData();
        }

        public override void Delete()
        {
        }

        public static List<PettyCashReportModel> GetPrintDataSource(int Id)
        {
            return PettyCashView.GetPrintDataSource(new int[]
            {
                Id
            });
        }

        public static List<PettyCashReportModel> GetPrintDataSource(IList<int> Ids)
        {
            ERPDataContext context = new ERPDataContext();
            try
            {
                List<PettyCash> q1 = (from i in context.PettyCashes.AsNoTracking()
                                      where Ids.Contains(i.ID)
                                      select i).ToList();
                return (from i in q1
                        join Br in context.Branches on i.BranchID equals Br.ID
                        join H in context.PettyCashHolders on i.HolderID equals H.ID
                        join cr in context.Currencies on i.CurrencyID equals cr.ID
                        from CS in context.CostCenters.Where((CostCenter x) => (int?)x.ID == i.CostCenter).DefaultIfEmpty()
                        select new PettyCashReportModel
                        {
                            Notes = i.Notes,
                            ID = i.ID,
                            Date = i.Date,
                            BranchName = Br.Name,
                            Type = i.Type,
                            HolderName = H.Name,
                            IsClosed = i.IsClosed,
                            Amount = i.Amount,
                            CostCenter = CS?.Name,
                            Currency = cr.Name
                        }).ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
        public override void Print()
        {
            PettyCash petty = this.Petty;
            bool flag = petty == null || petty.ID == 0;
            if (!flag)
            {
                PettyCashReport.Print(PettyCashView.GetPrintDataSource(petty.ID));
                base.Print();
            }
        }

        public static void Print(IList<int> Ids)
        {
            PettyCashReport.Print(PettyCashView.GetPrintDataSource(Ids));
        }

        private static PettyCashView instance;

        private ERPDataContext db;
    }
}
