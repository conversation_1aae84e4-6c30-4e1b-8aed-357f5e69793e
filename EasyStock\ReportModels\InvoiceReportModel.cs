﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class InvoiceReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Nom du dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Code du dépôt")]
        public int StoreID { get; set; }

        [Display(Name = "Numéro", GroupName = "Détails de la facture")]
        public int ID { get; set; }

        [Display(Name = "Code", GroupName = "Détails de la facture")]
        public string Code { get; set; }

        [Display(Name = "Date", GroupName = "Détails de la facture")]
        public DateTime Date { get; set; }

        [Display(Name = "Notes", GroupName = "Détails de la facture")]
        public string Notes { get; set; }

        [Display(Name = "Utilisateur", GroupName = "Détails de la facture")]
        public string UserID { get; set; }

        [Display(Name = "Total des articles", GroupName = "Valeur")]
        public double Total { get; set; }

        [Display(Name = "Articles")]
        public ICollection<InvoiceDetailReportModel> Details { get; set; }

        [Display(Name = "Remise", GroupName = "Valeur")]
        public double Discount { get; set; }

        [Display(Name = "Remise %")]
        public double DiscountPercentage
        {
            get
            {
                return this.Discount / this.Total;
            }
        }

        [Display(Name = "Total TTC", GroupName = "Valeur")]
        public double Net { get; set; }

        [Display(Name = "Autres dépenses", GroupName = "Valeur")]
        public double OtherExpenses { get; set; }

        [Display(Name = "Payé", GroupName = "Règlement")]
        public double Paid { get; set; }

        [Display(Name = "Détails des paiements", GroupName = "Règlement")]
        public ICollection<PayDetailReportModel> PayDetails { get; set; }

        [Display(Name = "Restant", GroupName = "Règlement")]
        public double Remaining
        {
            get
            {
                return this.Net - this.Paid;
            }
        }

        [Display(Name = "M.Taxe", GroupName = "Valeur")]
        public double Tax { get; set; }

        [Display(Name = "Net en texte")]
        public string NetText { get; set; }

        [Display(Name = "Date d'échéance", GroupName = "Détails de la facture")]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Mode de paiement")]
        public MethodOfPayment MethodOfPayment
        {
            get
            {
                return (this.Remaining == 0.0) ? MethodOfPayment.Cash : ((this.Remaining == this.Net) ? MethodOfPayment.Credit : MethodOfPayment.CashAndCredit);
            }
        }

        [Display(Name = "Solde actuel", GroupName = "Valeur")]
        public double CurrentBalance { get; set; }

        [Display(Name = "Solde précédent", GroupName = "Valeur")]
        public double BeforeBalance
        {
            get
            {
                return this.CurrentBalance - this.Net;
            }
        }
    }
}
