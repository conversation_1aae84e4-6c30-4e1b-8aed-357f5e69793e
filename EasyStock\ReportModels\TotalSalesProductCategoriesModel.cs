﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class TotalSalesProductCategoriesModel : CompanyInfoReportModel
    {
        [Display(Name = "Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Nombre de factures")]
        public int InvoiceCount { get; set; }

        [Display(Name = "Quantité")]
        public double Qty { get; set; }

        [Display(Name = "Total")]
        public double TotalAmount { get; set; }
    }
}
