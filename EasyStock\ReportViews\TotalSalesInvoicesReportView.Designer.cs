﻿namespace EasyStock.ReportViews
{
	// Token: 0x0200034F RID: 847
	public partial class TotalSalesInvoicesReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x0600143C RID: 5180 RVA: 0x00169D9C File Offset: 0x00167F9C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600143D RID: 5181 RVA: 0x00169DD4 File Offset: 0x00167FD4
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 59);
            this.documentViewer1.Size = new System.Drawing.Size(806, 370);
            // 
            // TotalSalesInvoicesReportView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(806, 453);
            this.Name = "TotalSalesInvoicesReportView";
            this.Text = "Total des factures de vente";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040019C4 RID: 6596
		private global::System.ComponentModel.IContainer components = null;
	}
}
