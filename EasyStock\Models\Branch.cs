﻿using EasyStock.Classes;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Les succursales")]
    public class Branch : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Code")]
        [ReadOnly(true)]
        public int ID { get; set; }

        [Display(Name = "Nom")]
        [Required(ErrorMessageResourceName = "ErrorCantBeEmpry", ErrorMessageResourceType = typeof(LangResource))]
        [StringLength(250)]
        public string Name { get; set; }

        [Display(Name = "Téléphone")]
        [StringLength(50)]
        public string Phone { get; set; }

        [Display(Name = "Ville")]
        [StringLength(150)]
        public string City { get; set; }

        [Display(Name = "Adresse")]
        [StringLength(250)]
        public string Address { get; set; }
    }
}

