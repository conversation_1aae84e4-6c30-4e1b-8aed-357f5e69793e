﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class Details : Consumptions
    {
        [Display(Name = "Nom de la liste des matières")]
        public string BOMName { get; set; }

        [Display(Name = "Nom du produit")]
        public string BOMProductName { get; set; }

        [Display(Name = "Nom de l'unité")]
        public string BOMUnitName { get; set; }

        [Display(Name = "Quantité requise")]
        [Range(0.001, 1.7976931348623157E+308, ErrorMessage = "La quantité doit être supérieure à 0.001.")]
        public double BOMOrderQuantity { get; set; }

        [Display(Name = "Quantité réelle")]
        public double BOMAcualQuantity { get; set; }

        [Display(Name = "Coût par défaut")]
        public double BOMTotalDefaultCost { get; set; }

        [Display(Name = "Coût réel")]
        public double BOMTotalActualCost { get; set; }
    }
}
