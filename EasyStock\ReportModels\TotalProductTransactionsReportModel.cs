﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class TotalProductTransactionsReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Article")]
        public string Product { get; set; }

        [Display(Name = "Quantité de solde initial")]
        public double OpeningQty { get; set; }

        [Display(Name = "Quantité d'achats")]
        public double PurchseQty { get; set; }

        [Display(Name = "Quantité de transfert entrant")]
        public double ReceiptTransferQty { get; set; }

        [Display(Name = "Quantité de retour de vente")]
        public double SalesReturnQty { get; set; }

        [Display(Name = "Quantité de ventes")]
        public double SalesQty { get; set; }

        [Display(Name = "Quantité de retour d'achats")]
        public double PurchaseRetrnQty { get; set; }

        [Display(Name = "Quantité de transfert sortant")]
        public double IssueTransferQty { get; set; }

        [Display(Name = "Quantité endommagée")]
        public double ExpiredQty { get; set; }

        [Display(Name = "Quantité de solde final")]
        public double ClosingQty
        {
            get
            {
                return this.OpeningQty + this.PurchseQty + this.ReceiptTransferQty + this.SalesReturnQty - this.SalesQty - this.PurchaseRetrnQty - this.IssueTransferQty - this.ExpiredQty;
            }
        }
    }
}
