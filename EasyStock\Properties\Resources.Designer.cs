﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace EasyStock.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("EasyStock.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap _3d_glass_window_logo_mockup {
            get {
                object obj = ResourceManager.GetObject("3d glass window logo mockup", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage _new {
            get {
                object obj = ResourceManager.GetObject("new", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage _private {
            get {
                object obj = ResourceManager.GetObject("private", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage actions_add {
            get {
                object obj = ResourceManager.GetObject("actions_add", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage actions_edit {
            get {
                object obj = ResourceManager.GetObject("actions_edit", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage actions_remove {
            get {
                object obj = ResourceManager.GetObject("actions_remove", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage actions_zoom {
            get {
                object obj = ResourceManager.GetObject("actions_zoom", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage actions_zoom1 {
            get {
                object obj = ResourceManager.GetObject("actions_zoom1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage actions_zoom2 {
            get {
                object obj = ResourceManager.GetObject("actions_zoom2", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage bo_address {
            get {
                object obj = ResourceManager.GetObject("bo_address", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage bo_address1 {
            get {
                object obj = ResourceManager.GetObject("bo_address1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage bo_product_group {
            get {
                object obj = ResourceManager.GetObject("bo_product_group", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage business_calculator {
            get {
                object obj = ResourceManager.GetObject("business_calculator", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage checkbox {
            get {
                object obj = ResourceManager.GetObject("checkbox", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage clearheaderandfooter {
            get {
                object obj = ResourceManager.GetObject("clearheaderandfooter", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage compressweekend {
            get {
                object obj = ResourceManager.GetObject("compressweekend", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage convertto {
            get {
                object obj = ResourceManager.GetObject("convertto", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage copy {
            get {
                object obj = ResourceManager.GetObject("copy", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage delete {
            get {
                object obj = ResourceManager.GetObject("delete", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage duplicatevalues {
            get {
                object obj = ResourceManager.GetObject("duplicatevalues", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage duplicatevalues1 {
            get {
                object obj = ResourceManager.GetObject("duplicatevalues1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage editwrappoints {
            get {
                object obj = ResourceManager.GetObject("editwrappoints", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage electronics_desktopmac {
            get {
                object obj = ResourceManager.GetObject("electronics_desktopmac", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage electronics_keyboard {
            get {
                object obj = ResourceManager.GetObject("electronics_keyboard", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage enablesearch {
            get {
                object obj = ResourceManager.GetObject("enablesearch", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage exportas {
            get {
                object obj = ResourceManager.GetObject("exportas", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage exporttoxls {
            get {
                object obj = ResourceManager.GetObject("exporttoxls", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage filterquery {
            get {
                object obj = ResourceManager.GetObject("filterquery", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage fittopage {
            get {
                object obj = ResourceManager.GetObject("fittopage", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage fontsize {
            get {
                object obj = ResourceManager.GetObject("fontsize", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap hide_16x16 {
            get {
                object obj = ResourceManager.GetObject("hide_16x16", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage import {
            get {
                object obj = ResourceManager.GetObject("import", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap lmc {
            get {
                object obj = ResourceManager.GetObject("lmc", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage managedatasource {
            get {
                object obj = ResourceManager.GetObject("managedatasource", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage open2 {
            get {
                object obj = ResourceManager.GetObject("open2", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage preview {
            get {
                object obj = ResourceManager.GetObject("preview", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage print {
            get {
                object obj = ResourceManager.GetObject("print", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage printcollated {
            get {
                object obj = ResourceManager.GetObject("printcollated", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage printlayoutview {
            get {
                object obj = ResourceManager.GetObject("printlayoutview", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage refreshallpivottable {
            get {
                object obj = ResourceManager.GetObject("refreshallpivottable", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage replace {
            get {
                object obj = ResourceManager.GetObject("replace", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage sales {
            get {
                object obj = ResourceManager.GetObject("sales", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage save {
            get {
                object obj = ResourceManager.GetObject("save", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage save1 {
            get {
                object obj = ResourceManager.GetObject("save1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage saveas {
            get {
                object obj = ResourceManager.GetObject("saveas", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage security_unlock {
            get {
                object obj = ResourceManager.GetObject("security_unlock", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage select {
            get {
                object obj = ResourceManager.GetObject("select", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage selectdatamember {
            get {
                object obj = ResourceManager.GetObject("selectdatamember", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage selecttool_pantool {
            get {
                object obj = ResourceManager.GetObject("selecttool_pantool", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap show_16x16 {
            get {
                object obj = ResourceManager.GetObject("show_16x16", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage showcontainerheader {
            get {
                object obj = ResourceManager.GetObject("showcontainerheader", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage tableconverttorange {
            get {
                object obj = ResourceManager.GetObject("tableconverttorange", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage time {
            get {
                object obj = ResourceManager.GetObject("time", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap touchmode_16x16 {
            get {
                object obj = ResourceManager.GetObject("touchmode_16x16", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap touchmode_32x32 {
            get {
                object obj = ResourceManager.GetObject("touchmode_32x32", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Untitled_2AAAAAAhaaaaaaay {
            get {
                object obj = ResourceManager.GetObject("Untitled-2AAAAAAhaaaaaaay", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage update {
            get {
                object obj = ResourceManager.GetObject("update", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage verticallines {
            get {
                object obj = ResourceManager.GetObject("verticallines", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        internal static DevExpress.Utils.Svg.SvgImage viewsettings {
            get {
                object obj = ResourceManager.GetObject("viewsettings", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
    }
}
