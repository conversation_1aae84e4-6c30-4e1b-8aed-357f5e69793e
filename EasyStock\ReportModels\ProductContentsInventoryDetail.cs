﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class ProductContentsInventoryDetail
    {
        [Display(Name = "N°")]
        public int Number { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Quantité")]
        public double Quantity { get; set; }

        [Display(Name = "Opération")]
        public string Action { get; set; }

        [Display(Name = "Partie Impliquée")]
        public string UserName { get; set; }
    }
}
