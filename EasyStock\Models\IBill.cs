﻿using System;
using System.Collections.Generic;

namespace EasyStock.Models
{
	public interface IBill
	{
		int ID { get; set; }

		int BranchID { get; set; }

		DateTime Date { get; set; }

		IEnumerable<IProductRowDetail> BaseDetails { get; }

		double Discount { get; set; }

		double DiscountPercentage { get; }

		double Net { get; }

		double OtherExpenses { get; set; }

		double Tax { get; set; }

		double Total { get; set; }

		BillState State { get; set; }
	}
}
