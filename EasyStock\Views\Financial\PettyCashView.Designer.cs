﻿namespace EasyStock.Views.Financial
{
	// Token: 0x020002E4 RID: 740
	public partial class PettyCashView : global::EasyStock.MainViews.MasterForm
	{
		// Token: 0x06001298 RID: 4760 RVA: 0x0014480C File Offset: 0x00142A0C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001299 RID: 4761 RVA: 0x00144844 File Offset: 0x00142A44
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.pettyCashBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.BranchIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.branchBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.TypeImageComboBoxEdit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.HolderIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.pettyCashHolderBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.CostCenterLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.AmountTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.CurrencyIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.IsClosedCheckEdit = new DevExpress.XtraEditors.CheckEdit();
            this.NotesMemoEdit = new DevExpress.XtraEditors.MemoEdit();
            this.DateDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.layoutControlGroup3 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.layoutControlGroup2 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForBranchID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForType = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForHolderID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCostCenter = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForAmount = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForCurrencyID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForNotes = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDate = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForIsClosed = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.branchBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TypeImageComboBoxEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.HolderIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashHolderBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CostCenterLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.AmountTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.CurrencyIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IsClosedCheckEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesMemoEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranchID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForType)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForHolderID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCostCenter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAmount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCurrencyID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForIsClosed)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BranchIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.TypeImageComboBoxEdit);
            this.dataLayoutControl1.Controls.Add(this.HolderIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.CostCenterLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.AmountTextEdit);
            this.dataLayoutControl1.Controls.Add(this.CurrencyIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.IsClosedCheckEdit);
            this.dataLayoutControl1.Controls.Add(this.NotesMemoEdit);
            this.dataLayoutControl1.Controls.Add(this.DateDateEdit);
            this.dataLayoutControl1.DataSource = this.pettyCashBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.HiddenItems.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup3});
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 26);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(878, 420);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(124, 44);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "N0");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(189, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // pettyCashBindingSource
            // 
            this.pettyCashBindingSource.DataSource = typeof(EasyStock.Models.PettyCash);
            // 
            // BranchIDLookUpEdit
            // 
            this.BranchIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "BranchID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BranchIDLookUpEdit.Location = new System.Drawing.Point(124, 67);
            this.BranchIDLookUpEdit.Name = "BranchIDLookUpEdit";
            this.BranchIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.BranchIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BranchIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BranchIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BranchIDLookUpEdit.Properties.DataSource = this.branchBindingSource;
            this.BranchIDLookUpEdit.Properties.DisplayMember = "Name";
            this.BranchIDLookUpEdit.Properties.NullText = "";
            this.BranchIDLookUpEdit.Properties.ValueMember = "ID";
            this.BranchIDLookUpEdit.Size = new System.Drawing.Size(273, 20);
            this.BranchIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.BranchIDLookUpEdit.TabIndex = 5;
            // 
            // branchBindingSource
            // 
            this.branchBindingSource.DataSource = typeof(EasyStock.Models.Branch);
            // 
            // TypeImageComboBoxEdit
            // 
            this.TypeImageComboBoxEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "Type", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.TypeImageComboBoxEdit.Location = new System.Drawing.Point(124, 89);
            this.TypeImageComboBoxEdit.Name = "TypeImageComboBoxEdit";
            this.TypeImageComboBoxEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.TypeImageComboBoxEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.TypeImageComboBoxEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.TypeImageComboBoxEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.TypeImageComboBoxEdit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Permanent", EasyStock.Models.PettyCash.PettyCashType.Permanent, 0),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem("Temporaire", EasyStock.Models.PettyCash.PettyCashType.Temporary, 1)});
            this.TypeImageComboBoxEdit.Properties.UseCtrlScroll = true;
            this.TypeImageComboBoxEdit.Size = new System.Drawing.Size(273, 20);
            this.TypeImageComboBoxEdit.StyleController = this.dataLayoutControl1;
            this.TypeImageComboBoxEdit.TabIndex = 6;
            // 
            // HolderIDLookUpEdit
            // 
            this.HolderIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "HolderID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.HolderIDLookUpEdit.Location = new System.Drawing.Point(124, 113);
            this.HolderIDLookUpEdit.Name = "HolderIDLookUpEdit";
            this.HolderIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.HolderIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.HolderIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.HolderIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.HolderIDLookUpEdit.Properties.DataSource = this.pettyCashHolderBindingSource;
            this.HolderIDLookUpEdit.Properties.DisplayMember = "Name";
            this.HolderIDLookUpEdit.Properties.NullText = "";
            this.HolderIDLookUpEdit.Properties.ValueMember = "ID";
            this.HolderIDLookUpEdit.Size = new System.Drawing.Size(273, 20);
            this.HolderIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.HolderIDLookUpEdit.TabIndex = 7;
            // 
            // pettyCashHolderBindingSource
            // 
            this.pettyCashHolderBindingSource.DataSource = typeof(EasyStock.Models.PettyCashHolder);
            // 
            // CostCenterLookUpEdit
            // 
            this.CostCenterLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "CostCenter", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CostCenterLookUpEdit.Location = new System.Drawing.Point(124, 161);
            this.CostCenterLookUpEdit.Name = "CostCenterLookUpEdit";
            this.CostCenterLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.CostCenterLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.CostCenterLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.CostCenterLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.CostCenterLookUpEdit.Properties.DisplayMember = "Name";
            this.CostCenterLookUpEdit.Properties.NullText = "";
            this.CostCenterLookUpEdit.Properties.ValueMember = "ID";
            this.CostCenterLookUpEdit.Size = new System.Drawing.Size(273, 20);
            this.CostCenterLookUpEdit.StyleController = this.dataLayoutControl1;
            this.CostCenterLookUpEdit.TabIndex = 9;
            // 
            // AmountTextEdit
            // 
            this.AmountTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "Amount", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.AmountTextEdit.Location = new System.Drawing.Point(124, 185);
            this.AmountTextEdit.Name = "AmountTextEdit";
            this.AmountTextEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.AmountTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.AmountTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.AmountTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.AmountTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.AmountTextEdit.Properties.MaskSettings.Set("mask", "F");
            this.AmountTextEdit.Size = new System.Drawing.Size(273, 20);
            this.AmountTextEdit.StyleController = this.dataLayoutControl1;
            this.AmountTextEdit.TabIndex = 10;
            // 
            // CurrencyIDLookUpEdit
            // 
            this.CurrencyIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "CurrencyID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.CurrencyIDLookUpEdit.Location = new System.Drawing.Point(124, 209);
            this.CurrencyIDLookUpEdit.Name = "CurrencyIDLookUpEdit";
            this.CurrencyIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.CurrencyIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.CurrencyIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.CurrencyIDLookUpEdit.Properties.DisplayMember = "Name";
            this.CurrencyIDLookUpEdit.Properties.NullText = "";
            this.CurrencyIDLookUpEdit.Properties.ValueMember = "ID";
            this.CurrencyIDLookUpEdit.Size = new System.Drawing.Size(273, 20);
            this.CurrencyIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.CurrencyIDLookUpEdit.TabIndex = 11;
            // 
            // IsClosedCheckEdit
            // 
            this.IsClosedCheckEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "IsClosed", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IsClosedCheckEdit.Location = new System.Drawing.Point(317, 44);
            this.IsClosedCheckEdit.Name = "IsClosedCheckEdit";
            this.IsClosedCheckEdit.Properties.Caption = "Clôturé";
            this.IsClosedCheckEdit.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Default;
            this.IsClosedCheckEdit.Properties.ReadOnly = true;
            this.IsClosedCheckEdit.Size = new System.Drawing.Size(80, 19);
            this.IsClosedCheckEdit.StyleController = this.dataLayoutControl1;
            this.IsClosedCheckEdit.TabIndex = 12;
            // 
            // NotesMemoEdit
            // 
            this.NotesMemoEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "Notes", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.NotesMemoEdit.Location = new System.Drawing.Point(124, 233);
            this.NotesMemoEdit.Name = "NotesMemoEdit";
            this.NotesMemoEdit.Size = new System.Drawing.Size(273, 163);
            this.NotesMemoEdit.StyleController = this.dataLayoutControl1;
            this.NotesMemoEdit.TabIndex = 15;
            // 
            // DateDateEdit
            // 
            this.DateDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.pettyCashBindingSource, "Date", true));
            this.DateDateEdit.EditValue = new System.DateTime(2021, 8, 17, 0, 0, 0, 0);
            this.DateDateEdit.Location = new System.Drawing.Point(124, 137);
            this.DateDateEdit.Name = "DateDateEdit";
            this.DateDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DateDateEdit.Properties.MaxValue = new System.DateTime(2021, 8, 17, 0, 0, 0, 0);
            this.DateDateEdit.Size = new System.Drawing.Size(273, 20);
            this.DateDateEdit.StyleController = this.dataLayoutControl1;
            this.DateDateEdit.TabIndex = 16;
            // 
            // layoutControlGroup3
            // 
            this.layoutControlGroup3.AppearanceGroup.BorderColor = System.Drawing.Color.DarkBlue;
            this.layoutControlGroup3.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup3.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup3.Name = "layoutControlGroup3";
            this.layoutControlGroup3.Size = new System.Drawing.Size(494, 404);
            this.layoutControlGroup3.Text = "Liste des fonds";
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(878, 420);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.emptySpaceItem1,
            this.emptySpaceItem2,
            this.layoutControlGroup2});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(858, 400);
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(848, 0);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(10, 400);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(401, 0);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(447, 400);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // layoutControlGroup2
            // 
            this.layoutControlGroup2.AppearanceGroup.BorderColor = System.Drawing.Color.Green;
            this.layoutControlGroup2.AppearanceGroup.Options.UseBorderColor = true;
            this.layoutControlGroup2.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForBranchID,
            this.ItemForID,
            this.ItemForType,
            this.ItemForHolderID,
            this.ItemForCostCenter,
            this.ItemForAmount,
            this.ItemForCurrencyID,
            this.ItemForNotes,
            this.ItemForDate,
            this.ItemForIsClosed});
            this.layoutControlGroup2.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup2.Name = "layoutControlGroup2";
            this.layoutControlGroup2.Size = new System.Drawing.Size(401, 400);
            this.layoutControlGroup2.Text = "Informations";
            // 
            // ItemForBranchID
            // 
            this.ItemForBranchID.Control = this.BranchIDLookUpEdit;
            this.ItemForBranchID.Location = new System.Drawing.Point(0, 23);
            this.ItemForBranchID.MaxSize = new System.Drawing.Size(377, 22);
            this.ItemForBranchID.MinSize = new System.Drawing.Size(377, 22);
            this.ItemForBranchID.Name = "ItemForBranchID";
            this.ItemForBranchID.Size = new System.Drawing.Size(377, 22);
            this.ItemForBranchID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForBranchID.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.MaxSize = new System.Drawing.Size(293, 22);
            this.ItemForID.MinSize = new System.Drawing.Size(293, 22);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(293, 23);
            this.ItemForID.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForID.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForType
            // 
            this.ItemForType.Control = this.TypeImageComboBoxEdit;
            this.ItemForType.Location = new System.Drawing.Point(0, 45);
            this.ItemForType.Name = "ItemForType";
            this.ItemForType.Size = new System.Drawing.Size(377, 24);
            this.ItemForType.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForHolderID
            // 
            this.ItemForHolderID.Control = this.HolderIDLookUpEdit;
            this.ItemForHolderID.Location = new System.Drawing.Point(0, 69);
            this.ItemForHolderID.Name = "ItemForHolderID";
            this.ItemForHolderID.Size = new System.Drawing.Size(377, 24);
            this.ItemForHolderID.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForCostCenter
            // 
            this.ItemForCostCenter.Control = this.CostCenterLookUpEdit;
            this.ItemForCostCenter.Location = new System.Drawing.Point(0, 117);
            this.ItemForCostCenter.Name = "ItemForCostCenter";
            this.ItemForCostCenter.Size = new System.Drawing.Size(377, 24);
            this.ItemForCostCenter.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForAmount
            // 
            this.ItemForAmount.Control = this.AmountTextEdit;
            this.ItemForAmount.Location = new System.Drawing.Point(0, 141);
            this.ItemForAmount.Name = "ItemForAmount";
            this.ItemForAmount.Size = new System.Drawing.Size(377, 24);
            this.ItemForAmount.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForCurrencyID
            // 
            this.ItemForCurrencyID.Control = this.CurrencyIDLookUpEdit;
            this.ItemForCurrencyID.Location = new System.Drawing.Point(0, 165);
            this.ItemForCurrencyID.Name = "ItemForCurrencyID";
            this.ItemForCurrencyID.Size = new System.Drawing.Size(377, 24);
            this.ItemForCurrencyID.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForNotes
            // 
            this.ItemForNotes.Control = this.NotesMemoEdit;
            this.ItemForNotes.Location = new System.Drawing.Point(0, 189);
            this.ItemForNotes.MaxSize = new System.Drawing.Size(377, 167);
            this.ItemForNotes.MinSize = new System.Drawing.Size(377, 167);
            this.ItemForNotes.Name = "ItemForNotes";
            this.ItemForNotes.Size = new System.Drawing.Size(377, 167);
            this.ItemForNotes.SizeConstraintsType = DevExpress.XtraLayout.SizeConstraintsType.Custom;
            this.ItemForNotes.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForDate
            // 
            this.ItemForDate.Control = this.DateDateEdit;
            this.ItemForDate.Location = new System.Drawing.Point(0, 93);
            this.ItemForDate.Name = "ItemForDate";
            this.ItemForDate.Size = new System.Drawing.Size(377, 24);
            this.ItemForDate.TextSize = new System.Drawing.Size(96, 13);
            // 
            // ItemForIsClosed
            // 
            this.ItemForIsClosed.Control = this.IsClosedCheckEdit;
            this.ItemForIsClosed.Location = new System.Drawing.Point(293, 0);
            this.ItemForIsClosed.Name = "ItemForIsClosed";
            this.ItemForIsClosed.Size = new System.Drawing.Size(84, 23);
            this.ItemForIsClosed.TextSize = new System.Drawing.Size(0, 0);
            this.ItemForIsClosed.TextVisible = false;
            // 
            // PettyCashView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(878, 470);
            this.Controls.Add(this.dataLayoutControl1);
            this.Name = "PettyCashView";
            this.Text = "L\'avance financière ( العهدة المالية )";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.branchBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TypeImageComboBoxEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.HolderIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashHolderBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CostCenterLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.AmountTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.CurrencyIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IsClosedCheckEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.NotesMemoEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DateDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranchID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForType)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForHolderID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCostCenter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForAmount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForCurrencyID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForNotes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForIsClosed)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04001883 RID: 6275
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x04001884 RID: 6276
		private global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x04001885 RID: 6277
		private global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x04001886 RID: 6278
		private global::System.Windows.Forms.BindingSource pettyCashBindingSource;

		// Token: 0x04001887 RID: 6279
		private global::System.Windows.Forms.BindingSource branchBindingSource;

		// Token: 0x04001888 RID: 6280
		private global::System.Windows.Forms.BindingSource pettyCashHolderBindingSource;

		// Token: 0x04001889 RID: 6281
		private global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x0400188A RID: 6282
		private global::DevExpress.XtraEditors.LookUpEdit BranchIDLookUpEdit;

		// Token: 0x0400188B RID: 6283
		private global::DevExpress.XtraEditors.ImageComboBoxEdit TypeImageComboBoxEdit;

		// Token: 0x0400188C RID: 6284
		private global::DevExpress.XtraEditors.LookUpEdit HolderIDLookUpEdit;

		// Token: 0x0400188D RID: 6285
		private global::DevExpress.XtraEditors.LookUpEdit CostCenterLookUpEdit;

		// Token: 0x0400188E RID: 6286
		private global::DevExpress.XtraEditors.TextEdit AmountTextEdit;

		// Token: 0x0400188F RID: 6287
		private global::DevExpress.XtraEditors.LookUpEdit CurrencyIDLookUpEdit;

		// Token: 0x04001890 RID: 6288
		private global::DevExpress.XtraEditors.CheckEdit IsClosedCheckEdit;

		// Token: 0x04001891 RID: 6289
		private global::DevExpress.XtraEditors.MemoEdit NotesMemoEdit;

		// Token: 0x04001892 RID: 6290
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x04001893 RID: 6291
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x04001894 RID: 6292
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup2;

		// Token: 0x04001895 RID: 6293
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBranchID;

		// Token: 0x04001896 RID: 6294
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x04001897 RID: 6295
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForType;

		// Token: 0x04001898 RID: 6296
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForHolderID;

		// Token: 0x04001899 RID: 6297
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCostCenter;

		// Token: 0x0400189A RID: 6298
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForAmount;

		// Token: 0x0400189B RID: 6299
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForCurrencyID;

		// Token: 0x0400189C RID: 6300
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForIsClosed;

		// Token: 0x0400189D RID: 6301
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForNotes;

		// Token: 0x0400189E RID: 6302
		private global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup3;

		// Token: 0x0400189F RID: 6303
		private global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x040018A0 RID: 6304
		private global::DevExpress.XtraEditors.DateEdit DateDateEdit;

		// Token: 0x040018A1 RID: 6305
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForDate;
	}
}
