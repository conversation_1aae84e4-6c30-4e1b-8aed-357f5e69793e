﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000337 RID: 823
	public partial class SalesProductCostView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x060013FE RID: 5118 RVA: 0x00163844 File Offset: 0x00161A44
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060013FF RID: 5119 RVA: 0x0016387C File Offset: 0x00161A7C
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 53);
            this.documentViewer1.Size = new System.Drawing.Size(800, 376);
            // 
            // SalesProductCostView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Name = "SalesProductCostView";
            this.Text = "Profit ou perte des articles";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040019A0 RID: 6560
		private global::System.ComponentModel.IContainer components = null;
	}
}
