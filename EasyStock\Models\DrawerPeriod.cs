﻿using EasyStock.Classes;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Périodes des caisses")]
    public class DrawerPeriod : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        [Display(Name = "Code", GroupName = "Données")]
        public int ID { get; set; }

        [Display(Name = "Date d'ouverture")]
        [Required(ErrorMessageResourceName = "ErrorCantBeEmpty", ErrorMessageResourceType = typeof(LangResource))]
        public DateTime PeriodStart
        {
            get
            {
                return this.periodStart;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.periodStart, value, "PeriodStart");
            }
        }

        [Display(Name = "Date de clôture")]
        public DateTime? PeriodEnd
        {
            get
            {
                return this.periodEnd;
            }
            set
            {
                base.SetProperty<DateTime?>(ref this.periodEnd, value, "PeriodEnd");
            }
        }

        [Display(Name = "Solde initial")]
        public double OpeningBalance
        {
            get
            {
                return this.opningBalance;
            }
            set
            {
                base.SetProperty<double>(ref this.opningBalance, value, "OpeningBalance");
            }
        }

        [Display(Name = "Solde final")]
        public double ClosingBalance
        {
            get
            {
                return this.closingBalance;
            }
            set
            {
                base.SetProperty<double>(ref this.closingBalance, value, "ClosingBalance");
            }
        }

        [Display(Name = "Solde réel")]
        public double ActualBalance
        {
            get
            {
                return this.actualBalance;
            }
            set
            {
                base.SetProperty<double>(ref this.actualBalance, value, "ActualBalance");
            }
        }

        [Display(Name = "Écart")]
        public double BalanceDifference
        {
            get
            {
                return this.actualBalance - this.closingBalance;
            }
        }

        [Display(Name = "Compte de régularisation des écarts")]
        public int? DifferenceAccountID
        {
            get
            {
                return this.differenceAccountID;
            }
            set
            {
                base.SetProperty<int?>(ref this.differenceAccountID, value, "DifferenceAccountID");
            }
        }

        [Display(Name = "Utilisateur de la période")]
        [Required(ErrorMessageResourceName = "ErrorCantBeEmpty", ErrorMessageResourceType = typeof(LangResource))]
        public int PeriodUserID
        {
            get
            {
                return this.periodUserID;
            }
            set
            {
                base.SetProperty<int>(ref this.periodUserID, value, "PeriodUserID");
            }
        }

        [Display(Name = "Utilisateur de clôture")]
        [RequiredIf("PeriodEnd", null, true, ErrorMessageResourceName = "ErrorCantBeEmpty", ErrorMessageResourceType = typeof(LangResource))]
        public int? ClosingPeriodUserID
        {
            get
            {
                return this.closingPeriodUserID;
            }
            set
            {
                base.SetProperty<int?>(ref this.closingPeriodUserID, value, "ClosingPeriodUserID");
            }
        }

        [Display(Name = "")]
        public Branch Branch
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<Branch>(ref this.branch, value, "Branch");
            }
        }

        [Display(Name = "Succursale")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est obligatoire")]
        public int BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int>(ref this.branchID, value, "BranchID");
            }
        }

        [Display(Name = "Caisse")]
        [Required(ErrorMessageResourceName = "ErrorCantBeEmpty", ErrorMessageResourceType = typeof(LangResource))]
        public int DrawerID
        {
            get
            {
                return this.drawerID;
            }
            set
            {
                base.SetProperty<int>(ref this.drawerID, value, "DrawerID");
            }
        }

        [Display(Name = "Caisse de clôture")]
        [RequiredIf("TransferdBalance", 0.0, true, ErrorMessageResourceName = "ErrorCantBeEmpty", ErrorMessageResourceType = typeof(LangResource))]
        public int? ClosingDrwerID
        {
            get
            {
                return this.closingDrawerID;
            }
            set
            {
                base.SetProperty<int?>(ref this.closingDrawerID, value, "ClosingDrwerID");
            }
        }

        [Display(Name = "Solde transféré")]
        public double TransferdBalance
        {
            get
            {
                return this.transferdBalance;
            }
            set
            {
                base.SetProperty<double>(ref this.transferdBalance, value, "TransferdBalance");
            }
        }

        [Display(Name = "Solde restant")]
        public double RemainingBalance
        {
            get
            {
                return this.remainingBalance;
            }
            set
            {
                base.SetProperty<double>(ref this.remainingBalance, value, "RemainingBalance");
            }
        }

        [Display(Name = "Clôturé")]
        public bool IsClosed
        {
            get
            {
                return this.PeriodEnd != null;
            }
        }

        [Display(Name = "Résumé des opérations de la période")]
        public BindingList<DrawerPeriodTransSummeryItem> Summery { get; set; }

        private DateTime periodStart;

        private DateTime? periodEnd;

        private double opningBalance;

        private double closingBalance;

        private double actualBalance;

        private int? differenceAccountID;

        private int periodUserID;

        private int? closingPeriodUserID;

        private Branch branch;

        private int branchID;

        private int drawerID;

        private int? closingDrawerID;

        private double transferdBalance;

        private double remainingBalance;
    }
}
