﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("InvoiceDetails")]
    public class InvoiceDetail : ProductTransaction
    {
        [Display(Name = "Catégorie")]
        public int? GroupID
        {
            get
            {
                return this.groupID;
            }
            set
            {
                base.SetProperty<int?>(ref this.groupID, value, "GroupID");
            }
        }

        [Display(Name = "")]
        public GroupOfProducts Group
        {
            get
            {
                return this.group;
            }
            set
            {
                base.SetProperty<GroupOfProducts>(ref this.group, value, "Group");
            }
        }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Remise %")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de réduction doit être compris entre 0 et 99 %")]
        public double DiscountPercentage
        {
            get
            {
                return this.discountPercentage;
            }
            set
            {
                base.SetProperty<double>(ref this.discountPercentage, value, "DiscountPercentage");
            }
        }

        [NotMapped]
        [Display(Name = "Montant TTC")]
        public double Net
        {
            get
            {
                return base.Total + this.Tax - this.Discount;
            }
            set
            {
                decimal net = Convert.ToDecimal(value);
                bool flag = net == 0m || base.Total == 0.0;
                if (!flag)
                {
                    decimal total = Convert.ToDecimal(base.Total);
                    decimal _Tax = Convert.ToDecimal(this.TaxPercentage);
                    decimal discount = 0m;
                    discount = 1m - net / (total * (1m + _Tax));
                    this.DiscountPercentage = Convert.ToDouble(discount);
                    this.Discount = this.DiscountPercentage * base.Total;
                    Product product = base.Product;
                    bool flag2 = product != null && product.CalculateTaxAfterDiscount;
                    if (flag2)
                    {
                        this.Tax = this.TaxPercentage * (base.Total - this.Discount);
                    }
                    else
                    {
                        this.Tax = this.TaxPercentage * base.Total;
                    }
                }
            }
        }

        public override double Quantity
        {
            get
            {
                return this._quantity;
            }
            set
            {
                base.SetProperty<double>(ref this._quantity, value, "Quantity");
            }
        }

        [Display(Name = "Taxe")]
        public double Tax { get; set; }

        [NotMapped]
        [Display(Name = "Taxe %")]
        [Range(0.0, 0.99, ErrorMessage = "Le pourcentage de taxe doit être compris entre 0 et 99 %")]
        public double TaxPercentage { get; set; }

        public new InvoiceDetail ShallowCopy()
        {
            return (InvoiceDetail)base.MemberwiseClone();
        }

        private int? groupID;

        private GroupOfProducts group;

        private double _quantity;

        private double discountPercentage;
    }
}
