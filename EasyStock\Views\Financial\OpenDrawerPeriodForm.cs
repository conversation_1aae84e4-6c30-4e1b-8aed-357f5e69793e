﻿using DevExpress.XtraEditors;
using DevExpress.XtraLayout.Utils;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.ComponentModel;
using System.Data.Entity.Migrations;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.Views.Financial
{
    public partial class OpenDrawerPeriodForm : DrawerPeriodForm
    {
        public new static OpenDrawerPeriodForm Instance
        {
            get
            {
                bool flag = OpenDrawerPeriodForm.instance == null || OpenDrawerPeriodForm.instance.IsDisposed;
                if (flag)
                {
                    OpenDrawerPeriodForm.instance = new OpenDrawerPeriodForm();
                }
                return OpenDrawerPeriodForm.instance;
            }
        }

        public OpenDrawerPeriodForm()
        {
            base.Name = "OpenDrawerPeriodForm";
            base.Shown += this.OpenDrawerPeriodForm_Shown;
        }

        private void OpenDrawerPeriodForm_Shown(object sender, EventArgs e)
        {
            this.lyc_ClosingGroup.Enabled = false;
            this.lyc_OpeningGroup.Enabled = true;
            this.lyc_ClosingGroup.Visibility = (this.lyc_ClosePeriodButton.Visibility = LayoutVisibility.Never);
            this.lyc_OpenPeriodButton.Visibility = LayoutVisibility.Always;
            this.PeriodStartDateEdit.EditValueChanged += this.PeriodStartDateEdit_EditValueChanged;
            this.OpenPeriodButton.Click += this.OpenPeriodButton_Click;
            this.DrawerIDLookUpEdit.EditValueChanged += this.DrawerIDLookUpEdit_EditValueChanged;
        }

        private void DrawerIDLookUpEdit_EditValueChanged(object sender, EventArgs e)
        {
            bool flag = base.period.DrawerID > 0;
            if (flag)
            {
                DateTime? date = (from x in this.db.DrawerPeriods
                                  where x.DrawerID == this.period.DrawerID
                                  select x.PeriodEnd).Max<DateTime?>();
                bool flag2 = date != null;
                if (flag2)
                {
                    base.period.PeriodStart = date.Value.AddMinutes(1.0);
                }
            }
        }

        private void OpenPeriodButton_Click(object sender, EventArgs e)
        {
            bool flag = !this.CheckAction(WindowActions.Add);
            if (!flag)
            {
                base.EnableValidation(this.dataLayoutControl1);
                bool flag2 = !this.ValidateChildren();
                if (!flag2)
                {
                    base.DisableValidation(this.dataLayoutControl1);
                    bool hasAnotherOpendPeriod = (from x in this.db.DrawerPeriods
                                                  where x.ID != this.period.ID && x.PeriodEnd == null && (x.DrawerID == this.period.DrawerID || x.PeriodUserID == this.period.PeriodUserID)
                                                  select x).Count<DrawerPeriod>() > 0;
                    bool flag3 = hasAnotherOpendPeriod;
                    if (flag3)
                    {
                        XtraMessageBox.Show("Il existe une période ouverte pour ce caisse ou cet utilisateur qui n'a pas encore été clôturée. \n Veuillez d'abord clôturer la période précédente.", "", MessageBoxButtons.OK, MessageBoxIcon.Hand);
                    }
                    else
                    {
                        bool isInotherPeriod = (from x in this.db.DrawerPeriods
                                                where x.ID != this.period.ID && x.PeriodEnd > (DateTime?)this.period.PeriodStart && x.PeriodStart < this.period.PeriodStart
                                                select x).Count<DrawerPeriod>() > 0;
                        bool flag4 = isInotherPeriod;
                        if (flag4)
                        {
                            XtraMessageBox.Show("La date de début de la période que vous essayez d'ouvrir se situe dans la période d'une autre période. \n Veuillez choisir une autre date.", "", MessageBoxButtons.OK, MessageBoxIcon.Hand);
                        }
                        else
                        {
                            base.GetPeriodSummery();
                            this.db.DrawerPeriods.AddOrUpdate(new DrawerPeriod[]
                            {
                                base.period
                            });
                            this.db.SaveChanges();
                            XtraMessageBox.Show(string.Format("La période numéro {0} a été ouverte avec succès", base.period.ID), "", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
                            base.Save();
                            this.OpenPeriodButton.Enabled = false;
                        }
                    }
                }
            }
        }

        private void PeriodStartDateEdit_EditValueChanged(object sender, EventArgs e)
        {
            bool flag = base.period.DrawerID == 0 || base.period.PeriodStart.Year < 1950 || base.period.PeriodEnd != null;
            if (!flag)
            {
                base.GetPeriodSummery();
            }
        }

        public override void New()
        {
            this.db = new ERPDataContext();
            DrawerPeriod drawerPeriod = new DrawerPeriod();
            drawerPeriod.Summery = new BindingList<DrawerPeriodTransSummeryItem>();
            drawerPeriod.PeriodStart = DateTime.Now;
            drawerPeriod.PeriodUserID = CurrentSession.CurrentUser.ID;
            Drawer defualtDrawer = CurrentSession.DefualtDrawer;
            drawerPeriod.DrawerID = ((defualtDrawer != null) ? defualtDrawer.ID : 0);
            base.period = drawerPeriod;
            this.DrawerIDLookUpEdit_EditValueChanged(null, null);
            base.GetPeriodSummery();
            this.gridControl1.DataSource = base.period.Summery;
            this.OpenPeriodButton.Enabled = true;
            base.New();
        }

        private static OpenDrawerPeriodForm instance;
    }
}
