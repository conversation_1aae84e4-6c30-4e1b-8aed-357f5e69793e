﻿namespace EasyStock.ReportViews
{
	// Token: 0x0200031B RID: 795
	public partial class IncomeStatmentReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x06001380 RID: 4992 RVA: 0x0015A00C File Offset: 0x0015820C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001381 RID: 4993 RVA: 0x0015A044 File Offset: 0x00158244
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 53);
            this.documentViewer1.Size = new System.Drawing.Size(798, 374);
            // 
            // IncomeStatmentReportView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(798, 448);
            this.Name = "IncomeStatmentReportView";
            this.Text = "Compte de résultat";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x0400195E RID: 6494
		private global::System.ComponentModel.IContainer components = null;
	}
}
