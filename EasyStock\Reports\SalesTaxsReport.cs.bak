﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x02000391 RID: 913
	public class SalesTaxsReport : MasterReport
	{
		// Token: 0x06001575 RID: 5493 RVA: 0x0000AE5A File Offset: 0x0000905A
		public SalesTaxsReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001576 RID: 5494 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void xrTableCell11_Draw(object sender, DrawEventArgs e)
		{
		}

		// Token: 0x06001577 RID: 5495 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void xrTableCell11_BeforePrint(object sender, PrintEventArgs e)
		{
		}

		// Token: 0x06001578 RID: 5496 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void xrTableCell11_AfterPrint(object sender, EventArgs e)
		{
		}

		// Token: 0x06001579 RID: 5497 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void xrTableCell11_PrintOnPage(object sender, PrintOnPageEventArgs e)
		{
		}

		// Token: 0x0600157A RID: 5498 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void SalesTaxsReport_BeforePrint(object sender, PrintEventArgs e)
		{
		}

		// Token: 0x0600157B RID: 5499 RVA: 0x001BFD38 File Offset: 0x001BDF38
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600157C RID: 5500 RVA: 0x001BFD70 File Offset: 0x001BDF70
		private void InitializeComponent()
		{
			this.components = new Container();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.Detail = new DetailBand();
			this.xrTable3 = new XRTable();
			this.xrTableRow9 = new XRTableRow();
			this.xrTableCell5 = new XRTableCell();
			this.xrTableCell7 = new XRTableCell();
			this.xrTableCell8 = new XRTableCell();
			this.xrTableCell9 = new XRTableCell();
			this.xrTableCell10 = new XRTableCell();
			this.xrTableCell11 = new XRTableCell();
			this.xrTableCell12 = new XRTableCell();
			this.xrTableCell14 = new XRTableCell();
			this.ReportHeader = new ReportHeaderBand();
			this.xrTable1 = new XRTable();
			this.xrTableRow2 = new XRTableRow();
			this.xrTableCell13 = new XRTableCell();
			this.xrTableRow3 = new XRTableRow();
			this.xrTableCell2 = new XRTableCell();
			this.xrTableRow4 = new XRTableRow();
			this.xrTableCell3 = new XRTableCell();
			this.xrTableRow1 = new XRTableRow();
			this.xrTableCell1 = new XRTableCell();
			this.xrTableRow8 = new XRTableRow();
			this.xrTableCell6 = new XRTableCell();
			this.xrPictureBox2 = new XRPictureBox();
			this.xrTable2 = new XRTable();
			this.xrTableRow5 = new XRTableRow();
			this.Cell_ReportName = new XRTableCell();
			this.xrTableRow6 = new XRTableRow();
			this.xrTableCell4 = new XRTableCell();
			this.xrTableRow7 = new XRTableRow();
			this.Cell_Filters = new XRTableCell();
			this.objectDataSource1 = new ObjectDataSource(this.components);
			this.GroupHeader1 = new GroupHeaderBand();
			this.xrTable4 = new XRTable();
			this.xrTableRow10 = new XRTableRow();
			this.xrTableCell15 = new XRTableCell();
			this.xrTableCell16 = new XRTableCell();
			this.xrTableCell17 = new XRTableCell();
			this.xrTableCell18 = new XRTableCell();
			this.xrTableCell19 = new XRTableCell();
			this.xrTableCell20 = new XRTableCell();
			this.xrTableCell21 = new XRTableCell();
			this.xrTableCell22 = new XRTableCell();
			this.GroupFooter1 = new GroupFooterBand();
			this.xrTable5 = new XRTable();
			this.xrTableRow11 = new XRTableRow();
			this.xrTableCell23 = new XRTableCell();
			this.xrTableCell27 = new XRTableCell();
			this.xrTableCell28 = new XRTableCell();
			this.xrTableCell29 = new XRTableCell();
			this.xrTableCell30 = new XRTableCell();
			this.xrControlStyle1 = new XRControlStyle();
			((ISupportInitialize)this.xrTable3).BeginInit();
			((ISupportInitialize)this.xrTable1).BeginInit();
			((ISupportInitialize)this.xrTable2).BeginInit();
			((ISupportInitialize)this.objectDataSource1).BeginInit();
			((ISupportInitialize)this.xrTable4).BeginInit();
			((ISupportInitialize)this.xrTable5).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.TopMargin.Dpi = 254f;
			this.TopMargin.HeightF = 85f;
			this.TopMargin.Name = "TopMargin";
			this.BottomMargin.Dpi = 254f;
			this.BottomMargin.HeightF = 66f;
			this.BottomMargin.Name = "BottomMargin";
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.xrTable3
			});
			this.Detail.Dpi = 254f;
			this.Detail.HeightF = 64f;
			this.Detail.Name = "Detail";
			this.xrTable3.Dpi = 254f;
			this.xrTable3.EvenStyleName = "xrControlStyle1";
			this.xrTable3.LocationFloat = new PointFloat(9f, 0f);
			this.xrTable3.Name = "xrTable3";
			this.xrTable3.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable3.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow9
			});
			this.xrTable3.SizeF = new SizeF(1921f, 64f);
			this.xrTable3.StylePriority.UseTextAlignment = false;
			this.xrTable3.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableRow9.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell5,
				this.xrTableCell7,
				this.xrTableCell8,
				this.xrTableCell9,
				this.xrTableCell10,
				this.xrTableCell11,
				this.xrTableCell12,
				this.xrTableCell14
			});
			this.xrTableRow9.Dpi = 254f;
			this.xrTableRow9.Name = "xrTableRow9";
			this.xrTableRow9.Weight = 0.5609756097560976;
			this.xrTableCell5.Dpi = 254f;
			this.xrTableCell5.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[InvoicesCode]")
			});
			this.xrTableCell5.Multiline = true;
			this.xrTableCell5.Name = "xrTableCell5";
			this.xrTableCell5.StylePriority.UseTextAlignment = false;
			this.xrTableCell5.Text = "xrTableCell5";
			this.xrTableCell5.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell5.TextFormatString = "{0:f0}";
			this.xrTableCell5.Weight = 0.11097357908558236;
			this.xrTableCell7.Dpi = 254f;
			this.xrTableCell7.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TaxFileNumber]")
			});
			this.xrTableCell7.Multiline = true;
			this.xrTableCell7.Name = "xrTableCell7";
			this.xrTableCell7.StylePriority.UseTextAlignment = false;
			this.xrTableCell7.Text = "xrTableCell7";
			this.xrTableCell7.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell7.Weight = 0.13345416892657405;
			this.xrTableCell8.Dpi = 254f;
			this.xrTableCell8.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Name]")
			});
			this.xrTableCell8.Multiline = true;
			this.xrTableCell8.Name = "xrTableCell8";
			this.xrTableCell8.StylePriority.UseTextAlignment = false;
			this.xrTableCell8.Text = "xrTableCell8";
			this.xrTableCell8.TextAlignment = TextAlignment.MiddleLeft;
			this.xrTableCell8.Weight = 0.1730526262250295;
			this.xrTableCell9.Dpi = 254f;
			this.xrTableCell9.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[InvoiceDate]")
			});
			this.xrTableCell9.Multiline = true;
			this.xrTableCell9.Name = "xrTableCell9";
			this.xrTableCell9.StylePriority.UseTextAlignment = false;
			this.xrTableCell9.Text = "xrTableCell9";
			this.xrTableCell9.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell9.TextFormatString = "{0:d}";
			this.xrTableCell9.Weight = 0.17100014471027328;
			this.xrTableCell10.Dpi = 254f;
			this.xrTableCell10.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Total]")
			});
			this.xrTableCell10.Multiline = true;
			this.xrTableCell10.Name = "xrTableCell10";
			this.xrTableCell10.StylePriority.UseTextAlignment = false;
			this.xrTableCell10.Text = "xrTableCell10";
			this.xrTableCell10.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell10.TextFormatString = "{0:0.##}";
			this.xrTableCell10.Weight = 0.10520240461361796;
			this.xrTableCell11.Dpi = 254f;
			this.xrTableCell11.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Discount]")
			});
			this.xrTableCell11.Multiline = true;
			this.xrTableCell11.Name = "xrTableCell11";
			this.xrTableCell11.StylePriority.UseTextAlignment = false;
			this.xrTableCell11.Text = "xrTableCell11";
			this.xrTableCell11.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell11.TextFormatString = "{0:0.##}";
			this.xrTableCell11.Weight = 0.10620548577541508;
			this.xrTableCell11.BeforePrint += this.xrTableCell11_BeforePrint;
			this.xrTableCell11.AfterPrint += this.xrTableCell11_AfterPrint;
			this.xrTableCell11.PrintOnPage += this.xrTableCell11_PrintOnPage;
			this.xrTableCell11.Draw += this.xrTableCell11_Draw;
			this.xrTableCell12.Dpi = 254f;
			this.xrTableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TaxAmount]")
			});
			this.xrTableCell12.Multiline = true;
			this.xrTableCell12.Name = "xrTableCell12";
			this.xrTableCell12.StylePriority.UseTextAlignment = false;
			this.xrTableCell12.Text = "xrTableCell12";
			this.xrTableCell12.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell12.TextFormatString = "{0:0.##}";
			this.xrTableCell12.Weight = 0.10114817365063493;
			this.xrTableCell14.Dpi = 254f;
			this.xrTableCell14.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Net]")
			});
			this.xrTableCell14.Multiline = true;
			this.xrTableCell14.Name = "xrTableCell14";
			this.xrTableCell14.StylePriority.UseTextAlignment = false;
			this.xrTableCell14.Text = "xrTableCell14";
			this.xrTableCell14.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell14.TextFormatString = "{0:0.##}";
			this.xrTableCell14.Weight = 0.1316062176165803;
			this.ReportHeader.Controls.AddRange(new XRControl[]
			{
				this.xrTable1,
				this.xrPictureBox2,
				this.xrTable2
			});
			this.ReportHeader.Dpi = 254f;
			this.ReportHeader.HeightF = 557.2762f;
			this.ReportHeader.Name = "ReportHeader";
			this.xrTable1.Dpi = 254f;
			this.xrTable1.LocationFloat = new PointFloat(1192.875f, 0f);
			this.xrTable1.Name = "xrTable1";
			this.xrTable1.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrTable1.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow2,
				this.xrTableRow3,
				this.xrTableRow4,
				this.xrTableRow1,
				this.xrTableRow8
			});
			this.xrTable1.SizeF = new SizeF(735.5416f, 313.5313f);
			this.xrTable1.StylePriority.UseTextAlignment = false;
			this.xrTable1.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell13
			});
			this.xrTableRow2.Dpi = 254f;
			this.xrTableRow2.Name = "xrTableRow2";
			this.xrTableRow2.Weight = 17.**************;
			this.xrTableCell13.Dpi = 254f;
			this.xrTableCell13.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyName]")
			});
			this.xrTableCell13.Font = new Font("Arial", 12f, FontStyle.Bold);
			this.xrTableCell13.Multiline = true;
			this.xrTableCell13.Name = "xrTableCell13";
			this.xrTableCell13.StylePriority.UseFont = false;
			this.xrTableCell13.StylePriority.UseTextAlignment = false;
			this.xrTableCell13.Text = "xrTableCell13";
			this.xrTableCell13.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell13.Weight = 1.8461538461538458;
			this.xrTableRow3.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell2
			});
			this.xrTableRow3.Dpi = 254f;
			this.xrTableRow3.Name = "xrTableRow3";
			this.xrTableRow3.Weight = 17.**************;
			this.xrTableCell2.Dpi = 254f;
			this.xrTableCell2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CommercialBook]")
			});
			this.xrTableCell2.Multiline = true;
			this.xrTableCell2.Name = "xrTableCell2";
			this.xrTableCell2.Text = "xrTableCell2";
			this.xrTableCell2.Weight = 1.8461538461538458;
			this.xrTableRow4.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell3
			});
			this.xrTableRow4.Dpi = 254f;
			this.xrTableRow4.Name = "xrTableRow4";
			this.xrTableRow4.Weight = 17.**************;
			this.xrTableCell3.Dpi = 254f;
			this.xrTableCell3.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyTaxCard]")
			});
			this.xrTableCell3.Multiline = true;
			this.xrTableCell3.Name = "xrTableCell3";
			this.xrTableCell3.Text = "xrTableCell3";
			this.xrTableCell3.Weight = 1.8461538461538458;
			this.xrTableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell1
			});
			this.xrTableRow1.Dpi = 254f;
			this.xrTableRow1.Name = "xrTableRow1";
			this.xrTableRow1.Weight = 17.**************;
			this.xrTableCell1.Dpi = 254f;
			this.xrTableCell1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyAddress]")
			});
			this.xrTableCell1.Multiline = true;
			this.xrTableCell1.Name = "xrTableCell1";
			this.xrTableCell1.Text = "xrTableCell1";
			this.xrTableCell1.Weight = 1.8461538461538458;
			this.xrTableRow8.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell6
			});
			this.xrTableRow8.Dpi = 254f;
			this.xrTableRow8.Name = "xrTableRow8";
			this.xrTableRow8.Weight = 17.**************;
			this.xrTableCell6.Dpi = 254f;
			this.xrTableCell6.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyPhone]")
			});
			this.xrTableCell6.Multiline = true;
			this.xrTableCell6.Name = "xrTableCell6";
			this.xrTableCell6.Text = "xrTableCell6";
			this.xrTableCell6.Weight = 1.8461538461538458;
			this.xrPictureBox2.Dpi = 254f;
			this.xrPictureBox2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "ImageSource", "[CompanyLogo]")
			});
			this.xrPictureBox2.LocationFloat = new PointFloat(72.49988f, 42.06873f);
			this.xrPictureBox2.Name = "xrPictureBox2";
			this.xrPictureBox2.SizeF = new SizeF(254f, 254f);
			this.xrPictureBox2.Sizing = ImageSizeMode.Squeeze;
			this.xrTable2.AnchorHorizontal = HorizontalAnchorStyles.Both;
			this.xrTable2.AnchorVertical = VerticalAnchorStyles.Both;
			this.xrTable2.Dpi = 254f;
			this.xrTable2.LocationFloat = new PointFloat(0.0002028147f, 313.5313f);
			this.xrTable2.Name = "xrTable2";
			this.xrTable2.Padding = new PaddingInfo(5, 5, 0, 0, 254f);
			this.xrTable2.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow5,
				this.xrTableRow6,
				this.xrTableRow7
			});
			this.xrTable2.SizeF = new SizeF(1930f, 218.745f);
			this.xrTable2.StylePriority.UseTextAlignment = false;
			this.xrTable2.TextAlignment = TextAlignment.TopCenter;
			this.xrTableRow5.Cells.AddRange(new XRTableCell[]
			{
				this.Cell_ReportName
			});
			this.xrTableRow5.Dpi = 254f;
			this.xrTableRow5.Name = "xrTableRow5";
			this.xrTableRow5.Weight = 1.0;
			this.Cell_ReportName.BackColor = Color.WhiteSmoke;
			this.Cell_ReportName.Borders = BorderSide.All;
			this.Cell_ReportName.BorderWidth = 2f;
			this.Cell_ReportName.CanGrow = false;
			this.Cell_ReportName.Dpi = 254f;
			this.Cell_ReportName.Font = new Font("Traditional Arabic", 21.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.Cell_ReportName.Multiline = true;
			this.Cell_ReportName.Name = "Cell_ReportName";
			this.Cell_ReportName.Padding = new PaddingInfo(13, 13, 13, 13, 254f);
			this.Cell_ReportName.RowSpan = 2;
			this.Cell_ReportName.StylePriority.UseBackColor = false;
			this.Cell_ReportName.StylePriority.UseBorders = false;
			this.Cell_ReportName.StylePriority.UseBorderWidth = false;
			this.Cell_ReportName.StylePriority.UseFont = false;
			this.Cell_ReportName.StylePriority.UsePadding = false;
			this.Cell_ReportName.StylePriority.UseTextAlignment = false;
			this.Cell_ReportName.Text = "اسم التقرير";
			this.Cell_ReportName.TextAlignment = TextAlignment.MiddleCenter;
			this.Cell_ReportName.Weight = 3.0;
			this.xrTableRow6.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell4
			});
			this.xrTableRow6.Dpi = 254f;
			this.xrTableRow6.Name = "xrTableRow6";
			this.xrTableRow6.Weight = 1.0;
			this.xrTableCell4.BackColor = Color.WhiteSmoke;
			this.xrTableCell4.Borders = BorderSide.All;
			this.xrTableCell4.BorderWidth = 2f;
			this.xrTableCell4.CanGrow = false;
			this.xrTableCell4.Dpi = 254f;
			this.xrTableCell4.Font = new Font("Arial", 12.64f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTableCell4.Multiline = true;
			this.xrTableCell4.Name = "xrTableCell4";
			this.xrTableCell4.Padding = new PaddingInfo(13, 13, 13, 13, 254f);
			this.xrTableCell4.StylePriority.UseBackColor = false;
			this.xrTableCell4.StylePriority.UseBorders = false;
			this.xrTableCell4.StylePriority.UseBorderWidth = false;
			this.xrTableCell4.StylePriority.UseFont = false;
			this.xrTableCell4.StylePriority.UsePadding = false;
			this.xrTableCell4.StylePriority.UseTextAlignment = false;
			this.xrTableCell4.Text = "xrTableCell4";
			this.xrTableCell4.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell4.Weight = 3.0;
			this.xrTableRow7.Cells.AddRange(new XRTableCell[]
			{
				this.Cell_Filters
			});
			this.xrTableRow7.Dpi = 254f;
			this.xrTableRow7.Name = "xrTableRow7";
			this.xrTableRow7.Weight = 1.0;
			this.Cell_Filters.CanGrow = false;
			this.Cell_Filters.Dpi = 254f;
			this.Cell_Filters.Multiline = true;
			this.Cell_Filters.Name = "Cell_Filters";
			this.Cell_Filters.StylePriority.UseTextAlignment = false;
			this.Cell_Filters.TextAlignment = TextAlignment.MiddleCenter;
			this.Cell_Filters.Weight = 3.0;
			this.objectDataSource1.DataSource = typeof(PurchaseTaxsReportModel);
			this.objectDataSource1.Name = "objectDataSource1";
			this.GroupHeader1.Controls.AddRange(new XRControl[]
			{
				this.xrTable4
			});
			this.GroupHeader1.Dpi = 254f;
			this.GroupHeader1.HeightF = 103.6875f;
			this.GroupHeader1.Name = "GroupHeader1";
			this.xrTable4.BackColor = Color.FromArgb(48, 54, 64);
			this.xrTable4.Dpi = 254f;
			this.xrTable4.Font = new Font("Arial", 11.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTable4.ForeColor = Color.White;
			this.xrTable4.LocationFloat = new PointFloat(9.000084f, 0f);
			this.xrTable4.Name = "xrTable4";
			this.xrTable4.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable4.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow10
			});
			this.xrTable4.SizeF = new SizeF(1921f, 103.6875f);
			this.xrTable4.StylePriority.UseBackColor = false;
			this.xrTable4.StylePriority.UseFont = false;
			this.xrTable4.StylePriority.UseForeColor = false;
			this.xrTableRow10.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell15,
				this.xrTableCell16,
				this.xrTableCell17,
				this.xrTableCell18,
				this.xrTableCell19,
				this.xrTableCell20,
				this.xrTableCell21,
				this.xrTableCell22
			});
			this.xrTableRow10.Dpi = 254f;
			this.xrTableRow10.Name = "xrTableRow10";
			this.xrTableRow10.Weight = 0.5609756097560976;
			this.xrTableCell15.BorderColor = Color.White;
			this.xrTableCell15.Borders = BorderSide.Right;
			this.xrTableCell15.Dpi = 254f;
			this.xrTableCell15.Multiline = true;
			this.xrTableCell15.Name = "xrTableCell15";
			this.xrTableCell15.StylePriority.UseBorderColor = false;
			this.xrTableCell15.StylePriority.UseBorders = false;
			this.xrTableCell15.StylePriority.UseTextAlignment = false;
			this.xrTableCell15.Text = "رقم الفاتوره";
			this.xrTableCell15.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell15.Weight = 0.11139921774544927;
			this.xrTableCell16.BorderColor = Color.White;
			this.xrTableCell16.Borders = BorderSide.Right;
			this.xrTableCell16.Dpi = 254f;
			this.xrTableCell16.Multiline = true;
			this.xrTableCell16.Name = "xrTableCell16";
			this.xrTableCell16.StylePriority.UseBorderColor = false;
			this.xrTableCell16.StylePriority.UseBorders = false;
			this.xrTableCell16.StylePriority.UseTextAlignment = false;
			this.xrTableCell16.Text = "رقم الملف الضريبي";
			this.xrTableCell16.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell16.Weight = 0.14824799047920573;
			this.xrTableCell17.BorderColor = Color.White;
			this.xrTableCell17.Borders = BorderSide.Right;
			this.xrTableCell17.Dpi = 254f;
			this.xrTableCell17.Multiline = true;
			this.xrTableCell17.Name = "xrTableCell17";
			this.xrTableCell17.StylePriority.UseBorderColor = false;
			this.xrTableCell17.StylePriority.UseBorders = false;
			this.xrTableCell17.StylePriority.UseTextAlignment = false;
			this.xrTableCell17.Text = "الاسم";
			this.xrTableCell17.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell17.Weight = 0.17305259596567169;
			this.xrTableCell18.BorderColor = Color.White;
			this.xrTableCell18.Borders = BorderSide.Right;
			this.xrTableCell18.Dpi = 254f;
			this.xrTableCell18.Multiline = true;
			this.xrTableCell18.Name = "xrTableCell18";
			this.xrTableCell18.StylePriority.UseBorderColor = false;
			this.xrTableCell18.StylePriority.UseBorders = false;
			this.xrTableCell18.StylePriority.UseTextAlignment = false;
			this.xrTableCell18.Text = "التاريخ";
			this.xrTableCell18.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell18.TextFormatString = "{0:d}";
			this.xrTableCell18.Weight = 0.15620622584067484;
			this.xrTableCell19.BorderColor = Color.White;
			this.xrTableCell19.Borders = BorderSide.Right;
			this.xrTableCell19.Dpi = 254f;
			this.xrTableCell19.Multiline = true;
			this.xrTableCell19.Name = "xrTableCell19";
			this.xrTableCell19.StylePriority.UseBorderColor = false;
			this.xrTableCell19.StylePriority.UseBorders = false;
			this.xrTableCell19.StylePriority.UseTextAlignment = false;
			this.xrTableCell19.Text = "المبلغ";
			this.xrTableCell19.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell19.Weight = 0.10477682087299485;
			this.xrTableCell20.BorderColor = Color.White;
			this.xrTableCell20.Borders = BorderSide.Right;
			this.xrTableCell20.Dpi = 254f;
			this.xrTableCell20.Multiline = true;
			this.xrTableCell20.Name = "xrTableCell20";
			this.xrTableCell20.StylePriority.UseBorderColor = false;
			this.xrTableCell20.StylePriority.UseBorders = false;
			this.xrTableCell20.StylePriority.UseTextAlignment = false;
			this.xrTableCell20.Text = " الخصم";
			this.xrTableCell20.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell20.Weight = 0.10577978227459091;
			this.xrTableCell21.BorderColor = Color.White;
			this.xrTableCell21.Borders = BorderSide.Right;
			this.xrTableCell21.Dpi = 254f;
			this.xrTableCell21.Multiline = true;
			this.xrTableCell21.Name = "xrTableCell21";
			this.xrTableCell21.StylePriority.UseBorderColor = false;
			this.xrTableCell21.StylePriority.UseBorders = false;
			this.xrTableCell21.StylePriority.UseTextAlignment = false;
			this.xrTableCell21.Text = " الضريبة";
			this.xrTableCell21.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell21.Weight = 0.11493922048164788;
			this.xrTableCell22.BorderColor = Color.White;
			this.xrTableCell22.Borders = BorderSide.Right;
			this.xrTableCell22.Dpi = 254f;
			this.xrTableCell22.Multiline = true;
			this.xrTableCell22.Name = "xrTableCell22";
			this.xrTableCell22.StylePriority.UseBorderColor = false;
			this.xrTableCell22.StylePriority.UseBorders = false;
			this.xrTableCell22.StylePriority.UseTextAlignment = false;
			this.xrTableCell22.Text = "الصافي";
			this.xrTableCell22.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell22.Weight = 0.11824088740127629;
			this.GroupFooter1.Controls.AddRange(new XRControl[]
			{
				this.xrTable5
			});
			this.GroupFooter1.Dpi = 254f;
			this.GroupFooter1.HeightF = 142.5363f;
			this.GroupFooter1.Name = "GroupFooter1";
			this.xrTable5.BackColor = Color.NavajoWhite;
			this.xrTable5.Dpi = 254f;
			this.xrTable5.Font = new Font("Arial", 11.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTable5.ForeColor = Color.Black;
			this.xrTable5.LocationFloat = new PointFloat(9.000008f, 0f);
			this.xrTable5.Name = "xrTable5";
			this.xrTable5.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrTable5.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow11
			});
			this.xrTable5.SizeF = new SizeF(1920.208f, 103.6875f);
			this.xrTable5.StylePriority.UseBackColor = false;
			this.xrTable5.StylePriority.UseFont = false;
			this.xrTable5.StylePriority.UseForeColor = false;
			this.xrTableRow11.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell23,
				this.xrTableCell27,
				this.xrTableCell28,
				this.xrTableCell29,
				this.xrTableCell30
			});
			this.xrTableRow11.Dpi = 254f;
			this.xrTableRow11.Name = "xrTableRow11";
			this.xrTableRow11.Weight = 0.5609756097560976;
			this.xrTableCell23.BorderColor = Color.White;
			this.xrTableCell23.Borders = BorderSide.Right;
			this.xrTableCell23.Dpi = 254f;
			this.xrTableCell23.Multiline = true;
			this.xrTableCell23.Name = "xrTableCell23";
			this.xrTableCell23.StylePriority.UseBorderColor = false;
			this.xrTableCell23.StylePriority.UseBorders = false;
			this.xrTableCell23.StylePriority.UseTextAlignment = false;
			this.xrTableCell23.Text = "المجموع";
			this.xrTableCell23.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell23.Weight = 0.32250116581779736;
			this.xrTableCell27.BorderColor = Color.White;
			this.xrTableCell27.Borders = BorderSide.Right;
			this.xrTableCell27.Dpi = 254f;
			this.xrTableCell27.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Total])")
			});
			this.xrTableCell27.Multiline = true;
			this.xrTableCell27.Name = "xrTableCell27";
			this.xrTableCell27.StylePriority.UseBorderColor = false;
			this.xrTableCell27.StylePriority.UseBorders = false;
			this.xrTableCell27.StylePriority.UseTextAlignment = false;
			this.xrTableCell27.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell27.TextFormatString = "{0:0.##}";
			this.xrTableCell27.Weight = 0.05769491412822348;
			this.xrTableCell28.BorderColor = Color.White;
			this.xrTableCell28.Borders = BorderSide.Right;
			this.xrTableCell28.Dpi = 254f;
			this.xrTableCell28.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Discount])")
			});
			this.xrTableCell28.Multiline = true;
			this.xrTableCell28.Name = "xrTableCell28";
			this.xrTableCell28.StylePriority.UseBorderColor = false;
			this.xrTableCell28.StylePriority.UseBorders = false;
			this.xrTableCell28.StylePriority.UseTextAlignment = false;
			this.xrTableCell28.Text = "قيمه الخصم";
			this.xrTableCell28.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell28.TextFormatString = "{0:0.##}";
			this.xrTableCell28.Weight = 0.058008346662544674;
			this.xrTableCell29.BorderColor = Color.White;
			this.xrTableCell29.Borders = BorderSide.Right;
			this.xrTableCell29.Dpi = 254f;
			this.xrTableCell29.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([TaxAmount])")
			});
			this.xrTableCell29.Multiline = true;
			this.xrTableCell29.Name = "xrTableCell29";
			this.xrTableCell29.StylePriority.UseBorderColor = false;
			this.xrTableCell29.StylePriority.UseBorders = false;
			this.xrTableCell29.StylePriority.UseTextAlignment = false;
			this.xrTableCell29.Text = "قيمه الضريبه";
			this.xrTableCell29.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell29.TextFormatString = "{0:0.##}";
			this.xrTableCell29.Weight = 0.05547162942605122;
			this.xrTableCell30.BorderColor = Color.White;
			this.xrTableCell30.Borders = BorderSide.Right;
			this.xrTableCell30.Dpi = 254f;
			this.xrTableCell30.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Net])")
			});
			this.xrTableCell30.Multiline = true;
			this.xrTableCell30.Name = "xrTableCell30";
			this.xrTableCell30.StylePriority.UseBorderColor = false;
			this.xrTableCell30.StylePriority.UseBorders = false;
			this.xrTableCell30.StylePriority.UseTextAlignment = false;
			this.xrTableCell30.Text = "ألصافي";
			this.xrTableCell30.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell30.TextFormatString = "{0:0.##}";
			this.xrTableCell30.Weight = 0.07241251046374719;
			this.xrControlStyle1.BackColor = Color.FromArgb(224, 224, 224);
			this.xrControlStyle1.Name = "xrControlStyle1";
			this.xrControlStyle1.Padding = new PaddingInfo(0, 0, 0, 0, 254f);
			base.Bands.AddRange(new Band[]
			{
				this.TopMargin,
				this.BottomMargin,
				this.Detail,
				this.ReportHeader,
				this.GroupHeader1,
				this.GroupFooter1
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource1
			});
			base.DataSource = this.objectDataSource1;
			this.Dpi = 254f;
			this.Font = new Font("Arial", 9.75f);
			base.Margins = new Margins(85, 85, 85, 66);
			base.PageHeight = 2970;
			base.PageWidth = 2100;
			base.PaperKind = PaperKind.A4;
			base.ReportUnit = ReportUnit.TenthsOfAMillimeter;
			this.RightToLeft = RightToLeft.Yes;
			base.RightToLeftLayout = RightToLeftLayout.Yes;
			base.SnapGridSize = 25f;
			base.StyleSheet.AddRange(new XRControlStyle[]
			{
				this.xrControlStyle1
			});
			base.Version = "20.2";
			this.BeforePrint += this.SalesTaxsReport_BeforePrint;
			((ISupportInitialize)this.xrTable3).EndInit();
			((ISupportInitialize)this.xrTable1).EndInit();
			((ISupportInitialize)this.xrTable2).EndInit();
			((ISupportInitialize)this.objectDataSource1).EndInit();
			((ISupportInitialize)this.xrTable4).EndInit();
			((ISupportInitialize)this.xrTable5).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x0400212E RID: 8494
		private int index = 0;

		// Token: 0x0400212F RID: 8495
		private IContainer components = null;

		// Token: 0x04002130 RID: 8496
		private TopMarginBand TopMargin;

		// Token: 0x04002131 RID: 8497
		private BottomMarginBand BottomMargin;

		// Token: 0x04002132 RID: 8498
		private DetailBand Detail;

		// Token: 0x04002133 RID: 8499
		private ReportHeaderBand ReportHeader;

		// Token: 0x04002134 RID: 8500
		public XRTable xrTable1;

		// Token: 0x04002135 RID: 8501
		public XRTableRow xrTableRow2;

		// Token: 0x04002136 RID: 8502
		public XRTableCell xrTableCell13;

		// Token: 0x04002137 RID: 8503
		public XRTableRow xrTableRow3;

		// Token: 0x04002138 RID: 8504
		public XRTableCell xrTableCell2;

		// Token: 0x04002139 RID: 8505
		public XRTableRow xrTableRow4;

		// Token: 0x0400213A RID: 8506
		public XRTableCell xrTableCell3;

		// Token: 0x0400213B RID: 8507
		public XRTableRow xrTableRow1;

		// Token: 0x0400213C RID: 8508
		public XRTableCell xrTableCell1;

		// Token: 0x0400213D RID: 8509
		public XRTableRow xrTableRow8;

		// Token: 0x0400213E RID: 8510
		public XRTableCell xrTableCell6;

		// Token: 0x0400213F RID: 8511
		public XRPictureBox xrPictureBox2;

		// Token: 0x04002140 RID: 8512
		public XRTable xrTable2;

		// Token: 0x04002141 RID: 8513
		public XRTableRow xrTableRow5;

		// Token: 0x04002142 RID: 8514
		public XRTableCell Cell_ReportName;

		// Token: 0x04002143 RID: 8515
		public XRTableRow xrTableRow6;

		// Token: 0x04002144 RID: 8516
		public XRTableCell xrTableCell4;

		// Token: 0x04002145 RID: 8517
		public XRTableRow xrTableRow7;

		// Token: 0x04002146 RID: 8518
		public XRTableCell Cell_Filters;

		// Token: 0x04002147 RID: 8519
		private ObjectDataSource objectDataSource1;

		// Token: 0x04002148 RID: 8520
		private GroupHeaderBand GroupHeader1;

		// Token: 0x04002149 RID: 8521
		private XRTable xrTable3;

		// Token: 0x0400214A RID: 8522
		private XRTableRow xrTableRow9;

		// Token: 0x0400214B RID: 8523
		private XRTableCell xrTableCell5;

		// Token: 0x0400214C RID: 8524
		private XRTableCell xrTableCell7;

		// Token: 0x0400214D RID: 8525
		private XRTableCell xrTableCell8;

		// Token: 0x0400214E RID: 8526
		private XRTableCell xrTableCell9;

		// Token: 0x0400214F RID: 8527
		private XRTableCell xrTableCell10;

		// Token: 0x04002150 RID: 8528
		private XRTableCell xrTableCell11;

		// Token: 0x04002151 RID: 8529
		private XRTableCell xrTableCell12;

		// Token: 0x04002152 RID: 8530
		private XRTableCell xrTableCell14;

		// Token: 0x04002153 RID: 8531
		private XRTable xrTable4;

		// Token: 0x04002154 RID: 8532
		private XRTableRow xrTableRow10;

		// Token: 0x04002155 RID: 8533
		private XRTableCell xrTableCell15;

		// Token: 0x04002156 RID: 8534
		private XRTableCell xrTableCell16;

		// Token: 0x04002157 RID: 8535
		private XRTableCell xrTableCell17;

		// Token: 0x04002158 RID: 8536
		private XRTableCell xrTableCell18;

		// Token: 0x04002159 RID: 8537
		private XRTableCell xrTableCell19;

		// Token: 0x0400215A RID: 8538
		private XRTableCell xrTableCell20;

		// Token: 0x0400215B RID: 8539
		private XRTableCell xrTableCell21;

		// Token: 0x0400215C RID: 8540
		private XRTableCell xrTableCell22;

		// Token: 0x0400215D RID: 8541
		private GroupFooterBand GroupFooter1;

		// Token: 0x0400215E RID: 8542
		private XRTable xrTable5;

		// Token: 0x0400215F RID: 8543
		private XRTableRow xrTableRow11;

		// Token: 0x04002160 RID: 8544
		private XRTableCell xrTableCell23;

		// Token: 0x04002161 RID: 8545
		private XRTableCell xrTableCell27;

		// Token: 0x04002162 RID: 8546
		private XRTableCell xrTableCell28;

		// Token: 0x04002163 RID: 8547
		private XRTableCell xrTableCell29;

		// Token: 0x04002164 RID: 8548
		private XRTableCell xrTableCell30;

		// Token: 0x04002165 RID: 8549
		private XRControlStyle xrControlStyle1;
	}
}
