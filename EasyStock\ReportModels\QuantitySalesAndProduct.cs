﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class QuantitySalesAndProduct : CompanyInfoReportModel
    {
        [Display(Name = "N°")]
        public int Number { get; set; }

        [Display(Name = "Nom de l'article")]
        public int ProductName { get; set; }

        [Display(Name = "Quantité vendue en grande taille", GroupName = "Quantité vendue")]
        public double SalesQuantityHigh { get; set; }

        [Display(Name = "Quantité vendue en taille moyenne", GroupName = "Quantité vendue")]
        public double SalesQuantityAverage { get; set; }

        [Display(Name = "Quantité vendue en petite taille", GroupName = "Quantité vendue")]
        public double SalesQuantitySmall { get; set; }

        [Display(Name = "Quantité actuelle en grande taille", GroupName = "Quantité actuelle")]
        public double RealQuantityHigh { get; set; }

        [Display(Name = "Quantité actuelle en taille moyenne", GroupName = "Quantité actuelle")]
        public double RealQuantityAverage { get; set; }

        [Display(Name = "Quantité actuelle en petite taille", GroupName = "Quantité actuelle")]
        public double RealQuantitySmall { get; set; }
    }
}
