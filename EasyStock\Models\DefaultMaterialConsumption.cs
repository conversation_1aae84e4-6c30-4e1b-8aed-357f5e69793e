﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("DefaultMaterialConsumptions")]
    [DisplayColumn("Consommations de matériaux par défaut")]
    public class DefaultMaterialConsumption : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code de l'ordre")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int OrderID
        {
            get
            {
                return this.orderID;
            }
            set
            {
                base.SetProperty<int>(ref this.orderID, value, "OrderID");
            }
        }

        public WorkOrder Order { get; set; }

        [Display(Name = "Article")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int ProductID
        {
            get
            {
                return this.productID;
            }
            set
            {
                base.SetProperty<int>(ref this.productID, value, "ProductID");
            }
        }

        [Display(Name = "Unité")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int UintID
        {
            get
            {
                return this.uintID;
            }
            set
            {
                base.SetProperty<int>(ref this.uintID, value, "UintID");
            }
        }

        [Display(Name = "Quantité")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double Quantity
        {
            get
            {
                return this.quantity;
            }
            set
            {
                base.SetProperty<double>(ref this.quantity, value, "Quantity");
            }
        }

        [Display(Name = "Coût unitaire")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double UnitCost
        {
            get
            {
                return this.unitcost;
            }
            set
            {
                base.SetProperty<double>(ref this.unitcost, value, "UnitCost");
            }
        }

        [Display(Name = "Coût total")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double TotalCost
        {
            get
            {
                return this.totalcost;
            }
            set
            {
                base.SetProperty<double>(ref this.totalcost, value, "TotalCost");
            }
        }

        [Display(Name = "Solde actuel")]
        public double CurrentBalance
        {
            get
            {
                return this.currentBalance;
            }
            set
            {
                base.SetProperty<double>(ref this.currentBalance, value, "CurrentBalance");
            }
        }

        [Display(Name = "Manque")]
        public double Shtorage
        {
            get
            {
                return this.shtorage;
            }
            set
            {
                base.SetProperty<double>(ref this.shtorage, value, "Shtorage");
            }
        }

        private int id;

        private int orderID;

        private int productID;

        private int uintID;

        private double quantity;

        private double unitcost;

        private double totalcost;

        private double currentBalance;

        private double shtorage;
    }
}
