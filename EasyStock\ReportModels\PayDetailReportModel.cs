﻿using System;
using System.ComponentModel.DataAnnotations;
using EasyStock.Models;

namespace EasyStock.ReportModels
{
    public class PayDetailReportModel
    {
        [Display(Name = "Méthode de Paiement")]
        public PayMethodType MethodType { get; set; }

        [Display(Name = "Compte de Paiement")]
        public string MethodName { get; set; }

        [Display(Name = "Montant")]
        public double Amount { get; set; }

        [Display(Name = "Devise")]
        public string Currancy { get; set; }

        [Display(Name = "Taux de Change")]
        public double CurrancyRate { get; set; }

        [Display(Name = "Montant Local")]
        public double LocalAmount
        {
            get
            {
                return this.Amount * this.CurrancyRate;
            }
        }
    }
}
