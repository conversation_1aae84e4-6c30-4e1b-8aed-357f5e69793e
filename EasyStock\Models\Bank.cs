﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Banques")]
    [Table("Banks")]
    public class Bank : BillingDetail
    {
        [Display(Name = "Nom de la banque")]
        public override string Name
        {
            get
            {
                return base.Name;
            }
            set
            {
                base.Name = value;
            }
        }

        [Required]
        [Display(Name = "Numéro de compte bancaire")]
        public string BankAccountNumber { get; set; }

        [Display(Name = "Code SWIFT")]
        public string Swift { get; set; }

        [Display(Name = "IBAN (Numéro de compte bancaire international)")]
        public string EBAN { get; set; }

        public override string DisplayName
        {
            get
            {
                return string.Format("{0} - {1}", this.Name, this.BankAccountNumber);
            }
        }

        [Display(Name = "Adresse")]
        public string Address { get; set; }

        [StringLength(20)]
        [Display(Name = "Téléphone 1")]
        public string Phone1 { get; set; }

        [StringLength(20)]
        [Display(Name = "Téléphone 2")]
        public string Phone2 { get; set; }

        [StringLength(20)]
        [Display(Name = "Téléphone 3")]
        public string Phone3 { get; set; }
    }
}
