﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000343 RID: 835
	public partial class TotalPurchaseInvoicesReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x0600141E RID: 5150 RVA: 0x00166B84 File Offset: 0x00164D84
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600141F RID: 5151 RVA: 0x00166BBC File Offset: 0x00164DBC
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 53);
            this.documentViewer1.Size = new System.Drawing.Size(800, 376);
            // 
            // TotalPurchaseInvoicesReportView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Name = "TotalPurchaseInvoicesReportView";
            this.Text = "Total des factures d\'achat";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040019B2 RID: 6578
		private global::System.ComponentModel.IContainer components = null;
	}
}
