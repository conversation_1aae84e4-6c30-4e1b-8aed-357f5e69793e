﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.Models
{
	public enum CashLinkType
	{
        [Display(Name = "Non lié à une facture")]
        Non = -1,
        [Display(Name = "Ventes")]
        Sales = 4,
        [Display(Name = "Achats")]
        Purchase = 2,
        [Display(Name = "Retour des ventes")]
        SalesReturn = 5,
        [Display(Name = "Retour des achats")]
        PurchaseReturn = 3,
        [Display(Name = "Petite caisse")]
        PettyCash = 20
    }
}
