﻿using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using DevExpress.XtraLayout.Utils;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Controls;
using EasyStock.MainViews;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;

namespace EasyStock.ReportViews
{
    public partial class FilterForm : XtraForm
    {
        public bool IsCanceld = true;
        public bool hasStartDate => FromDate.DateTime.Year > 1950;

        public bool hasEndtDate => ToDate.DateTime.Year > 1950;

        public bool hasStores
        {
            get
            {
                GridPopupContainerControl stores = Stores;
                return stores != null && stores.EditValue?.Count() > 0;
            }
        }

        public bool hasProducts
        {
            get
            {
                GridPopupContainerControl products = Products;
                return products != null && products.EditValue?.Count() > 0;
            }
        }

        public bool hasVendors
        {
            get
            {
                GridPopupContainerControl vendors = Vendors;
                return vendors != null && vendors.EditValue?.Count() > 0;
            }
        }

        public DateTime startDate
        {
            get
            {
                if (hasStartDate)
                {
                    return FromDate.DateTime.Date;
                }
                return DateTime.Now.Date;
            }
        }

        public DateTime endtDate
        {
            get
            {
                if (hasEndtDate)
                {
                    return ToDate.DateTime.Date;
                }
                return DateTime.Now.Date;
            }
        }

        public int[] SelectedStores => Stores?.EditValue?.ToArray();

        public int[] SelectedProducts => Products?.EditValue?.ToArray();

        public int ProductID => (Products?.EditValue?.ToList()?.FirstOrDefault()).GetValueOrDefault();

        public int AccountID => (Accounts?.EditValue?.ToList()?.FirstOrDefault()).GetValueOrDefault();

        public int CustomerID => (Customers?.EditValue?.ToList()?.FirstOrDefault()).GetValueOrDefault();

        public int VendorID => (Vendors?.EditValue?.ToList()?.FirstOrDefault()).GetValueOrDefault();

        public BaseReportForm ReportForm { get; set; }

        public string FilterText
        {
            get
            {
                List<string> text = new List<string>();
                foreach (object item in Root.Items)
                {
                    if (item is LayoutControlItem controlItem && controlItem.Visible && controlItem.Control is BaseEdit edit)
                    {
                        text.Add(controlItem.Text + " : " + edit.Text);
                    }
                }
                return string.Join(", ", text);
            }
        }

        public new event EventHandler Show;

        public FilterForm(ReportFilter[] Filters)
        {
            InitializeComponent();
            setFilters(Filters);
            base.FormClosing += FilterForm_FormClosing;
        }

        public FilterForm(BaseReportForm _ReportForm)
        {
            InitializeComponent();
            base.ShowInTaskbar = false;
            ReportForm = _ReportForm;
            ReportName.Text = ReportForm.Text;
            setFilters(ReportForm.Filters);
            base.FormClosing += FilterForm_FormClosing;
        }

        private void setFilters(ReportFilter[] Filters)
        {
            ItemForAccounts.Visibility = ((!Filters.Contains(ReportFilter.Account)) ? LayoutVisibility.Never : LayoutVisibility.Always);
            ItemForCustomers.Visibility = ((!Filters.Contains(ReportFilter.Customer)) ? LayoutVisibility.Never : LayoutVisibility.Always);
            ItemForVendors.Visibility = ((!Filters.Contains(ReportFilter.Vendor)) ? LayoutVisibility.Never : LayoutVisibility.Always);
            ItemForProducts.Visibility = ((!Filters.Contains(ReportFilter.Product)) ? LayoutVisibility.Never : LayoutVisibility.Always);
            ItemForStore.Visibility = ((!Filters.Contains(ReportFilter.Store)) ? LayoutVisibility.Never : LayoutVisibility.Always);
            ItemForCostCenter.Visibility = ((!Filters.Contains(ReportFilter.CostCenter)) ? LayoutVisibility.Never : LayoutVisibility.Always);
            if (Filters.Contains(ReportFilter.Date))
            {
                LayoutControlItem itemForToDate = ItemForToDate;
                LayoutVisibility visibility = (ItemForFromDate.Visibility = LayoutVisibility.Always);
                itemForToDate.Visibility = visibility;
                DateEdit fromDate = FromDate;
                object editValue = (ToDate.EditValue = DateTime.Now);
                fromDate.EditValue = editValue;
            }
            else
            {
                ItemForToDate.Visibility = LayoutVisibility.Never;
            }
            using (ERPDataContext db = new ERPDataContext())
            {
                Accounts.DataSource = CurrentSession.AccessableAccounts.ToList();
                Customers.DataSource = db.Customers.ToList();
                Vendors.DataSource = db.Vendors.ToList();
                Stores.DataSource = db.Stores.ToList();
                Products.DataSource = CurrentSession.AccessableProducts;
                CostCenter.DataSource = db.CostCenters.Select((CostCenter x) => new { x.ID, x.Name }).ToList();
            }
            AddColumns(Accounts.gridView, "Note", "Name", "ID");
            AddColumns(Stores.gridView, "Name", "ID");
            AddColumns(Products.gridView, "Name", "ID");
            AddColumns(CostCenter.gridView, "Name", "ID");
            AddColumns(Customers.gridView, "Mobile", "Phone", "Name", "ID");
            AddColumns(Vendors.gridView, "Mobile", "Phone", "Name", "ID");
        }

        private void AddColumns(GridView view, params string[] columns)
        {
            view.Columns.Clear();
            foreach (string columnName in columns)
            {
                GridColumn column = view.Columns.AddField(columnName);
                column.VisibleIndex = 0;
                column.BestFit();
            }
        }

        private void FilterForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ReportForm != null && !ReportForm.IsDisposed)
            {
                e.Cancel = true;
                base.Visible = false;
                Hide();
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            if (this.Show != null)
            {
                this.Show?.Invoke(this, e);
            }
            else if (ReportForm.ValidateFilters())
            {
                IsCanceld = false;
                ReportForm.RefreshDataSource();
                Close();
            }
        }
    }
}
