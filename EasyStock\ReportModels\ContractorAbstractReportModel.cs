﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class ContractorAbstractReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Numéro")]
        public int ID { get; set; }

        [Display(Name = "Code")]
        public string Code { get; set; }

        [Display(Name = "Nom du prestataire")]
        public string ContractorNae { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Caisse")]
        public string DrawerName { get; set; }

        [Display(Name = "Type de travail")]
        public string Type { get; set; }

        [Display(Name = "Remarques")]
        public string Notes { get; set; }

        [Display(Name = "Montant")]
        public double Ammount { get; set; }

        [Display(Name = "Payé")]
        public double Paid { get; set; }

        [Display(Name = "Restant")]
        public double Remaining
        {
            get
            {
                return this.Ammount - this.Paid;
            }
        }
    }
}
