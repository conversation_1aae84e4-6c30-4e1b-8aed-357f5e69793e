﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class ProductStoreValidityReprtModel : CompanyInfoReportModel
    {
        [Display(Name = "Code article")]
        public int ProductID { get; set; }

        [Display(Name = "Article")]
        public string ProductName { get; set; }

        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Validité")]
        public DateTime Date { get; set; }

        [Display(Name = "Solde")]
        public double Balance { get; set; }

        [Display(Name = "Jours restants")]
        public int RemainingDays
        {
            get
            {
                return (int)(this.Date - DateTime.Now).TotalDays;
            }
        }
    }
}
