﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class PurchaseTaxsReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Code de la facture")]
        public string InvoicesCode { get; set; }

        [Display(Name = "Numéro fiscal du client")]
        public string TaxFileNumber { get; set; }

        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Date")]
        public DateTime InvoiceDate { get; set; }

        [Display(Name = "Total")]
        public double Total { get; set; }

        [Display(Name = "Montant de la remise")]
        public double Discount { get; set; }

        [Display(Name = "Montant de la tva")]
        public double TaxAmount { get; set; }

        [Display(Name = "Net")]
        public double Net { get; set; }
    }
}
