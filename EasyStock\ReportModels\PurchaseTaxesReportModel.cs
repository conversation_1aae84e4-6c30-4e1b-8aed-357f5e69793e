﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class PurchaseTaxesReportModel : CompanyInfoReportModel
    {
        [Display(Name = "Code de la facture")]
        public string InvoicesCode { get; set; }

        [Display(Name = "Numéro fiscal")]
        public string TaxFileNumber { get; set; }

        [Display(Name = "Nom")]
        public string Name { get; set; }

        [Display(Name = "Date")]
        public DateTime InvoiceDate { get; set; }

        [Display(Name = "Montant")]
        public double Total { get; set; }

        [Display(Name = "Remise")]
        public double Discount { get; set; }

        [Display(Name = "Taxe")]
        public double TaxAmount { get; set; }

        [Display(Name = "Net")]
        public double Net { get; set; }
    }
}
