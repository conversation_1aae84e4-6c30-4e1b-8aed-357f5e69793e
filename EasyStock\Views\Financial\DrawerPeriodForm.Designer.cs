﻿namespace EasyStock.Views.Financial
{
	// Token: 0x020002F0 RID: 752
	public partial class DrawerPeriodForm : global::EasyStock.MainViews.MasterForm
	{
		// Token: 0x060012DB RID: 4827 RVA: 0x001482D8 File Offset: 0x001464D8
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x060012DC RID: 4828 RVA: 0x00148310 File Offset: 0x00146510
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.dataLayoutControl1 = new DevExpress.XtraDataLayout.DataLayoutControl();
            this.ClosePeriodButton = new DevExpress.XtraEditors.SimpleButton();
            this.OpenPeriodButton = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.summeryBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.drawerPeriodBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colProcessType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colProcessCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colProcessSum = new DevExpress.XtraGrid.Columns.GridColumn();
            this.IDTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.PeriodStartDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.PeriodEndDateEdit = new DevExpress.XtraEditors.DateEdit();
            this.OpeningBalanceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ClosingBalanceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.ActualBalanceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.BalanceDifferenceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.TransferdBalanceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.RemainingBalanceTextEdit = new DevExpress.XtraEditors.TextEdit();
            this.PeriodUserIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.ClosingPeriodUserIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.DifferenceAccountIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.DrawerIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.ClosingDrwerIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.BranchIDLookUpEdit = new DevExpress.XtraEditors.LookUpEdit();
            this.Root = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlGroup4 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.layoutControlItem1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForClosingBalance = new DevExpress.XtraLayout.LayoutControlItem();
            this.lyc_ClosingGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForPeriodEnd = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForClosingPeriodUserID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForBalanceDifference = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForDifferenceAccountID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForClosingDrwerID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForTransferdBalance = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForRemainingBalance = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForActualBalance = new DevExpress.XtraLayout.LayoutControlItem();
            this.lyc_ClosePeriodButton = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem1 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lyc_OpeningGroup = new DevExpress.XtraLayout.LayoutControlGroup();
            this.ItemForDrawerID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForPeriodStart = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForPeriodUserID = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForOpeningBalance = new DevExpress.XtraLayout.LayoutControlItem();
            this.ItemForID = new DevExpress.XtraLayout.LayoutControlItem();
            this.lyc_OpenPeriodButton = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem2 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.ItemForBranchID = new DevExpress.XtraLayout.LayoutControlItem();
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).BeginInit();
            this.dataLayoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.summeryBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.drawerPeriodBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodStartDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodStartDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodEndDateEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodEndDateEdit.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.OpeningBalanceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ClosingBalanceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ActualBalanceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BalanceDifferenceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.TransferdBalanceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RemainingBalanceTextEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodUserIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ClosingPeriodUserIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DifferenceAccountIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.DrawerIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ClosingDrwerIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchIDLookUpEdit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForClosingBalance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_ClosingGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPeriodEnd)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForClosingPeriodUserID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBalanceDifference)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDifferenceAccountID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForClosingDrwerID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForTransferdBalance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRemainingBalance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForActualBalance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_ClosePeriodButton)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_OpeningGroup)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDrawerID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPeriodStart)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPeriodUserID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOpeningBalance)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_OpenPeriodButton)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranchID)).BeginInit();
            this.SuspendLayout();
            // 
            // dataLayoutControl1
            // 
            this.dataLayoutControl1.Controls.Add(this.ClosePeriodButton);
            this.dataLayoutControl1.Controls.Add(this.OpenPeriodButton);
            this.dataLayoutControl1.Controls.Add(this.gridControl1);
            this.dataLayoutControl1.Controls.Add(this.IDTextEdit);
            this.dataLayoutControl1.Controls.Add(this.PeriodStartDateEdit);
            this.dataLayoutControl1.Controls.Add(this.PeriodEndDateEdit);
            this.dataLayoutControl1.Controls.Add(this.OpeningBalanceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ClosingBalanceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.ActualBalanceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.BalanceDifferenceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.TransferdBalanceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.RemainingBalanceTextEdit);
            this.dataLayoutControl1.Controls.Add(this.PeriodUserIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.ClosingPeriodUserIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.DifferenceAccountIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.DrawerIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.ClosingDrwerIDLookUpEdit);
            this.dataLayoutControl1.Controls.Add(this.BranchIDLookUpEdit);
            this.dataLayoutControl1.DataSource = this.drawerPeriodBindingSource;
            this.dataLayoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dataLayoutControl1.Location = new System.Drawing.Point(0, 27);
            this.dataLayoutControl1.Name = "dataLayoutControl1";
            this.dataLayoutControl1.Root = this.Root;
            this.dataLayoutControl1.Size = new System.Drawing.Size(998, 520);
            this.dataLayoutControl1.TabIndex = 4;
            this.dataLayoutControl1.Text = "dataLayoutControl1";
            // 
            // ClosePeriodButton
            // 
            this.ClosePeriodButton.ImageOptions.SvgImage = global::EasyStock.Properties.Resources._private;
            this.ClosePeriodButton.Location = new System.Drawing.Point(676, 237);
            this.ClosePeriodButton.Name = "ClosePeriodButton";
            this.ClosePeriodButton.Size = new System.Drawing.Size(298, 36);
            this.ClosePeriodButton.StyleController = this.dataLayoutControl1;
            this.ClosePeriodButton.TabIndex = 25;
            this.ClosePeriodButton.Text = "Clôture de la période";
            // 
            // OpenPeriodButton
            // 
            this.OpenPeriodButton.ImageOptions.SvgImage = global::EasyStock.Properties.Resources.security_unlock;
            this.OpenPeriodButton.Location = new System.Drawing.Point(24, 189);
            this.OpenPeriodButton.Name = "OpenPeriodButton";
            this.OpenPeriodButton.Size = new System.Drawing.Size(298, 36);
            this.OpenPeriodButton.StyleController = this.dataLayoutControl1;
            this.OpenPeriodButton.TabIndex = 19;
            this.OpenPeriodButton.Text = "Ouverture de la période";
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.summeryBindingSource;
            this.gridControl1.Location = new System.Drawing.Point(350, 45);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.Size = new System.Drawing.Size(298, 427);
            this.gridControl1.TabIndex = 18;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // summeryBindingSource
            // 
            this.summeryBindingSource.DataMember = "Summery";
            this.summeryBindingSource.DataSource = this.drawerPeriodBindingSource;
            // 
            // drawerPeriodBindingSource
            // 
            this.drawerPeriodBindingSource.DataSource = typeof(EasyStock.Models.DrawerPeriod);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colProcessType,
            this.colProcessCount,
            this.colProcessSum});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.ReadOnly = true;
            this.gridView1.OptionsCustomization.AllowSort = false;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // colProcessType
            // 
            this.colProcessType.FieldName = "ProcessType";
            this.colProcessType.Name = "colProcessType";
            this.colProcessType.Visible = true;
            this.colProcessType.VisibleIndex = 0;
            // 
            // colProcessCount
            // 
            this.colProcessCount.FieldName = "ProcessCount";
            this.colProcessCount.Name = "colProcessCount";
            this.colProcessCount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "ProcessCount", "{0:0.##}")});
            this.colProcessCount.Visible = true;
            this.colProcessCount.VisibleIndex = 1;
            // 
            // colProcessSum
            // 
            this.colProcessSum.FieldName = "ProcessSum";
            this.colProcessSum.Name = "colProcessSum";
            this.colProcessSum.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(DevExpress.Data.SummaryItemType.Sum, "ProcessSum", "{0:0.##}")});
            this.colProcessSum.Visible = true;
            this.colProcessSum.VisibleIndex = 2;
            // 
            // IDTextEdit
            // 
            this.IDTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "ID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.IDTextEdit.Location = new System.Drawing.Point(152, 45);
            this.IDTextEdit.Name = "IDTextEdit";
            this.IDTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.IDTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.IDTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.IDTextEdit.Properties.MaskSettings.Set("mask", "d");
            this.IDTextEdit.Properties.MaskSettings.Set("MaskManagerSignature", "allowNull=False");
            this.IDTextEdit.Properties.ReadOnly = true;
            this.IDTextEdit.Size = new System.Drawing.Size(170, 20);
            this.IDTextEdit.StyleController = this.dataLayoutControl1;
            this.IDTextEdit.TabIndex = 4;
            // 
            // PeriodStartDateEdit
            // 
            this.PeriodStartDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "PeriodStart", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PeriodStartDateEdit.EditValue = null;
            this.PeriodStartDateEdit.Location = new System.Drawing.Point(152, 117);
            this.PeriodStartDateEdit.Name = "PeriodStartDateEdit";
            this.PeriodStartDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.PeriodStartDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.PeriodStartDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.PeriodStartDateEdit.Properties.MaskSettings.Set("mask", "f");
            this.PeriodStartDateEdit.Properties.MaskSettings.Set("culture", "ar-EG");
            this.PeriodStartDateEdit.Properties.UseMaskAsDisplayFormat = true;
            this.PeriodStartDateEdit.Size = new System.Drawing.Size(170, 20);
            this.PeriodStartDateEdit.StyleController = this.dataLayoutControl1;
            this.PeriodStartDateEdit.TabIndex = 5;
            // 
            // PeriodEndDateEdit
            // 
            this.PeriodEndDateEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "PeriodEnd", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PeriodEndDateEdit.EditValue = null;
            this.PeriodEndDateEdit.Location = new System.Drawing.Point(804, 45);
            this.PeriodEndDateEdit.Name = "PeriodEndDateEdit";
            this.PeriodEndDateEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.PeriodEndDateEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.PeriodEndDateEdit.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.PeriodEndDateEdit.Properties.MaskSettings.Set("mask", "F");
            this.PeriodEndDateEdit.Properties.MaskSettings.Set("culture", "ar-EG");
            this.PeriodEndDateEdit.Properties.UseMaskAsDisplayFormat = true;
            this.PeriodEndDateEdit.Size = new System.Drawing.Size(170, 20);
            this.PeriodEndDateEdit.StyleController = this.dataLayoutControl1;
            this.PeriodEndDateEdit.TabIndex = 6;
            // 
            // OpeningBalanceTextEdit
            // 
            this.OpeningBalanceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "OpeningBalance", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.OpeningBalanceTextEdit.Location = new System.Drawing.Point(152, 165);
            this.OpeningBalanceTextEdit.Name = "OpeningBalanceTextEdit";
            this.OpeningBalanceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.OpeningBalanceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.OpeningBalanceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.OpeningBalanceTextEdit.Properties.MaskSettings.Set("MaskManagerType", typeof(DevExpress.Data.Mask.NumericMaskManager));
            this.OpeningBalanceTextEdit.Properties.MaskSettings.Set("mask", "F");
            this.OpeningBalanceTextEdit.Properties.ReadOnly = true;
            this.OpeningBalanceTextEdit.Size = new System.Drawing.Size(170, 20);
            this.OpeningBalanceTextEdit.StyleController = this.dataLayoutControl1;
            this.OpeningBalanceTextEdit.TabIndex = 7;
            // 
            // ClosingBalanceTextEdit
            // 
            this.ClosingBalanceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "ClosingBalance", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ClosingBalanceTextEdit.Location = new System.Drawing.Point(478, 476);
            this.ClosingBalanceTextEdit.Name = "ClosingBalanceTextEdit";
            this.ClosingBalanceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ClosingBalanceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ClosingBalanceTextEdit.Properties.Mask.EditMask = "F";
            this.ClosingBalanceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.ClosingBalanceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.ClosingBalanceTextEdit.Size = new System.Drawing.Size(170, 20);
            this.ClosingBalanceTextEdit.StyleController = this.dataLayoutControl1;
            this.ClosingBalanceTextEdit.TabIndex = 8;
            // 
            // ActualBalanceTextEdit
            // 
            this.ActualBalanceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "ActualBalance", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ActualBalanceTextEdit.Location = new System.Drawing.Point(804, 93);
            this.ActualBalanceTextEdit.Name = "ActualBalanceTextEdit";
            this.ActualBalanceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ActualBalanceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ActualBalanceTextEdit.Properties.Mask.EditMask = "F";
            this.ActualBalanceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.ActualBalanceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.ActualBalanceTextEdit.Size = new System.Drawing.Size(170, 20);
            this.ActualBalanceTextEdit.StyleController = this.dataLayoutControl1;
            this.ActualBalanceTextEdit.TabIndex = 9;
            // 
            // BalanceDifferenceTextEdit
            // 
            this.BalanceDifferenceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "BalanceDifference", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BalanceDifferenceTextEdit.Location = new System.Drawing.Point(804, 117);
            this.BalanceDifferenceTextEdit.Name = "BalanceDifferenceTextEdit";
            this.BalanceDifferenceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BalanceDifferenceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BalanceDifferenceTextEdit.Properties.Mask.EditMask = "F";
            this.BalanceDifferenceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.BalanceDifferenceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.BalanceDifferenceTextEdit.Properties.ReadOnly = true;
            this.BalanceDifferenceTextEdit.Size = new System.Drawing.Size(170, 20);
            this.BalanceDifferenceTextEdit.StyleController = this.dataLayoutControl1;
            this.BalanceDifferenceTextEdit.TabIndex = 10;
            // 
            // TransferdBalanceTextEdit
            // 
            this.TransferdBalanceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "TransferdBalance", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.TransferdBalanceTextEdit.Location = new System.Drawing.Point(804, 189);
            this.TransferdBalanceTextEdit.Name = "TransferdBalanceTextEdit";
            this.TransferdBalanceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.TransferdBalanceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.TransferdBalanceTextEdit.Properties.Mask.EditMask = "F";
            this.TransferdBalanceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.TransferdBalanceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.TransferdBalanceTextEdit.Size = new System.Drawing.Size(170, 20);
            this.TransferdBalanceTextEdit.StyleController = this.dataLayoutControl1;
            this.TransferdBalanceTextEdit.TabIndex = 16;
            // 
            // RemainingBalanceTextEdit
            // 
            this.RemainingBalanceTextEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "RemainingBalance", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.RemainingBalanceTextEdit.Location = new System.Drawing.Point(804, 213);
            this.RemainingBalanceTextEdit.Name = "RemainingBalanceTextEdit";
            this.RemainingBalanceTextEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.RemainingBalanceTextEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.RemainingBalanceTextEdit.Properties.Mask.EditMask = "F";
            this.RemainingBalanceTextEdit.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric;
            this.RemainingBalanceTextEdit.Properties.Mask.UseMaskAsDisplayFormat = true;
            this.RemainingBalanceTextEdit.Size = new System.Drawing.Size(170, 20);
            this.RemainingBalanceTextEdit.StyleController = this.dataLayoutControl1;
            this.RemainingBalanceTextEdit.TabIndex = 17;
            // 
            // PeriodUserIDLookUpEdit
            // 
            this.PeriodUserIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "PeriodUserID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.PeriodUserIDLookUpEdit.Location = new System.Drawing.Point(152, 141);
            this.PeriodUserIDLookUpEdit.Name = "PeriodUserIDLookUpEdit";
            this.PeriodUserIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.PeriodUserIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.PeriodUserIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.PeriodUserIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.PeriodUserIDLookUpEdit.Properties.NullText = "";
            this.PeriodUserIDLookUpEdit.Properties.ReadOnly = true;
            this.PeriodUserIDLookUpEdit.Size = new System.Drawing.Size(170, 20);
            this.PeriodUserIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.PeriodUserIDLookUpEdit.TabIndex = 21;
            // 
            // ClosingPeriodUserIDLookUpEdit
            // 
            this.ClosingPeriodUserIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "ClosingPeriodUserID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ClosingPeriodUserIDLookUpEdit.Location = new System.Drawing.Point(804, 69);
            this.ClosingPeriodUserIDLookUpEdit.Name = "ClosingPeriodUserIDLookUpEdit";
            this.ClosingPeriodUserIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.ClosingPeriodUserIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ClosingPeriodUserIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ClosingPeriodUserIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ClosingPeriodUserIDLookUpEdit.Properties.NullText = "";
            this.ClosingPeriodUserIDLookUpEdit.Size = new System.Drawing.Size(170, 20);
            this.ClosingPeriodUserIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.ClosingPeriodUserIDLookUpEdit.TabIndex = 22;
            // 
            // DifferenceAccountIDLookUpEdit
            // 
            this.DifferenceAccountIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "DifferenceAccountID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DifferenceAccountIDLookUpEdit.Location = new System.Drawing.Point(804, 141);
            this.DifferenceAccountIDLookUpEdit.Name = "DifferenceAccountIDLookUpEdit";
            this.DifferenceAccountIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.DifferenceAccountIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DifferenceAccountIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DifferenceAccountIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DifferenceAccountIDLookUpEdit.Properties.NullText = "";
            this.DifferenceAccountIDLookUpEdit.Size = new System.Drawing.Size(170, 20);
            this.DifferenceAccountIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.DifferenceAccountIDLookUpEdit.TabIndex = 20;
            // 
            // DrawerIDLookUpEdit
            // 
            this.DrawerIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "DrawerID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.DrawerIDLookUpEdit.Location = new System.Drawing.Point(152, 93);
            this.DrawerIDLookUpEdit.Name = "DrawerIDLookUpEdit";
            this.DrawerIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.DrawerIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.DrawerIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.DrawerIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.DrawerIDLookUpEdit.Properties.NullText = "";
            this.DrawerIDLookUpEdit.Size = new System.Drawing.Size(170, 20);
            this.DrawerIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.DrawerIDLookUpEdit.TabIndex = 23;
            // 
            // ClosingDrwerIDLookUpEdit
            // 
            this.ClosingDrwerIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "ClosingDrwerID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ClosingDrwerIDLookUpEdit.Location = new System.Drawing.Point(804, 165);
            this.ClosingDrwerIDLookUpEdit.Name = "ClosingDrwerIDLookUpEdit";
            this.ClosingDrwerIDLookUpEdit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.ClosingDrwerIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.ClosingDrwerIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.ClosingDrwerIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.ClosingDrwerIDLookUpEdit.Properties.NullText = "";
            this.ClosingDrwerIDLookUpEdit.Size = new System.Drawing.Size(170, 20);
            this.ClosingDrwerIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.ClosingDrwerIDLookUpEdit.TabIndex = 24;
            // 
            // BranchIDLookUpEdit
            // 
            this.BranchIDLookUpEdit.DataBindings.Add(new System.Windows.Forms.Binding("EditValue", this.drawerPeriodBindingSource, "BranchID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.BranchIDLookUpEdit.Location = new System.Drawing.Point(152, 69);
            this.BranchIDLookUpEdit.Name = "BranchIDLookUpEdit";
            this.BranchIDLookUpEdit.Properties.Appearance.Options.UseTextOptions = true;
            this.BranchIDLookUpEdit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.BranchIDLookUpEdit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.BranchIDLookUpEdit.Properties.NullText = "";
            this.BranchIDLookUpEdit.Size = new System.Drawing.Size(170, 20);
            this.BranchIDLookUpEdit.StyleController = this.dataLayoutControl1;
            this.BranchIDLookUpEdit.TabIndex = 26;
            // 
            // Root
            // 
            this.Root.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.Root.GroupBordersVisible = false;
            this.Root.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup1});
            this.Root.Name = "Root";
            this.Root.Size = new System.Drawing.Size(998, 520);
            this.Root.TextVisible = false;
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.AllowDrawBackground = false;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlGroup4,
            this.lyc_ClosingGroup,
            this.lyc_OpeningGroup});
            this.layoutControlGroup1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlGroup1.Name = "autoGeneratedGroup0";
            this.layoutControlGroup1.Size = new System.Drawing.Size(978, 500);
            // 
            // layoutControlGroup4
            // 
            this.layoutControlGroup4.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.layoutControlItem1,
            this.ItemForClosingBalance});
            this.layoutControlGroup4.Location = new System.Drawing.Point(326, 0);
            this.layoutControlGroup4.Name = "layoutControlGroup4";
            this.layoutControlGroup4.Size = new System.Drawing.Size(326, 500);
            this.layoutControlGroup4.Text = "Mouvements de la période";
            // 
            // layoutControlItem1
            // 
            this.layoutControlItem1.Control = this.gridControl1;
            this.layoutControlItem1.Location = new System.Drawing.Point(0, 0);
            this.layoutControlItem1.Name = "layoutControlItem1";
            this.layoutControlItem1.Size = new System.Drawing.Size(302, 431);
            this.layoutControlItem1.TextSize = new System.Drawing.Size(0, 0);
            this.layoutControlItem1.TextVisible = false;
            // 
            // ItemForClosingBalance
            // 
            this.ItemForClosingBalance.Control = this.ClosingBalanceTextEdit;
            this.ItemForClosingBalance.Location = new System.Drawing.Point(0, 431);
            this.ItemForClosingBalance.Name = "ItemForClosingBalance";
            this.ItemForClosingBalance.Size = new System.Drawing.Size(302, 24);
            this.ItemForClosingBalance.TextSize = new System.Drawing.Size(116, 13);
            // 
            // lyc_ClosingGroup
            // 
            this.lyc_ClosingGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForPeriodEnd,
            this.ItemForClosingPeriodUserID,
            this.ItemForBalanceDifference,
            this.ItemForDifferenceAccountID,
            this.ItemForClosingDrwerID,
            this.ItemForTransferdBalance,
            this.ItemForRemainingBalance,
            this.ItemForActualBalance,
            this.lyc_ClosePeriodButton,
            this.emptySpaceItem1});
            this.lyc_ClosingGroup.Location = new System.Drawing.Point(652, 0);
            this.lyc_ClosingGroup.Name = "lyc_ClosingGroup";
            this.lyc_ClosingGroup.Size = new System.Drawing.Size(326, 500);
            this.lyc_ClosingGroup.Text = "Clôture de la période";
            // 
            // ItemForPeriodEnd
            // 
            this.ItemForPeriodEnd.Control = this.PeriodEndDateEdit;
            this.ItemForPeriodEnd.Location = new System.Drawing.Point(0, 0);
            this.ItemForPeriodEnd.Name = "ItemForPeriodEnd";
            this.ItemForPeriodEnd.Size = new System.Drawing.Size(302, 24);
            this.ItemForPeriodEnd.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForClosingPeriodUserID
            // 
            this.ItemForClosingPeriodUserID.Control = this.ClosingPeriodUserIDLookUpEdit;
            this.ItemForClosingPeriodUserID.Location = new System.Drawing.Point(0, 24);
            this.ItemForClosingPeriodUserID.Name = "ItemForClosingPeriodUserID";
            this.ItemForClosingPeriodUserID.Size = new System.Drawing.Size(302, 24);
            this.ItemForClosingPeriodUserID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForBalanceDifference
            // 
            this.ItemForBalanceDifference.Control = this.BalanceDifferenceTextEdit;
            this.ItemForBalanceDifference.Location = new System.Drawing.Point(0, 72);
            this.ItemForBalanceDifference.Name = "ItemForBalanceDifference";
            this.ItemForBalanceDifference.Size = new System.Drawing.Size(302, 24);
            this.ItemForBalanceDifference.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForDifferenceAccountID
            // 
            this.ItemForDifferenceAccountID.Control = this.DifferenceAccountIDLookUpEdit;
            this.ItemForDifferenceAccountID.Location = new System.Drawing.Point(0, 96);
            this.ItemForDifferenceAccountID.Name = "ItemForDifferenceAccountID";
            this.ItemForDifferenceAccountID.Size = new System.Drawing.Size(302, 24);
            this.ItemForDifferenceAccountID.Text = "Compte de régularisation";
            this.ItemForDifferenceAccountID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForClosingDrwerID
            // 
            this.ItemForClosingDrwerID.Control = this.ClosingDrwerIDLookUpEdit;
            this.ItemForClosingDrwerID.Location = new System.Drawing.Point(0, 120);
            this.ItemForClosingDrwerID.Name = "ItemForClosingDrwerID";
            this.ItemForClosingDrwerID.Size = new System.Drawing.Size(302, 24);
            this.ItemForClosingDrwerID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForTransferdBalance
            // 
            this.ItemForTransferdBalance.Control = this.TransferdBalanceTextEdit;
            this.ItemForTransferdBalance.Location = new System.Drawing.Point(0, 144);
            this.ItemForTransferdBalance.Name = "ItemForTransferdBalance";
            this.ItemForTransferdBalance.Size = new System.Drawing.Size(302, 24);
            this.ItemForTransferdBalance.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForRemainingBalance
            // 
            this.ItemForRemainingBalance.Control = this.RemainingBalanceTextEdit;
            this.ItemForRemainingBalance.Location = new System.Drawing.Point(0, 168);
            this.ItemForRemainingBalance.Name = "ItemForRemainingBalance";
            this.ItemForRemainingBalance.Size = new System.Drawing.Size(302, 24);
            this.ItemForRemainingBalance.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForActualBalance
            // 
            this.ItemForActualBalance.Control = this.ActualBalanceTextEdit;
            this.ItemForActualBalance.Location = new System.Drawing.Point(0, 48);
            this.ItemForActualBalance.Name = "ItemForActualBalance";
            this.ItemForActualBalance.Size = new System.Drawing.Size(302, 24);
            this.ItemForActualBalance.TextSize = new System.Drawing.Size(116, 13);
            // 
            // lyc_ClosePeriodButton
            // 
            this.lyc_ClosePeriodButton.Control = this.ClosePeriodButton;
            this.lyc_ClosePeriodButton.Location = new System.Drawing.Point(0, 192);
            this.lyc_ClosePeriodButton.Name = "lyc_ClosePeriodButton";
            this.lyc_ClosePeriodButton.Size = new System.Drawing.Size(302, 40);
            this.lyc_ClosePeriodButton.TextSize = new System.Drawing.Size(0, 0);
            this.lyc_ClosePeriodButton.TextVisible = false;
            // 
            // emptySpaceItem1
            // 
            this.emptySpaceItem1.AllowHotTrack = false;
            this.emptySpaceItem1.Location = new System.Drawing.Point(0, 232);
            this.emptySpaceItem1.Name = "emptySpaceItem1";
            this.emptySpaceItem1.Size = new System.Drawing.Size(302, 223);
            this.emptySpaceItem1.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lyc_OpeningGroup
            // 
            this.lyc_OpeningGroup.CustomizationFormText = "Début de la période";
            this.lyc_OpeningGroup.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.ItemForDrawerID,
            this.ItemForPeriodStart,
            this.ItemForPeriodUserID,
            this.ItemForOpeningBalance,
            this.ItemForID,
            this.lyc_OpenPeriodButton,
            this.emptySpaceItem2,
            this.ItemForBranchID});
            this.lyc_OpeningGroup.Location = new System.Drawing.Point(0, 0);
            this.lyc_OpeningGroup.Name = "lyc_OpeningGroup";
            this.lyc_OpeningGroup.Size = new System.Drawing.Size(326, 500);
            this.lyc_OpeningGroup.Text = "Début de la période";
            // 
            // ItemForDrawerID
            // 
            this.ItemForDrawerID.Control = this.DrawerIDLookUpEdit;
            this.ItemForDrawerID.Location = new System.Drawing.Point(0, 48);
            this.ItemForDrawerID.Name = "ItemForDrawerID";
            this.ItemForDrawerID.Size = new System.Drawing.Size(302, 24);
            this.ItemForDrawerID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForPeriodStart
            // 
            this.ItemForPeriodStart.Control = this.PeriodStartDateEdit;
            this.ItemForPeriodStart.Location = new System.Drawing.Point(0, 72);
            this.ItemForPeriodStart.Name = "ItemForPeriodStart";
            this.ItemForPeriodStart.Size = new System.Drawing.Size(302, 24);
            this.ItemForPeriodStart.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForPeriodUserID
            // 
            this.ItemForPeriodUserID.Control = this.PeriodUserIDLookUpEdit;
            this.ItemForPeriodUserID.Location = new System.Drawing.Point(0, 96);
            this.ItemForPeriodUserID.Name = "ItemForPeriodUserID";
            this.ItemForPeriodUserID.Size = new System.Drawing.Size(302, 24);
            this.ItemForPeriodUserID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForOpeningBalance
            // 
            this.ItemForOpeningBalance.Control = this.OpeningBalanceTextEdit;
            this.ItemForOpeningBalance.Location = new System.Drawing.Point(0, 120);
            this.ItemForOpeningBalance.Name = "ItemForOpeningBalance";
            this.ItemForOpeningBalance.Size = new System.Drawing.Size(302, 24);
            this.ItemForOpeningBalance.TextSize = new System.Drawing.Size(116, 13);
            // 
            // ItemForID
            // 
            this.ItemForID.Control = this.IDTextEdit;
            this.ItemForID.Location = new System.Drawing.Point(0, 0);
            this.ItemForID.Name = "ItemForID";
            this.ItemForID.Size = new System.Drawing.Size(302, 24);
            this.ItemForID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // lyc_OpenPeriodButton
            // 
            this.lyc_OpenPeriodButton.Control = this.OpenPeriodButton;
            this.lyc_OpenPeriodButton.Location = new System.Drawing.Point(0, 144);
            this.lyc_OpenPeriodButton.Name = "lyc_OpenPeriodButton";
            this.lyc_OpenPeriodButton.Size = new System.Drawing.Size(302, 40);
            this.lyc_OpenPeriodButton.TextSize = new System.Drawing.Size(0, 0);
            this.lyc_OpenPeriodButton.TextVisible = false;
            // 
            // emptySpaceItem2
            // 
            this.emptySpaceItem2.AllowHotTrack = false;
            this.emptySpaceItem2.Location = new System.Drawing.Point(0, 184);
            this.emptySpaceItem2.Name = "emptySpaceItem2";
            this.emptySpaceItem2.Size = new System.Drawing.Size(302, 271);
            this.emptySpaceItem2.TextSize = new System.Drawing.Size(0, 0);
            // 
            // ItemForBranchID
            // 
            this.ItemForBranchID.Control = this.BranchIDLookUpEdit;
            this.ItemForBranchID.DataBindings.Add(new System.Windows.Forms.Binding("CustomizationFormText", this.drawerPeriodBindingSource, "BranchID", true, System.Windows.Forms.DataSourceUpdateMode.OnPropertyChanged));
            this.ItemForBranchID.Location = new System.Drawing.Point(0, 24);
            this.ItemForBranchID.Name = "ItemForBranchID";
            this.ItemForBranchID.Size = new System.Drawing.Size(302, 24);
            this.ItemForBranchID.TextSize = new System.Drawing.Size(116, 13);
            // 
            // DrawerPeriodForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(998, 568);
            this.Controls.Add(this.dataLayoutControl1);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(1000, 600);
            this.MinimumSize = new System.Drawing.Size(1000, 590);
            this.Name = "DrawerPeriodForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Période / Journal de caisse";
            this.Controls.SetChildIndex(this.dataLayoutControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.dataLayoutControl1)).EndInit();
            this.dataLayoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.summeryBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.drawerPeriodBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IDTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodStartDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodStartDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodEndDateEdit.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodEndDateEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.OpeningBalanceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ClosingBalanceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ActualBalanceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BalanceDifferenceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.TransferdBalanceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RemainingBalanceTextEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PeriodUserIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ClosingPeriodUserIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DifferenceAccountIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.DrawerIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ClosingDrwerIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.BranchIDLookUpEdit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Root)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForClosingBalance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_ClosingGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPeriodEnd)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForClosingPeriodUserID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBalanceDifference)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDifferenceAccountID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForClosingDrwerID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForTransferdBalance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForRemainingBalance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForActualBalance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_ClosePeriodButton)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_OpeningGroup)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForDrawerID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPeriodStart)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForPeriodUserID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForOpeningBalance)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForID)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lyc_OpenPeriodButton)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ItemForBranchID)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040018D0 RID: 6352
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x040018D1 RID: 6353
		public global::DevExpress.XtraDataLayout.DataLayoutControl dataLayoutControl1;

		// Token: 0x040018D2 RID: 6354
		public global::DevExpress.XtraLayout.LayoutControlGroup Root;

		// Token: 0x040018D3 RID: 6355
		public global::System.Windows.Forms.BindingSource drawerPeriodBindingSource;

		// Token: 0x040018D4 RID: 6356
		public global::DevExpress.XtraEditors.TextEdit IDTextEdit;

		// Token: 0x040018D5 RID: 6357
		public global::DevExpress.XtraEditors.DateEdit PeriodStartDateEdit;

		// Token: 0x040018D6 RID: 6358
		public global::DevExpress.XtraEditors.DateEdit PeriodEndDateEdit;

		// Token: 0x040018D7 RID: 6359
		public global::DevExpress.XtraEditors.TextEdit OpeningBalanceTextEdit;

		// Token: 0x040018D8 RID: 6360
		public global::DevExpress.XtraEditors.TextEdit ClosingBalanceTextEdit;

		// Token: 0x040018D9 RID: 6361
		public global::DevExpress.XtraEditors.TextEdit ActualBalanceTextEdit;

		// Token: 0x040018DA RID: 6362
		public global::DevExpress.XtraEditors.TextEdit BalanceDifferenceTextEdit;

		// Token: 0x040018DB RID: 6363
		public global::DevExpress.XtraEditors.TextEdit TransferdBalanceTextEdit;

		// Token: 0x040018DC RID: 6364
		public global::DevExpress.XtraEditors.TextEdit RemainingBalanceTextEdit;

		// Token: 0x040018DD RID: 6365
		public global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;

		// Token: 0x040018DE RID: 6366
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForID;

		// Token: 0x040018DF RID: 6367
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForPeriodStart;

		// Token: 0x040018E0 RID: 6368
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForPeriodEnd;

		// Token: 0x040018E1 RID: 6369
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForOpeningBalance;

		// Token: 0x040018E2 RID: 6370
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForClosingBalance;

		// Token: 0x040018E3 RID: 6371
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForBalanceDifference;

		// Token: 0x040018E4 RID: 6372
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForDifferenceAccountID;

		// Token: 0x040018E5 RID: 6373
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForPeriodUserID;

		// Token: 0x040018E6 RID: 6374
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForClosingPeriodUserID;

		// Token: 0x040018E7 RID: 6375
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForDrawerID;

		// Token: 0x040018E8 RID: 6376
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForClosingDrwerID;

		// Token: 0x040018E9 RID: 6377
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForTransferdBalance;

		// Token: 0x040018EA RID: 6378
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForRemainingBalance;

		// Token: 0x040018EB RID: 6379
		public global::DevExpress.XtraLayout.LayoutControlItem ItemForActualBalance;

		// Token: 0x040018EC RID: 6380
		public global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x040018ED RID: 6381
		public global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x040018EE RID: 6382
		public global::DevExpress.XtraLayout.LayoutControlGroup lyc_OpeningGroup;

		// Token: 0x040018EF RID: 6383
		public global::DevExpress.XtraLayout.LayoutControlItem layoutControlItem1;

		// Token: 0x040018F0 RID: 6384
		public global::DevExpress.XtraLayout.LayoutControlGroup lyc_ClosingGroup;

		// Token: 0x040018F1 RID: 6385
		public global::DevExpress.XtraEditors.SimpleButton OpenPeriodButton;

		// Token: 0x040018F2 RID: 6386
		public global::DevExpress.XtraLayout.LayoutControlItem lyc_OpenPeriodButton;

		// Token: 0x040018F3 RID: 6387
		public global::DevExpress.XtraEditors.SimpleButton ClosePeriodButton;

		// Token: 0x040018F4 RID: 6388
		public global::DevExpress.XtraEditors.LookUpEdit PeriodUserIDLookUpEdit;

		// Token: 0x040018F5 RID: 6389
		public global::DevExpress.XtraEditors.LookUpEdit ClosingPeriodUserIDLookUpEdit;

		// Token: 0x040018F6 RID: 6390
		public global::DevExpress.XtraEditors.LookUpEdit DifferenceAccountIDLookUpEdit;

		// Token: 0x040018F7 RID: 6391
		public global::DevExpress.XtraEditors.LookUpEdit DrawerIDLookUpEdit;

		// Token: 0x040018F8 RID: 6392
		public global::DevExpress.XtraEditors.LookUpEdit ClosingDrwerIDLookUpEdit;

		// Token: 0x040018F9 RID: 6393
		public global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem2;

		// Token: 0x040018FA RID: 6394
		public global::DevExpress.XtraLayout.LayoutControlItem lyc_ClosePeriodButton;

		// Token: 0x040018FB RID: 6395
		public global::DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem1;

		// Token: 0x040018FC RID: 6396
		public global::DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup4;

		// Token: 0x040018FD RID: 6397
		public global::System.Windows.Forms.BindingSource summeryBindingSource;

		// Token: 0x040018FE RID: 6398
		public global::DevExpress.XtraGrid.Columns.GridColumn colProcessType;

		// Token: 0x040018FF RID: 6399
		public global::DevExpress.XtraGrid.Columns.GridColumn colProcessCount;

		// Token: 0x04001900 RID: 6400
		public global::DevExpress.XtraGrid.Columns.GridColumn colProcessSum;

		// Token: 0x04001901 RID: 6401
		private global::DevExpress.XtraEditors.LookUpEdit BranchIDLookUpEdit;

		// Token: 0x04001902 RID: 6402
		private global::DevExpress.XtraLayout.LayoutControlItem ItemForBranchID;
	}
}
