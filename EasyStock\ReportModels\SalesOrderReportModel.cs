﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class SalesOrderReportModel : CompanyInfoReportModel
    {
        [Display(Name = "État")]
        public BillState State { get; set; }

        [Display(Name = "Nom de la succursale")]
        public new string BranchName { get; set; }

        [Display(Name = "Code succursale")]
        public new int BranchID { get; set; }

        [Display(Name = "Numéro", GroupName = "Détails de la facture")]
        public int ID { get; set; }

        [Display(Name = "Code", GroupName = "Détails de la facture")]
        public string Code { get; set; }

        [Display(Name = "Date", GroupName = "Détails de la facture")]
        public DateTime Date { get; set; }

        [Display(Name = "Notes", GroupName = "Détails de la facture")]
        public string Notes { get; set; }

        [Display(Name = "Total des articles", GroupName = "Valeur")]
        public double Total { get; set; }

        [Display(Name = "Articles")]
        public ICollection<InvoiceDetailReportModel> Details { get; set; }

        [Display(Name = "Montant de la remise", GroupName = "Valeur")]
        public double Discount { get; set; }

        [Display(Name = "Pourcentage de remise")]
        public double DiscountPercentage
        {
            get
            {
                return this.Discount / this.Total;
            }
        }

        [Display(Name = "Net", GroupName = "Valeur")]
        public double Net { get; set; }

        [Display(Name = "Autres frais", GroupName = "Valeur")]
        public double OtherExpenses { get; set; }

        [Display(Name = "Montant de la taxe", GroupName = "Valeur")]
        public double Tax { get; set; }

        [Display(Name = "Code client")]
        public int CustomerID { get; set; }

        [StringLength(400)]
        [Display(Name = "Nom du client")]
        public string CustomerName { get; set; }

        [Display(Name = "Ville du client")]
        public string CustomerCity { get; set; }

        [StringLength(250)]
        [Display(Name = "Adresse du client")]
        public string CustomerAddress { get; set; }

        [StringLength(50)]
        [Display(Name = "Téléphone du client")]
        public string CustomerPhone { get; set; }

        [Display(Name = "Mobile du client")]
        public string CustomerMobile { get; set; }

        [Display(Name = "Numéro fiscal du client")]
        public string CustomerTaxNumber { get; set; }
    }
}
