﻿using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace EasyStock.ReportModels
{
    public class InOutModel
    {
        public ICollection<ProductTransaction> ProductTransaction { get; set; } = new List<ProductTransaction>();

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Entrée")]
        public double In { get; set; }

        [Display(Name = "Sortie")]
        public double Out { get; set; }

        [Display(Name = "Solde")]
        public double Balance
        {
            get
            {
                double In = (from x in this.ProductTransaction
                             where x.TransactionType == ProductTransactionType.In
                             select x).Sum((ProductTransaction sa) => sa.Quantity * sa.Factor);
                double Out = (from x in this.ProductTransaction
                              where x.TransactionType == ProductTransactionType.Out
                              select x).Sum((ProductTransaction sa) => sa.Quantity * sa.Factor);
                return In - Out;
            }
        }
    }
}
