﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using EasyStock.Controller;

namespace EasyStock.Models
{
    [DisplayName("Paiements")]
    public class PayDetail : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID { get; set; }

        public SystemProcess SourceType { get; set; }

        public int SourceID { get; set; }

        [Display(Name = "Code")]
        public string Code { get; set; }

        [Display(Name = "Méthode de paiement")]
        public PayMethodType MethodType
        {
            get
            {
                return this.methodType;
            }
            set
            {
                base.SetProperty<PayMethodType>(ref this.methodType, value, "MethodType");
            }
        }

        [Display(Name = "Compte de paiement")]
        public int MethodID
        {
            get
            {
                return this.methodID;
            }
            set
            {
                base.SetProperty<int>(ref this.methodID, value, "MethodID");
            }
        }

        [Display(Name = "Montant")]
        public double Amount
        {
            get
            {
                return this.amount;
            }
            set
            {
                base.SetProperty<double>(ref this.amount, value, "Amount");
            }
        }

        public Currency Currency
        {
            get
            {
                bool flag = this._currency == null || this._currency.ID != this.CurrancyID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._currency = db.Currencies.SingleOrDefault((Currency x) => x.ID == this.CurrancyID);
                    }
                }
                return this._currency;
            }
        }

        [Display(Name = "Devise")]
        public int CurrancyID { get; set; }

        [Display(Name = "Taux de change")]
        public double CurrancyRate { get; set; }

        [NotMapped]
        [Display(Name = "Montant local")]
        public double LocalAmount
        {
            get
            {
                return this.Amount * this.CurrancyRate;
            }
            set
            {
                this.Amount = value / this.CurrancyRate;
            }
        }

        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [Display(Name = "Date d'entrée")]
        public DateTime InsertDate { get; set; }

        private PayMethodType methodType;

        private int methodID;

        private double amount;

        [NotMapped]
        private Currency _currency;
    }
}
