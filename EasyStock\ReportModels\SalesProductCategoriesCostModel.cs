﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class SalesProductCategoriesCostModel : CompanyInfoReportModel
    {
        [Display(Name = "Catégorie")]
        public string Category { get; set; }

        [Display(Name = "Quantité")]
        public double Qty { get; set; }

        [Display(Name = "Coût total")]
        public double TotalCost { get; set; }

        [Display(Name = "Total des ventes")]
        public double TotalSales { get; set; }

        [Display(Name = "Marge bénéficiaire")]
        public double ProfitMargin
        {
            get
            {
                return this.TotalSales - this.TotalCost;
            }
        }
    }
}
