﻿using DevExpress.XtraReports.UI;
using EasyStock.Classes;
using EasyStock.Controller;
using EasyStock.Models;
using EasyStock.ReportModels;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace EasyStock.Reports
{
    public class MasterReport : XtraReport
    {
        public MasterReport()
        {
            base.ShowPrintMarginsWarning = false;
        }

        public virtual void LoadDummyData()
        {
        }

        internal virtual void LoadLayout()
        {
            base.Name = base.GetType().Name;
            using (ERPDataContext db = new ERPDataContext())
            {
                ReportTemplate row = (from x in db.ReportTemplates
                                      where x.ReportName == this.Name
                                      orderby x.IsDefault descending
                                      select x).FirstOrDefault<ReportTemplate>();
                bool flag = ((row != null) ? row.Template : null) != null;
                if (flag)
                {
                    using (MemoryStream stream = new MemoryStream())
                    {
                        stream.Write(row.Template.ToArray<byte>(), 0, row.Template.ToArray<byte>().Length);
                        base.LoadLayout(stream);
                    }
                }
            }
        }

        internal void SetCompanyInfo(IEnumerable<CompanyInfoReportModel> rows)
        {
            foreach (CompanyInfoReportModel model in rows)
            {
                bool flag = model == null;
                if (!flag)
                {
                    model.CompanyAddress = CurrentSession.CompanyInfo.Address;
                    model.CompanyCity = CurrentSession.CompanyInfo.City;
                    model.CompanyName = CurrentSession.CompanyInfo.Name;
                    model.CompanyLogo = CurrentSession.CompanyInfo.CompanyLogo;
                    model.CompanyMobile = CurrentSession.CompanyInfo.Mobile;
                    model.CompanyPhone = CurrentSession.CompanyInfo.Phone;
                    model.CompanyTaxCard = CurrentSession.CompanyInfo.CompanyTaxCard;
                    model.CommercialBook = CurrentSession.CompanyInfo.CommercialBook;
                    model.CompanyNIS = CurrentSession.CompanyInfo.CompanyNIS;
                    model.CompanyART = CurrentSession.CompanyInfo.CompanyART;
                    model.CompanyRIB = CurrentSession.CompanyInfo.CompanyRIB;
                }
            }
        }

        internal void SetCompanyInfo(CompanyInfoReportModel row)
        {
            this.SetCompanyInfo(new CompanyInfoReportModel[]
            {
                row
            });
        }

        public static void Print(XtraReport rpt)
        {
            rpt.ShowPrintMarginsWarning = false;
            switch (CurrentSession.InvoicePrintMode)
            {
                case PrintMode.Direct:
                    rpt.Print("");
                    break;
                case PrintMode.ShowPreview:
                    rpt.ShowPreview();
                    break;
                case PrintMode.ShowDialog:
                    rpt.PrintDialog();
                    break;
            }
        }

        private void InitializeComponent()
        {
            this.topMarginBand1 = new DevExpress.XtraReports.UI.TopMarginBand();
            this.detailBand1 = new DevExpress.XtraReports.UI.DetailBand();
            this.bottomMarginBand1 = new DevExpress.XtraReports.UI.BottomMarginBand();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            this.topMarginBand1.Name = "topMarginBand1";
            this.detailBand1.Name = "detailBand1";
            this.bottomMarginBand1.Name = "bottomMarginBand1";
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.topMarginBand1,
            this.detailBand1,
            this.bottomMarginBand1});
            this.Version = "23.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        private TopMarginBand topMarginBand1;

        private DetailBand detailBand1;

        private BottomMarginBand bottomMarginBand1;
    }
}
