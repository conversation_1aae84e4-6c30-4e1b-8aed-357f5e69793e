﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
	public class Consumptions
	{
		[Display(Name = "<PERSON>épôt")]
		public string StoreName { get; set; }

		[Display(Name = "Article")]
		public string ProductName { get; set; }

		[Display(Name = "Unité")]
		public string UnitName { get; set; }

		[Display(Name = "Qté")]
        [Range(0.001, 1.7976931348623157E+308, ErrorMessage = "La quantité doit être supérieure à 0.001")]
        public double Quantity { get; set; }

		[Display(Name = "Prix ")]
		public double UnitPrice { get; set; }

		[Display(Name = "Total")]
		public double TotalCost
		{
			get
			{
				return this.UnitPrice * this.Quantity;
			}
		}
	}
}
