﻿using DevExpress.XtraLayout.Utils;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class ProductStoreValidityReportView : XtraReportForm
    {
        public ProductStoreValidityReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Product
        })
        {
            this.InitializeComponent();
            base.FiltersForm.ItemForFromDate.Text = "Date";
            base.FiltersForm.ItemForToDate.Visibility = LayoutVisibility.Never;
            base.FiltersForm.FromDate.EditValue = DateTime.Now;
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                result = true;
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                ProductStoreValidityReport rpt = new ProductStoreValidityReport();
                ICollection<ProductStoreValidityReprtModel> dataSource = this.GetData();
                rpt.DataSource = dataSource;
                rpt.SetCompanyInfo(dataSource.FirstOrDefault<ProductStoreValidityReprtModel>());
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;

                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<ProductStoreValidityReprtModel> GetData()
        {
            int[] ProductIDs = base.FiltersForm.Products.EditValue?.ToArray();
            using ERPDataContext context = new ERPDataContext();
            IQueryable<ProductTransaction> filterdTrans = from x in context.ProductTransactions
                                                          where (int)x.TransactionState == 2
                                                          select x into trans
                                                          where trans.Expire.HasValue && DbFunctions.TruncateTime(trans.Date) <= FiltersForm.startDate
                                                          select trans;
            if (ProductIDs != null && ProductIDs.Count() > 0)
            {
                filterdTrans = filterdTrans.Where((ProductTransaction c) => ProductIDs.Contains(c.ProductID));
            }
            IQueryable<ProductStoreValidityReprtModel> data = from tr in filterdTrans
                                                              group tr by new { tr.ProductID, tr.Expire, tr.StoreID } into g
                                                              join pr in context.Products on g.Key.ProductID equals pr.ID
                                                              join brn in context.Stores on g.Key.StoreID equals brn.ID
                                                              select new ProductStoreValidityReprtModel
                                                              {
                                                                  ProductID = g.Key.ProductID,
                                                                  ProductName = pr.Name,
                                                                  Date = g.Key.Expire.Value,
                                                                  StoreName = brn.Name,
                                                                  Balance = (g.Where((ProductTransaction j) => (int)j.TransactionType == 0).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0) - (g.Where((ProductTransaction j) => (int)j.TransactionType == 1).Sum(x => (double?)(x.Quantity * x.Factor)) ?? 0.0)
                                                              };
            return data.Where((ProductStoreValidityReprtModel x) => x.Balance != 0.0).ToList();
        }
    }
}
