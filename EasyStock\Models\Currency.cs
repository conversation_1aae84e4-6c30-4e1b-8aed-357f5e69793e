﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Devises")]
    public class Currency
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Nom")]
        public string Name { get; set; }


        [StringLength(50)]
        [Display(Name = "Livre")]
        public string Pound1 { get; set; }

        [StringLength(50)]
        [Display(Name = "Deux Livreser")]
        public string Pound2 { get; set; }

        [StringLength(50)]
        [Display(Name = "Livres")]
        public string Pound3 { get; set; }

        [StringLength(50)]
        [Display(Name = "Piaster")]
        public string Piaster1 { get; set; }

        [StringLength(50)]
        [Display(Name = "Deux Piastres")]
        public string Piaster2 { get; set; }

        [StringLength(50)]
        [Display(Name = "Piasters")]
        public string Piaster3 { get; set; }

        public double LastRate { get; set; }
    }
}
