﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Transferts de fonds")]
    public class CashTransfer : BaseNotifyPropertyChangedModel
    {
        [Display(Name = "N°")]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int ID
        {
            get
            {
                return this._Id;
            }
            set
            {
                base.SetProperty<int>(ref this._Id, value, "ID");
            }
        }

        [Display(Name = "")]
        public Branch Branch
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<Branch>(ref this.branch, value, "Branch");
            }
        }

        [Display(Name = "Succursale")]
        [Range(1, **********, ErrorMessage = "Ce champ est obligatoire")]
        public int BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int>(ref this.branchID, value, "BranchID");
            }
        }

        [Display(Name = "Du compte")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, **********, ErrorMessage = "Ce champ est obligatoire")]
        public int FromAccountID
        {
            get
            {
                return this.fromAccountID;
            }
            set
            {
                base.SetProperty<int>(ref this.fromAccountID, value, "FromAccountID");
            }
        }

        [Display(Name = "Au compte")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, **********, ErrorMessage = "Ce champ est obligatoire")]
        public int ToAccountID
        {
            get
            {
                return this.toAccountID;
            }
            set
            {
                base.SetProperty<int>(ref this.toAccountID, value, "ToAccountID");
            }
        }

        [Display(Name = "Monnai")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1, **********, ErrorMessage = "Ce champ est obligatoire")]
        public int CurrencyID
        {
            get
            {
                return this.currencyID;
            }
            set
            {
                base.SetProperty<int>(ref this.currencyID, value, "CurrencyID");
            }
        }

        [Display(Name = "Taux de change")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1E-06, 1.7976931348623157E+308, ErrorMessage = "Ce champ est obligatoire")]
        public double CurrencyRate
        {
            get
            {
                return this.currencyRate;
            }
            set
            {
                base.SetProperty<double>(ref this.currencyRate, value, "CurrencyRate");
            }
        }

        [Display(Name = "Montant")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        [Range(1E-06, 1.7976931348623157E+308, ErrorMessage = "Ce champ est obligatoire")]
        public double Amount
        {
            get
            {
                return this.amount;
            }
            set
            {
                base.SetProperty<double>(ref this.amount, value, "Amount");
            }
        }

        [Display(Name = "Date")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public DateTime Date
        {
            get
            {
                return this.date;
            }
            set
            {
                base.SetProperty<DateTime>(ref this.date, value, "Date");
            }
        }

        [Display(Name = "Montant local")]
        public double LocalAmount
        {
            get
            {
                return this.Amount * this.CurrencyRate;
            }
        }

        [Display(Name = "Notes")]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        private int _Id;

        private Branch branch;

        private int branchID;

        private int fromAccountID;

        private int toAccountID;

        private int currencyID;

        private double currencyRate;

        private double amount;

        private DateTime date;

        private string notes;
    }
}
