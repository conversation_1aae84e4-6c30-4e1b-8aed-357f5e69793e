﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    internal class ProductStoreTransactionReportModel
    {
        [Display(Name = "Numéro")]
        public int ID { get; set; }

        [Display(Name = "Code Produit")]
        public int ProductID { get; set; }

        [Display(Name = "Produit")]
        public string ProductName { get; set; }

        [Display(Name = "Code Dépôt")]
        public int StoreID { get; set; }

        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Date de l'opération")]
        public DateTime Date { get; set; }

        [Display(Name = "Type d'opération")]
        public TransactionType ProcessType { get; set; }

        [Display(Name = "Numéro de l'opération")]
        public int BillID { get; set; }

        [Display(Name = "Partie prenante")]
        public string PartName { get; set; }

        [Display(Name = "Code Partie prenante")]
        public int PartID { get; set; }

        [Display(Name = "Unité")]
        public string UnitName { get; set; }

        [Display(Name = "Type de transaction")]
        public ProductTransactionType TransactionType { get; set; }

        [Display(Name = "Quantité")]
        public double Quantity { get; set; }

        [Display(Name = "Série")]
        public string Serial { get; set; }

        [Display(Name = "Couleur")]
        public string Color { get; set; }

        [Display(Name = "Taille")]
        public string Size { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Prix")]
        public double Price { get; set; }

        [Display(Name = "Total")]
        public double Total
        {
            get
            {
                return this.Price * this.Quantity;
            }
        }

        [Display(Name = "Code de suivi")]
        public int? ParentTransactionID { get; set; }

        [Display(Name = "Solde")]
        public double Balance { get; set; }
    }
}
