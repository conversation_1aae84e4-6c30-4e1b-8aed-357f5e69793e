﻿namespace EasyStock.ReportViews
{
	// Token: 0x020002FE RID: 766
	public partial class SalesProductCategoriesCostView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x0600130B RID: 4875 RVA: 0x0014F2E4 File Offset: 0x0014D4E4
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600130C RID: 4876 RVA: 0x0014F31C File Offset: 0x0014D51C
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 53);
            this.documentViewer1.Size = new System.Drawing.Size(1126, 602);
            // 
            // SalesProductCategoriesCostView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1126, 676);
            this.Name = "SalesProductCategoriesCostView";
            this.Text = "Coûts de vente par catégorie d\'articles";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x04001918 RID: 6424
		private global::System.ComponentModel.IContainer components = null;
	}
}
