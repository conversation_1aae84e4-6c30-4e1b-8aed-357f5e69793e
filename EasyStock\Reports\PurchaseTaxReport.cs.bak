﻿using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x02000389 RID: 905
	public class PurchaseTaxReport : MasterReport
	{
		// Token: 0x06001557 RID: 5463 RVA: 0x0000ADAE File Offset: 0x00008FAE
		public PurchaseTaxReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001558 RID: 5464 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void ReportHeader_BeforePrint(object sender, PrintEventArgs e)
		{
		}

		// Token: 0x06001559 RID: 5465 RVA: 0x001B2974 File Offset: 0x001B0B74
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600155A RID: 5466 RVA: 0x001B29AC File Offset: 0x001B0BAC
		private void InitializeComponent()
		{
			this.components = new Container();
			ObjectConstructorInfo objectConstructorInfo = new ObjectConstructorInfo();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.Detail = new DetailBand();
			this.xrTable3 = new XRTable();
			this.xrTableRow9 = new XRTableRow();
			this.xrTableCell5 = new XRTableCell();
			this.xrTableCell7 = new XRTableCell();
			this.xrTableCell8 = new XRTableCell();
			this.xrTableCell9 = new XRTableCell();
			this.xrTableCell10 = new XRTableCell();
			this.xrTableCell11 = new XRTableCell();
			this.xrTableCell12 = new XRTableCell();
			this.xrTableCell14 = new XRTableCell();
			this.ReportHeader = new ReportHeaderBand();
			this.xrTable2 = new XRTable();
			this.xrTableRow5 = new XRTableRow();
			this.Cell_ReportName = new XRTableCell();
			this.xrTableRow6 = new XRTableRow();
			this.xrTableCell4 = new XRTableCell();
			this.xrTableRow7 = new XRTableRow();
			this.Cell_Filters = new XRTableCell();
			this.xrPictureBox2 = new XRPictureBox();
			this.xrTable1 = new XRTable();
			this.xrTableRow2 = new XRTableRow();
			this.xrTableCell13 = new XRTableCell();
			this.xrTableRow3 = new XRTableRow();
			this.xrTableCell2 = new XRTableCell();
			this.xrTableRow4 = new XRTableRow();
			this.xrTableCell3 = new XRTableCell();
			this.xrTableRow1 = new XRTableRow();
			this.xrTableCell1 = new XRTableCell();
			this.xrTableRow8 = new XRTableRow();
			this.xrTableCell6 = new XRTableCell();
			this.GroupHeader1 = new GroupHeaderBand();
			this.xrTable4 = new XRTable();
			this.xrTableRow10 = new XRTableRow();
			this.xrTableCell15 = new XRTableCell();
			this.xrTableCell16 = new XRTableCell();
			this.xrTableCell17 = new XRTableCell();
			this.xrTableCell18 = new XRTableCell();
			this.xrTableCell19 = new XRTableCell();
			this.xrTableCell20 = new XRTableCell();
			this.xrTableCell21 = new XRTableCell();
			this.xrTableCell22 = new XRTableCell();
			this.xrControlStyle1 = new XRControlStyle();
			this.objectDataSource1 = new ObjectDataSource(this.components);
			this.GroupFooter1 = new GroupFooterBand();
			this.xrTable5 = new XRTable();
			this.xrTableRow11 = new XRTableRow();
			this.xrTableCell27 = new XRTableCell();
			this.xrTableCell28 = new XRTableCell();
			this.xrTableCell29 = new XRTableCell();
			this.xrTableCell30 = new XRTableCell();
			this.xrTableCell23 = new XRTableCell();
			this.xrControlStyle2 = new XRControlStyle();
			this.xrControlStyle3 = new XRControlStyle();
			this.xrControlStyle4 = new XRControlStyle();
			this.xrControlStyle5 = new XRControlStyle();
			((ISupportInitialize)this.xrTable3).BeginInit();
			((ISupportInitialize)this.xrTable2).BeginInit();
			((ISupportInitialize)this.xrTable1).BeginInit();
			((ISupportInitialize)this.xrTable4).BeginInit();
			((ISupportInitialize)this.objectDataSource1).BeginInit();
			((ISupportInitialize)this.xrTable5).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.TopMargin.HeightF = 36f;
			this.TopMargin.Name = "TopMargin";
			this.BottomMargin.HeightF = 38f;
			this.BottomMargin.Name = "BottomMargin";
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.xrTable3
			});
			this.Detail.HeightF = 25.19685f;
			this.Detail.Name = "Detail";
			this.xrTable3.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable3.Name = "xrTable3";
			this.xrTable3.OddStyleName = "xrControlStyle5";
			this.xrTable3.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable3.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow9
			});
			this.xrTable3.SizeF = new SizeF(756.2992f, 25.19685f);
			this.xrTable3.StylePriority.UseTextAlignment = false;
			this.xrTable3.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableRow9.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell5,
				this.xrTableCell7,
				this.xrTableCell8,
				this.xrTableCell9,
				this.xrTableCell10,
				this.xrTableCell11,
				this.xrTableCell12,
				this.xrTableCell14
			});
			this.xrTableRow9.Name = "xrTableRow9";
			this.xrTableRow9.Weight = 0.5609756097560976;
			this.xrTableCell5.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Net]")
			});
			this.xrTableCell5.Multiline = true;
			this.xrTableCell5.Name = "xrTableCell5";
			this.xrTableCell5.StylePriority.UseTextAlignment = false;
			this.xrTableCell5.Text = "xrTableCell5";
			this.xrTableCell5.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell5.TextFormatString = "{0:0.##}";
			this.xrTableCell5.Weight = 0.1113992772876453;
			this.xrTableCell7.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TaxAmount]")
			});
			this.xrTableCell7.Multiline = true;
			this.xrTableCell7.Name = "xrTableCell7";
			this.xrTableCell7.StylePriority.UseTextAlignment = false;
			this.xrTableCell7.Text = "xrTableCell7";
			this.xrTableCell7.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell7.TextFormatString = "{0:0.##}";
			this.xrTableCell7.Weight = 0.11027197910940036;
			this.xrTableCell8.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Discount]")
			});
			this.xrTableCell8.Multiline = true;
			this.xrTableCell8.Name = "xrTableCell8";
			this.xrTableCell8.StylePriority.UseTextAlignment = false;
			this.xrTableCell8.Text = "xrTableCell8";
			this.xrTableCell8.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell8.TextFormatString = "{0:0.##}";
			this.xrTableCell8.Weight = 0.12185052574162537;
			this.xrTableCell9.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Total]")
			});
			this.xrTableCell9.Multiline = true;
			this.xrTableCell9.Name = "xrTableCell9";
			this.xrTableCell9.StylePriority.UseTextAlignment = false;
			this.xrTableCell9.Text = "xrTableCell9";
			this.xrTableCell9.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell9.TextFormatString = "{0:0.##}";
			this.xrTableCell9.Weight = 0.12491607068640945;
			this.xrTableCell10.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[InvoiceDate]")
			});
			this.xrTableCell10.Multiline = true;
			this.xrTableCell10.Name = "xrTableCell10";
			this.xrTableCell10.StylePriority.UseTextAlignment = false;
			this.xrTableCell10.Text = "xrTableCell10";
			this.xrTableCell10.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell10.TextFormatString = "{0:M/d/yyyy}";
			this.xrTableCell10.Weight = 0.13421921700369585;
			this.xrTableCell11.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Name]")
			});
			this.xrTableCell11.Multiline = true;
			this.xrTableCell11.Name = "xrTableCell11";
			this.xrTableCell11.StylePriority.UseTextAlignment = false;
			this.xrTableCell11.Text = "xrTableCell11";
			this.xrTableCell11.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell11.Weight = 0.17404317089051036;
			this.xrTableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TaxFileNumber]")
			});
			this.xrTableCell12.Multiline = true;
			this.xrTableCell12.Name = "xrTableCell12";
			this.xrTableCell12.StylePriority.UseTextAlignment = false;
			this.xrTableCell12.Text = "xrTableCell12";
			this.xrTableCell12.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell12.Weight = 0.1495057066249767;
			this.xrTableCell14.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[InvoicesCode]")
			});
			this.xrTableCell14.Multiline = true;
			this.xrTableCell14.Name = "xrTableCell14";
			this.xrTableCell14.StylePriority.UseTextAlignment = false;
			this.xrTableCell14.Text = "xrTableCell14";
			this.xrTableCell14.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell14.Weight = 0.10643685325944416;
			this.ReportHeader.Controls.AddRange(new XRControl[]
			{
				this.xrTable2,
				this.xrPictureBox2,
				this.xrTable1
			});
			this.ReportHeader.HeightF = 209.5576f;
			this.ReportHeader.Name = "ReportHeader";
			this.ReportHeader.BeforePrint += this.ReportHeader_BeforePrint;
			this.xrTable2.AnchorHorizontal = HorizontalAnchorStyles.Both;
			this.xrTable2.AnchorVertical = VerticalAnchorStyles.Both;
			this.xrTable2.LocationFloat = new PointFloat(0f, 123.4375f);
			this.xrTable2.Name = "xrTable2";
			this.xrTable2.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable2.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow5,
				this.xrTableRow6,
				this.xrTableRow7
			});
			this.xrTable2.SizeF = new SizeF(757f, 86.12007f);
			this.xrTable2.StylePriority.UseTextAlignment = false;
			this.xrTable2.TextAlignment = TextAlignment.TopCenter;
			this.xrTableRow5.Cells.AddRange(new XRTableCell[]
			{
				this.Cell_ReportName
			});
			this.xrTableRow5.Name = "xrTableRow5";
			this.xrTableRow5.Weight = 1.0;
			this.Cell_ReportName.BackColor = Color.WhiteSmoke;
			this.Cell_ReportName.Borders = BorderSide.All;
			this.Cell_ReportName.BorderWidth = 2f;
			this.Cell_ReportName.CanGrow = false;
			this.Cell_ReportName.Font = new Font("Traditional Arabic", 21.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.Cell_ReportName.Multiline = true;
			this.Cell_ReportName.Name = "Cell_ReportName";
			this.Cell_ReportName.Padding = new PaddingInfo(5, 5, 5, 5, 100f);
			this.Cell_ReportName.RowSpan = 2;
			this.Cell_ReportName.StylePriority.UseBackColor = false;
			this.Cell_ReportName.StylePriority.UseBorders = false;
			this.Cell_ReportName.StylePriority.UseBorderWidth = false;
			this.Cell_ReportName.StylePriority.UseFont = false;
			this.Cell_ReportName.StylePriority.UsePadding = false;
			this.Cell_ReportName.StylePriority.UseTextAlignment = false;
			this.Cell_ReportName.Text = "اسم التقرير";
			this.Cell_ReportName.TextAlignment = TextAlignment.MiddleCenter;
			this.Cell_ReportName.Weight = 3.0;
			this.xrTableRow6.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell4
			});
			this.xrTableRow6.Name = "xrTableRow6";
			this.xrTableRow6.Weight = 1.0;
			this.xrTableCell4.BackColor = Color.WhiteSmoke;
			this.xrTableCell4.Borders = BorderSide.All;
			this.xrTableCell4.BorderWidth = 2f;
			this.xrTableCell4.CanGrow = false;
			this.xrTableCell4.Font = new Font("Arial", 12.64f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTableCell4.Multiline = true;
			this.xrTableCell4.Name = "xrTableCell4";
			this.xrTableCell4.Padding = new PaddingInfo(5, 5, 5, 5, 100f);
			this.xrTableCell4.StylePriority.UseBackColor = false;
			this.xrTableCell4.StylePriority.UseBorders = false;
			this.xrTableCell4.StylePriority.UseBorderWidth = false;
			this.xrTableCell4.StylePriority.UseFont = false;
			this.xrTableCell4.StylePriority.UsePadding = false;
			this.xrTableCell4.StylePriority.UseTextAlignment = false;
			this.xrTableCell4.Text = "xrTableCell4";
			this.xrTableCell4.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell4.Weight = 3.0;
			this.xrTableRow7.Cells.AddRange(new XRTableCell[]
			{
				this.Cell_Filters
			});
			this.xrTableRow7.Name = "xrTableRow7";
			this.xrTableRow7.Weight = 1.0;
			this.Cell_Filters.CanGrow = false;
			this.Cell_Filters.Multiline = true;
			this.Cell_Filters.Name = "Cell_Filters";
			this.Cell_Filters.StylePriority.UseTextAlignment = false;
			this.Cell_Filters.TextAlignment = TextAlignment.MiddleCenter;
			this.Cell_Filters.Weight = 3.0;
			this.xrPictureBox2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "ImageSource", "[CompanyLogo]")
			});
			this.xrPictureBox2.LocationFloat = new PointFloat(650f, 13.43749f);
			this.xrPictureBox2.Name = "xrPictureBox2";
			this.xrPictureBox2.SizeF = new SizeF(100f, 100f);
			this.xrPictureBox2.Sizing = ImageSizeMode.Squeeze;
			this.xrTable1.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable1.Name = "xrTable1";
			this.xrTable1.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable1.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow2,
				this.xrTableRow3,
				this.xrTableRow4,
				this.xrTableRow1,
				this.xrTableRow8
			});
			this.xrTable1.SizeF = new SizeF(289.5833f, 123.4375f);
			this.xrTable1.StylePriority.UseTextAlignment = false;
			this.xrTable1.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell13
			});
			this.xrTableRow2.Name = "xrTableRow2";
			this.xrTableRow2.Weight = 17.**************;
			this.xrTableCell13.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyName]")
			});
			this.xrTableCell13.Font = new Font("Arial", 12f, FontStyle.Bold);
			this.xrTableCell13.Multiline = true;
			this.xrTableCell13.Name = "xrTableCell13";
			this.xrTableCell13.StylePriority.UseFont = false;
			this.xrTableCell13.StylePriority.UseTextAlignment = false;
			this.xrTableCell13.Text = "xrTableCell13";
			this.xrTableCell13.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell13.Weight = 1.8461538461538458;
			this.xrTableRow3.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell2
			});
			this.xrTableRow3.Name = "xrTableRow3";
			this.xrTableRow3.Weight = 17.**************;
			this.xrTableCell2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CommercialBook]")
			});
			this.xrTableCell2.Multiline = true;
			this.xrTableCell2.Name = "xrTableCell2";
			this.xrTableCell2.Text = "xrTableCell2";
			this.xrTableCell2.Weight = 1.8461538461538458;
			this.xrTableRow4.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell3
			});
			this.xrTableRow4.Name = "xrTableRow4";
			this.xrTableRow4.Weight = 17.**************;
			this.xrTableCell3.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyTaxCard]")
			});
			this.xrTableCell3.Multiline = true;
			this.xrTableCell3.Name = "xrTableCell3";
			this.xrTableCell3.Text = "xrTableCell3";
			this.xrTableCell3.Weight = 1.8461538461538458;
			this.xrTableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell1
			});
			this.xrTableRow1.Name = "xrTableRow1";
			this.xrTableRow1.Weight = 17.**************;
			this.xrTableCell1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyAddress]")
			});
			this.xrTableCell1.Multiline = true;
			this.xrTableCell1.Name = "xrTableCell1";
			this.xrTableCell1.Text = "xrTableCell1";
			this.xrTableCell1.Weight = 1.8461538461538458;
			this.xrTableRow8.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell6
			});
			this.xrTableRow8.Name = "xrTableRow8";
			this.xrTableRow8.Weight = 17.**************;
			this.xrTableCell6.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyPhone]")
			});
			this.xrTableCell6.Multiline = true;
			this.xrTableCell6.Name = "xrTableCell6";
			this.xrTableCell6.Text = "xrTableCell6";
			this.xrTableCell6.Weight = 1.8461538461538458;
			this.GroupHeader1.Controls.AddRange(new XRControl[]
			{
				this.xrTable4
			});
			this.GroupHeader1.HeightF = 41.94241f;
			this.GroupHeader1.Name = "GroupHeader1";
			this.xrTable4.BackColor = Color.FromArgb(48, 54, 64);
			this.xrTable4.Font = new Font("Arial", 11.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTable4.ForeColor = Color.White;
			this.xrTable4.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable4.Name = "xrTable4";
			this.xrTable4.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable4.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow10
			});
			this.xrTable4.SizeF = new SizeF(756.2992f, 40.82185f);
			this.xrTable4.StylePriority.UseBackColor = false;
			this.xrTable4.StylePriority.UseFont = false;
			this.xrTable4.StylePriority.UseForeColor = false;
			this.xrTableRow10.BorderColor = Color.White;
			this.xrTableRow10.Borders = BorderSide.Right;
			this.xrTableRow10.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell15,
				this.xrTableCell16,
				this.xrTableCell17,
				this.xrTableCell18,
				this.xrTableCell19,
				this.xrTableCell20,
				this.xrTableCell21,
				this.xrTableCell22
			});
			this.xrTableRow10.Name = "xrTableRow10";
			this.xrTableRow10.StylePriority.UseBorderColor = false;
			this.xrTableRow10.StylePriority.UseBorders = false;
			this.xrTableRow10.StylePriority.UseTextAlignment = false;
			this.xrTableRow10.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableRow10.Weight = 0.5609756097560976;
			this.xrTableCell15.BorderColor = Color.White;
			this.xrTableCell15.Borders = BorderSide.Right;
			this.xrTableCell15.Multiline = true;
			this.xrTableCell15.Name = "xrTableCell15";
			this.xrTableCell15.StylePriority.UseBorderColor = false;
			this.xrTableCell15.StylePriority.UseBorders = false;
			this.xrTableCell15.StylePriority.UseTextAlignment = false;
			this.xrTableCell15.Text = "الصافى";
			this.xrTableCell15.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell15.Weight = 0.11139921774544927;
			this.xrTableCell16.BorderColor = Color.White;
			this.xrTableCell16.Borders = BorderSide.Right;
			this.xrTableCell16.Multiline = true;
			this.xrTableCell16.Name = "xrTableCell16";
			this.xrTableCell16.StylePriority.UseBorderColor = false;
			this.xrTableCell16.StylePriority.UseBorders = false;
			this.xrTableCell16.StylePriority.UseTextAlignment = false;
			this.xrTableCell16.Text = "الضريبة";
			this.xrTableCell16.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell16.Weight = 0.11027201159081115;
			this.xrTableCell17.BorderColor = Color.White;
			this.xrTableCell17.Borders = BorderSide.Right;
			this.xrTableCell17.Multiline = true;
			this.xrTableCell17.Name = "xrTableCell17";
			this.xrTableCell17.StylePriority.UseBorderColor = false;
			this.xrTableCell17.StylePriority.UseBorders = false;
			this.xrTableCell17.StylePriority.UseTextAlignment = false;
			this.xrTableCell17.Text = "الخصم";
			this.xrTableCell17.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell17.Weight = 0.12185052525654418;
			this.xrTableCell18.BorderColor = Color.White;
			this.xrTableCell18.Borders = BorderSide.Right;
			this.xrTableCell18.Multiline = true;
			this.xrTableCell18.Name = "xrTableCell18";
			this.xrTableCell18.StylePriority.UseBorderColor = false;
			this.xrTableCell18.StylePriority.UseBorders = false;
			this.xrTableCell18.StylePriority.UseTextAlignment = false;
			this.xrTableCell18.Text = "المبلغ";
			this.xrTableCell18.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell18.TextFormatString = "{0:d}";
			this.xrTableCell18.Weight = 0.12491606414533182;
			this.xrTableCell19.BorderColor = Color.White;
			this.xrTableCell19.Borders = BorderSide.Right;
			this.xrTableCell19.Multiline = true;
			this.xrTableCell19.Name = "xrTableCell19";
			this.xrTableCell19.StylePriority.UseBorderColor = false;
			this.xrTableCell19.StylePriority.UseBorders = false;
			this.xrTableCell19.StylePriority.UseTextAlignment = false;
			this.xrTableCell19.Text = "التاريخ";
			this.xrTableCell19.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell19.Weight = 0.1342191940992037;
			this.xrTableCell20.BorderColor = Color.White;
			this.xrTableCell20.Borders = BorderSide.Right;
			this.xrTableCell20.Multiline = true;
			this.xrTableCell20.Name = "xrTableCell20";
			this.xrTableCell20.StylePriority.UseBorderColor = false;
			this.xrTableCell20.StylePriority.UseBorders = false;
			this.xrTableCell20.StylePriority.UseTextAlignment = false;
			this.xrTableCell20.Text = "الاسم";
			this.xrTableCell20.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell20.Weight = 0.17404307422814824;
			this.xrTableCell21.BorderColor = Color.White;
			this.xrTableCell21.Borders = BorderSide.Right;
			this.xrTableCell21.Multiline = true;
			this.xrTableCell21.Name = "xrTableCell21";
			this.xrTableCell21.StylePriority.UseBorderColor = false;
			this.xrTableCell21.StylePriority.UseBorders = false;
			this.xrTableCell21.StylePriority.UseTextAlignment = false;
			this.xrTableCell21.Text = "رقم الملف الضريبى";
			this.xrTableCell21.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell21.Weight = 0.1495056186221496;
			this.xrTableCell22.BorderColor = Color.White;
			this.xrTableCell22.Borders = BorderSide.Right;
			this.xrTableCell22.Multiline = true;
			this.xrTableCell22.Name = "xrTableCell22";
			this.xrTableCell22.StylePriority.UseBorderColor = false;
			this.xrTableCell22.StylePriority.UseBorders = false;
			this.xrTableCell22.StylePriority.UseTextAlignment = false;
			this.xrTableCell22.Text = "رقم الفاتوره";
			this.xrTableCell22.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell22.Weight = 0.10643703537387349;
			this.xrControlStyle1.BackColor = Color.LightGray;
			this.xrControlStyle1.Name = "xrControlStyle1";
			this.xrControlStyle1.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.objectDataSource1.Constructor = objectConstructorInfo;
			this.objectDataSource1.DataSource = typeof(PurchaseTaxesReportModel);
			this.objectDataSource1.Name = "objectDataSource1";
			this.GroupFooter1.Controls.AddRange(new XRControl[]
			{
				this.xrTable5
			});
			this.GroupFooter1.HeightF = 47.91667f;
			this.GroupFooter1.Name = "GroupFooter1";
			this.xrTable5.BackColor = Color.NavajoWhite;
			this.xrTable5.Font = new Font("Arial", 11.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTable5.ForeColor = Color.Black;
			this.xrTable5.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable5.Name = "xrTable5";
			this.xrTable5.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable5.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow11
			});
			this.xrTable5.SizeF = new SizeF(755.9874f, 40.82185f);
			this.xrTable5.StylePriority.UseBackColor = false;
			this.xrTable5.StylePriority.UseFont = false;
			this.xrTable5.StylePriority.UseForeColor = false;
			this.xrTableRow11.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell27,
				this.xrTableCell28,
				this.xrTableCell29,
				this.xrTableCell30,
				this.xrTableCell23
			});
			this.xrTableRow11.Name = "xrTableRow11";
			this.xrTableRow11.Weight = 0.5609756097560976;
			this.xrTableCell27.BorderColor = Color.White;
			this.xrTableCell27.Borders = BorderSide.Right;
			this.xrTableCell27.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Net])")
			});
			this.xrTableCell27.Multiline = true;
			this.xrTableCell27.Name = "xrTableCell27";
			this.xrTableCell27.StylePriority.UseBorderColor = false;
			this.xrTableCell27.StylePriority.UseBorders = false;
			this.xrTableCell27.StylePriority.UseTextAlignment = false;
			this.xrTableCell27.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell27.TextFormatString = "{0:#.00}";
			this.xrTableCell27.Weight = 0.026911589807490588;
			this.xrTableCell28.BorderColor = Color.White;
			this.xrTableCell28.Borders = BorderSide.Right;
			this.xrTableCell28.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([TaxAmount])")
			});
			this.xrTableCell28.Multiline = true;
			this.xrTableCell28.Name = "xrTableCell28";
			this.xrTableCell28.StylePriority.UseBorderColor = false;
			this.xrTableCell28.StylePriority.UseBorders = false;
			this.xrTableCell28.StylePriority.UseTextAlignment = false;
			this.xrTableCell28.Text = "قيمه الخصم";
			this.xrTableCell28.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell28.TextFormatString = "{0:#.00}";
			this.xrTableCell28.Weight = 0.026639272032905822;
			this.xrTableCell29.BorderColor = Color.White;
			this.xrTableCell29.Borders = BorderSide.Right;
			this.xrTableCell29.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Discount])")
			});
			this.xrTableCell29.Multiline = true;
			this.xrTableCell29.Name = "xrTableCell29";
			this.xrTableCell29.StylePriority.UseBorderColor = false;
			this.xrTableCell29.StylePriority.UseBorders = false;
			this.xrTableCell29.StylePriority.UseTextAlignment = false;
			this.xrTableCell29.Text = "قيمه الضريبه";
			this.xrTableCell29.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell29.TextFormatString = "{0:#.00}";
			this.xrTableCell29.Weight = 0.029436400029633006;
			this.xrTableCell30.BorderColor = Color.White;
			this.xrTableCell30.Borders = BorderSide.Right;
			this.xrTableCell30.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Total])")
			});
			this.xrTableCell30.Multiline = true;
			this.xrTableCell30.Name = "xrTableCell30";
			this.xrTableCell30.StylePriority.UseBorderColor = false;
			this.xrTableCell30.StylePriority.UseBorders = false;
			this.xrTableCell30.StylePriority.UseTextAlignment = false;
			this.xrTableCell30.Text = "ألصافي";
			this.xrTableCell30.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell30.TextFormatString = "{0:#.00}";
			this.xrTableCell30.Weight = 0.030176959340157808;
			this.xrTableCell23.BorderColor = Color.White;
			this.xrTableCell23.Borders = BorderSide.Right;
			this.xrTableCell23.Multiline = true;
			this.xrTableCell23.Name = "xrTableCell23";
			this.xrTableCell23.StylePriority.UseBorderColor = false;
			this.xrTableCell23.StylePriority.UseBorders = false;
			this.xrTableCell23.StylePriority.UseTextAlignment = false;
			this.xrTableCell23.Text = "المجموع";
			this.xrTableCell23.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell23.Weight = 0.1361965942797786;
			this.xrControlStyle2.BackColor = Color.FromArgb(224, 224, 224);
			this.xrControlStyle2.Name = "xrControlStyle2";
			this.xrControlStyle2.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.xrControlStyle3.Name = "xrControlStyle3";
			this.xrControlStyle3.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.xrControlStyle4.Name = "xrControlStyle4";
			this.xrControlStyle4.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			this.xrControlStyle5.BackColor = Color.FromArgb(224, 224, 224);
			this.xrControlStyle5.Name = "xrControlStyle5";
			this.xrControlStyle5.Padding = new PaddingInfo(0, 0, 0, 0, 100f);
			base.Bands.AddRange(new Band[]
			{
				this.TopMargin,
				this.BottomMargin,
				this.Detail,
				this.ReportHeader,
				this.GroupHeader1,
				this.GroupFooter1
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource1
			});
			base.DataSource = this.objectDataSource1;
			this.Font = new Font("Arial", 9.75f);
			base.Margins = new Margins(32, 36, 36, 38);
			base.PageHeight = 1169;
			base.PageWidth = 827;
			base.PaperKind = PaperKind.A4;
			base.StyleSheet.AddRange(new XRControlStyle[]
			{
				this.xrControlStyle1,
				this.xrControlStyle2,
				this.xrControlStyle3,
				this.xrControlStyle4,
				this.xrControlStyle5
			});
			base.Version = "20.2";
			((ISupportInitialize)this.xrTable3).EndInit();
			((ISupportInitialize)this.xrTable2).EndInit();
			((ISupportInitialize)this.xrTable1).EndInit();
			((ISupportInitialize)this.xrTable4).EndInit();
			((ISupportInitialize)this.objectDataSource1).EndInit();
			((ISupportInitialize)this.xrTable5).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x04001FDD RID: 8157
		private IContainer components = null;

		// Token: 0x04001FDE RID: 8158
		private TopMarginBand TopMargin;

		// Token: 0x04001FDF RID: 8159
		private BottomMarginBand BottomMargin;

		// Token: 0x04001FE0 RID: 8160
		private DetailBand Detail;

		// Token: 0x04001FE1 RID: 8161
		private ReportHeaderBand ReportHeader;

		// Token: 0x04001FE2 RID: 8162
		public XRTable xrTable2;

		// Token: 0x04001FE3 RID: 8163
		public XRTableRow xrTableRow5;

		// Token: 0x04001FE4 RID: 8164
		public XRTableCell Cell_ReportName;

		// Token: 0x04001FE5 RID: 8165
		public XRTableRow xrTableRow6;

		// Token: 0x04001FE6 RID: 8166
		public XRTableCell xrTableCell4;

		// Token: 0x04001FE7 RID: 8167
		public XRTableRow xrTableRow7;

		// Token: 0x04001FE8 RID: 8168
		public XRTableCell Cell_Filters;

		// Token: 0x04001FE9 RID: 8169
		public XRPictureBox xrPictureBox2;

		// Token: 0x04001FEA RID: 8170
		public XRTable xrTable1;

		// Token: 0x04001FEB RID: 8171
		public XRTableRow xrTableRow2;

		// Token: 0x04001FEC RID: 8172
		public XRTableCell xrTableCell13;

		// Token: 0x04001FED RID: 8173
		public XRTableRow xrTableRow3;

		// Token: 0x04001FEE RID: 8174
		public XRTableCell xrTableCell2;

		// Token: 0x04001FEF RID: 8175
		public XRTableRow xrTableRow4;

		// Token: 0x04001FF0 RID: 8176
		public XRTableCell xrTableCell3;

		// Token: 0x04001FF1 RID: 8177
		public XRTableRow xrTableRow1;

		// Token: 0x04001FF2 RID: 8178
		public XRTableCell xrTableCell1;

		// Token: 0x04001FF3 RID: 8179
		public XRTableRow xrTableRow8;

		// Token: 0x04001FF4 RID: 8180
		public XRTableCell xrTableCell6;

		// Token: 0x04001FF5 RID: 8181
		private GroupHeaderBand GroupHeader1;

		// Token: 0x04001FF6 RID: 8182
		private XRTable xrTable4;

		// Token: 0x04001FF7 RID: 8183
		private XRTableRow xrTableRow10;

		// Token: 0x04001FF8 RID: 8184
		private XRTableCell xrTableCell15;

		// Token: 0x04001FF9 RID: 8185
		private XRTableCell xrTableCell16;

		// Token: 0x04001FFA RID: 8186
		private XRTableCell xrTableCell17;

		// Token: 0x04001FFB RID: 8187
		private XRTableCell xrTableCell18;

		// Token: 0x04001FFC RID: 8188
		private XRTableCell xrTableCell19;

		// Token: 0x04001FFD RID: 8189
		private XRTableCell xrTableCell20;

		// Token: 0x04001FFE RID: 8190
		private XRTableCell xrTableCell21;

		// Token: 0x04001FFF RID: 8191
		private XRTableCell xrTableCell22;

		// Token: 0x04002000 RID: 8192
		private XRTable xrTable3;

		// Token: 0x04002001 RID: 8193
		private XRTableRow xrTableRow9;

		// Token: 0x04002002 RID: 8194
		private XRTableCell xrTableCell5;

		// Token: 0x04002003 RID: 8195
		private XRTableCell xrTableCell7;

		// Token: 0x04002004 RID: 8196
		private XRTableCell xrTableCell8;

		// Token: 0x04002005 RID: 8197
		private XRTableCell xrTableCell9;

		// Token: 0x04002006 RID: 8198
		private XRTableCell xrTableCell10;

		// Token: 0x04002007 RID: 8199
		private XRTableCell xrTableCell11;

		// Token: 0x04002008 RID: 8200
		private XRTableCell xrTableCell12;

		// Token: 0x04002009 RID: 8201
		private XRTableCell xrTableCell14;

		// Token: 0x0400200A RID: 8202
		private XRControlStyle xrControlStyle1;

		// Token: 0x0400200B RID: 8203
		private ObjectDataSource objectDataSource1;

		// Token: 0x0400200C RID: 8204
		private GroupFooterBand GroupFooter1;

		// Token: 0x0400200D RID: 8205
		private XRTable xrTable5;

		// Token: 0x0400200E RID: 8206
		private XRTableRow xrTableRow11;

		// Token: 0x0400200F RID: 8207
		private XRTableCell xrTableCell27;

		// Token: 0x04002010 RID: 8208
		private XRTableCell xrTableCell28;

		// Token: 0x04002011 RID: 8209
		private XRTableCell xrTableCell29;

		// Token: 0x04002012 RID: 8210
		private XRTableCell xrTableCell30;

		// Token: 0x04002013 RID: 8211
		private XRTableCell xrTableCell23;

		// Token: 0x04002014 RID: 8212
		private XRControlStyle xrControlStyle2;

		// Token: 0x04002015 RID: 8213
		private XRControlStyle xrControlStyle3;

		// Token: 0x04002016 RID: 8214
		private XRControlStyle xrControlStyle4;

		// Token: 0x04002017 RID: 8215
		private XRControlStyle xrControlStyle5;
	}
}
