﻿using DevExpress.XtraReports.UI;
using System.ComponentModel;

namespace EasyStock.Reports
{
    public partial class AccountsBalanceReports : MasterReport
    {
        public AccountsBalanceReports()
        {
            InitializeComponent();
        }
        private void xrTableCell10_BeforePrint(object sender, CancelEventArgs e)
        {
        }

        private void xrTableCell9_PrintOnPage(object sender, PrintOnPageEventArgs e)
        {
            XRTableCell cell = sender as XRTableCell;
            bool flag = cell.Text == "0";
            if (flag)
            {
                cell.Text = "";
            }
        }
    }
}
