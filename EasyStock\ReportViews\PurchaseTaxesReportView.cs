﻿using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    // Token: 0x02000330 RID: 816
    public partial class PurchaseTaxesReportView : XtraReportForm
    {
        public PurchaseTaxesReportView()
        : base(new ReportFilter[2]
        {
            ReportFilter.Date,
            ReportFilter.Vendor
        })
        {
            InitializeComponent();
        }

        internal override bool ValidateFilters()
        {
            if (!base.FiltersForm.hasStartDate)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                return false;
            }
            if (!base.FiltersForm.hasEndtDate)
            {
                base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                return false;
            }
            return true;
        }

        public override void RefreshDataSource()
        {
            if (ValidateFilters())
            {
                PurchaseTaxReport rpt = new PurchaseTaxReport();
                ICollection<PurchaseTaxsReportModel> dataSource = (ICollection<PurchaseTaxsReportModel>)(rpt.DataSource = GetData());
                rpt.SetCompanyInfo(dataSource.FirstOrDefault());
                rpt.Cell_ReportName.Text = Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<PurchaseTaxsReportModel> GetData()
        {
            int[] AllVendores = base.FiltersForm.Vendors.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<PurchaseInvoice> filterdInvoices = context.PurchaseInvoices.Where((PurchaseInvoice inv) => DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate);
                if (AllVendores != null && AllVendores.Count() > 0)
                {
                    filterdInvoices = filterdInvoices.Where((PurchaseInvoice Ven) => AllVendores.Contains(Ven.VendorID));
                }
                List<PurchaseTaxsReportModel> data = (from inv in filterdInvoices
                                                      from Vend in context.Vendors.Where((Vendor vend) => vend.ID == inv.VendorID).DefaultIfEmpty()
                                                      select new PurchaseTaxsReportModel
                                                      {
                                                          InvoicesCode = inv.Code,
                                                          InvoiceDate = inv.Date,
                                                          TaxAmount = inv.Tax,
                                                          Discount = inv.Discount,
                                                          Net = inv.Total + inv.Tax + inv.OtherExpenses - inv.Discount,
                                                          Total = inv.Total,
                                                          Name = ((Vend != null) ? Vend.Name : ""),
                                                          TaxFileNumber = ((Vend != null) ? Vend.TaxFileNumber : "")
                                                      }).ToList();
                IQueryable<RevExpEntry> filterdExpences = from detail in context.RevExpEntries.Include((RevExpEntry x) => x.Personal)
                                                          where detail.Taxable && (int)detail.EntryType == 9 && DbFunctions.TruncateTime(detail.DateTime) >= FiltersForm.startDate && DbFunctions.TruncateTime(detail.DateTime) <= FiltersForm.endtDate
                                                          select detail;
                IEnumerable<PurchaseTaxsReportModel> data2 = from detail in filterdExpences.ToList()
                                                             select new PurchaseTaxsReportModel
                                                             {
                                                                 InvoicesCode = detail.InvoiceCode,
                                                                 InvoiceDate = detail.DateTime,
                                                                 TaxAmount = detail.TaxValue,
                                                                 Discount = detail.DiscountValue,
                                                                 Net = detail.TotalAfterTax,
                                                                 Total = detail.Total,
                                                                 Name = ((detail.Personal != null) ? detail.Personal.Name : ""),
                                                                 TaxFileNumber = ((detail.Personal != null) ? detail.Personal.TaxFileNumber : "")
                                                             };
                data.AddRange(data2.ToList());
                return data.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }

        private void PurchaseTaxesReportView_Load(object sender, EventArgs e)
        {
        }
    }
}
