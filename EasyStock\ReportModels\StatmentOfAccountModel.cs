﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class StatmentOfAccountModel
    {
        [Display(Name = "Compte")]
        public string Account { get; set; }

        [Display(Name = "Numéro de compte")]
        public int AccountID { get; set; }

        [Display(Name = "Code de l'écriture")]
        public int ID { get; set; }

        [Display(Name = "Numéro de l'écriture")]
        public string Code { get; set; }

        [Display(Name = "Date")]
        public DateTime Date { get; set; }

        [Display(Name = "Dépôt")]
        public int StoreID { get; set; }

        [Display(Name = "Source de l'écriture")]
        public SystemProcess ProcessType { get; set; }

        [Display(Name = "Code de la source")]
        public int ProcessID { get; set; }

        [Display(Name = "Énoncé")]
        public string Statement { get; set; }

        [Display(Name = "Débit")]
        public double Debit { get; set; }

        [Display(Name = "Crédit")]
        public double Credit { get; set; }

        [Display(Name = "Débit local")]
        public double LocalDebit
        {
            get
            {
                return this.Debit * this.CurrencyRate;
            }
        }

        [Display(Name = "Crédit local")]
        public double LocalCredit
        {
            get
            {
                return this.Credit * this.CurrencyRate;
            }
        }

        [Display(Name = "Code de la monnaie")]
        public int CurrencyID { get; set; }

        [Display(Name = "Monnaie")]
        public string Currency { get; set; }

        [Display(Name = "Centre de coût")]
        public string CostCenter { get; set; }

        [Display(Name = "Taux de change")]
        public double CurrencyRate { get; set; }

        [Display(Name = "Solde")]
        public double Balance { get; set; }

        [Display(Name = "Date d'échéance")]
        public DateTime? DueDate { get; set; }
    }
}
