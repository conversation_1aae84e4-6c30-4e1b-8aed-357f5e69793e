﻿using EasyStock.Controller;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data.Entity;
using System.Linq;

namespace EasyStock.ReportModels
{
    public class ProductInOutBalanceModel : CompanyInfoReportModel
    {
        public class Filter
        {
            public List<int> productIDs { get; set; }

            public List<int> brancheIDs { get; set; }

            public DateTime fromDate { get; set; }

            public DateTime toDate { get; set; }
        }

        [Display(Name = "N°")]
        public int ID { get; set; }

        [Display(Name = "Code article")]
        public int ProductID { get; set; }

        [Display(Name = "Nom de l'article")]
        public string ProductName { get; set; }

        [Display(Name = "Quantité initiale", GroupName = "Solde initial")]
        public double FirstQuantity { get; set; }

        [Display(Name = "Valeur initiale", GroupName = "Solde initial")]
        public double FirstValue { get; set; }

        [Display(Name = "Quantité entrante", GroupName = "Entrées")]
        public double InQuantity { get; set; }

        [Display(Name = "Valeur entrante", GroupName = "Entrées")]
        public double InValue { get; set; }

        [Display(Name = "Quantité sortante", GroupName = "Sorties")]
        public double OutQuantity { get; set; }

        [Display(Name = "Valeur sortante", GroupName = "Sorties")]
        public double OutValue { get; set; }

        [Display(Name = "Quantité finale", GroupName = "Solde final")]
        public double FinalQuantity => FirstQuantity + InQuantity - OutQuantity;

        [Display(Name = "Valeur finale", GroupName = "Solde final")]
        public double FinalValue => FirstValue + InValue - OutValue;

        public static ICollection<ProductInOutBalanceModel> GetProductInOut(Filter filter)
        {
            using ERPDataContext db = new ERPDataContext();
            filter.fromDate = filter.fromDate.Date;
            filter.toDate = filter.toDate.Date;
            IQueryable<Product> products = db.Products.Where((Product x) => (int)x.Type != 1).AsQueryable();
            List<int> productIDs = filter.productIDs;
            if (productIDs != null && productIDs.Count > 0)
            {
                products = products.Where((Product x) => filter.productIDs.Contains(x.ID)).AsQueryable();
            }
            IQueryable<ProductTransaction> productTransaction = db.ProductTransactions.Where(x => (int)x.TransactionState == 2).AsQueryable();
            List<int> brancheIDs = filter.brancheIDs;
            if (brancheIDs != null && brancheIDs.Count > 0)
            {
                productTransaction = productTransaction.Where(x => filter.brancheIDs.Contains(x.StoreID)).AsQueryable();
            }
            IQueryable<ProductTransaction> beforePeriodTransQ = productTransaction.Where(x => DbFunctions.TruncateTime(x.Date) < filter.fromDate);
            productTransaction = productTransaction.Where(x => DbFunctions.TruncateTime(x.Date) >= filter.fromDate && DbFunctions.TruncateTime(x.Date) <= filter.toDate);
            int id = 1;
            List<ProductInOutBalanceModel> productInOutBalances = products.Select((Product p) => new ProductInOutBalanceModel
            {
                ProductID = p.ID,
                ProductName = p.Name,
                FirstQuantity = ((from x in beforePeriodTransQ
                                  where x.ProductID == p.ID && (int)x.TransactionType == 0
                                  select (double?)x.Quantity * (double?)x.Factor).Sum() ?? 0.0) - ((from x in beforePeriodTransQ
                                                                                                    where x.ProductID == p.ID && (int)x.TransactionType == 1
                                                                                                    select (double?)x.Quantity * (double?)x.Factor).Sum() ?? 0.0),
                FirstValue = ((from x in beforePeriodTransQ
                               where x.ProductID == p.ID && (int)x.TransactionType == 0
                               select (double?)x.CostValue * (double?)x.Quantity).Sum() ?? 0.0) - ((from x in beforePeriodTransQ
                                                                                                    where x.ProductID == p.ID && (int)x.TransactionType == 1
                                                                                                    select (double?)x.CostValue * (double?)x.Quantity).Sum() ?? 0.0),
                InQuantity = ((from x in productTransaction
                               where x.ProductID == p.ID && (int)x.TransactionType == 0
                               select (double?)x.Quantity * (double?)x.Factor).Sum() ?? 0.0),
                InValue = ((from x in productTransaction
                            where x.ProductID == p.ID && (int)x.TransactionType == 0
                            select (double?)x.CostValue * (double?)x.Quantity).Sum() ?? 0.0),
                OutQuantity = ((from x in productTransaction
                                where x.ProductID == p.ID && (int)x.TransactionType == 1
                                select (double?)x.Quantity * (double?)x.Factor).Sum() ?? 0.0),
                OutValue = ((from x in productTransaction
                             where x.ProductID == p.ID && (int)x.TransactionType == 1
                             select (double?)x.CostValue * (double?)x.Quantity).Sum() ?? 0.0)
            }).ToList();
            productInOutBalances.ForEach(delegate (ProductInOutBalanceModel x)
            {
                x.ID = id++;
            });
            return productInOutBalances;
        }
    }
}
