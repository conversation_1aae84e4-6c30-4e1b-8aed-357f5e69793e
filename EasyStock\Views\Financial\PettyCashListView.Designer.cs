﻿namespace EasyStock.Views.Financial
{
	// Token: 0x020002E2 RID: 738
	public partial class PettyCashListView : global::EasyStock.MainViews.MasterForm
	{
		// Token: 0x06001286 RID: 4742 RVA: 0x001435EC File Offset: 0x001417EC
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001287 RID: 4743 RVA: 0x00143624 File Offset: 0x00141824
		private void InitializeComponent()
		{
            this.components = new System.ComponentModel.Container();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.pettyCashBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBranchID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoBranch = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.branchBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colHolderID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repoholder = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.pettyCashHolderBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colCostCenter = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repocostcenter = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.costCenterBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colAmount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCurrencyID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repocurrency = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.currencyBindingSource = new System.Windows.Forms.BindingSource(this.components);
            this.colIsClosed = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoBranch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.branchBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoholder)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashHolderBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repocostcenter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.costCenterBindingSource)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repocurrency)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.currencyBindingSource)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.DataSource = this.pettyCashBindingSource;
            this.gridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gridControl1.Location = new System.Drawing.Point(0, 26);
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repoholder,
            this.repoBranch,
            this.repocostcenter,
            this.repocurrency});
            this.gridControl1.Size = new System.Drawing.Size(800, 400);
            this.gridControl1.TabIndex = 15;
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // pettyCashBindingSource
            // 
            this.pettyCashBindingSource.DataSource = typeof(EasyStock.Models.PettyCash);
            // 
            // gridView1
            // 
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colID,
            this.colBranchID,
            this.colType,
            this.colHolderID,
            this.colCostCenter,
            this.colAmount,
            this.colCurrencyID,
            this.colIsClosed,
            this.colNotes});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            // 
            // colID
            // 
            this.colID.FieldName = "ID";
            this.colID.Name = "colID";
            this.colID.OptionsColumn.ReadOnly = true;
            this.colID.Visible = true;
            this.colID.VisibleIndex = 0;
            // 
            // colBranchID
            // 
            this.colBranchID.ColumnEdit = this.repoBranch;
            this.colBranchID.FieldName = "BranchID";
            this.colBranchID.Name = "colBranchID";
            this.colBranchID.Visible = true;
            this.colBranchID.VisibleIndex = 1;
            // 
            // repoBranch
            // 
            this.repoBranch.AutoHeight = false;
            this.repoBranch.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoBranch.DataSource = this.branchBindingSource;
            this.repoBranch.DisplayMember = "Name";
            this.repoBranch.Name = "repoBranch";
            this.repoBranch.ValueMember = "ID";
            // 
            // branchBindingSource
            // 
            this.branchBindingSource.DataSource = typeof(EasyStock.Models.Branch);
            // 
            // colType
            // 
            this.colType.FieldName = "Type";
            this.colType.Name = "colType";
            this.colType.Visible = true;
            this.colType.VisibleIndex = 2;
            // 
            // colHolderID
            // 
            this.colHolderID.ColumnEdit = this.repoholder;
            this.colHolderID.FieldName = "HolderID";
            this.colHolderID.Name = "colHolderID";
            this.colHolderID.Visible = true;
            this.colHolderID.VisibleIndex = 3;
            // 
            // repoholder
            // 
            this.repoholder.AutoHeight = false;
            this.repoholder.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repoholder.DataSource = this.pettyCashHolderBindingSource;
            this.repoholder.DisplayMember = "Name";
            this.repoholder.Name = "repoholder";
            this.repoholder.ValueMember = "ID";
            // 
            // pettyCashHolderBindingSource
            // 
            this.pettyCashHolderBindingSource.DataSource = typeof(EasyStock.Models.PettyCashHolder);
            // 
            // colCostCenter
            // 
            this.colCostCenter.ColumnEdit = this.repocostcenter;
            this.colCostCenter.FieldName = "CostCenter";
            this.colCostCenter.Name = "colCostCenter";
            this.colCostCenter.Visible = true;
            this.colCostCenter.VisibleIndex = 4;
            // 
            // repocostcenter
            // 
            this.repocostcenter.AutoHeight = false;
            this.repocostcenter.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repocostcenter.DataSource = this.costCenterBindingSource;
            this.repocostcenter.DisplayMember = "Name";
            this.repocostcenter.Name = "repocostcenter";
            this.repocostcenter.ValueMember = "ID";
            // 
            // costCenterBindingSource
            // 
            this.costCenterBindingSource.DataSource = typeof(EasyStock.Models.CostCenter);
            // 
            // colAmount
            // 
            this.colAmount.FieldName = "Amount";
            this.colAmount.Name = "colAmount";
            this.colAmount.Visible = true;
            this.colAmount.VisibleIndex = 5;
            // 
            // colCurrencyID
            // 
            this.colCurrencyID.ColumnEdit = this.repocurrency;
            this.colCurrencyID.FieldName = "CurrencyID";
            this.colCurrencyID.Name = "colCurrencyID";
            this.colCurrencyID.Visible = true;
            this.colCurrencyID.VisibleIndex = 6;
            // 
            // repocurrency
            // 
            this.repocurrency.AutoHeight = false;
            this.repocurrency.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.repocurrency.DataSource = this.currencyBindingSource;
            this.repocurrency.DisplayMember = "Name";
            this.repocurrency.Name = "repocurrency";
            this.repocurrency.ValueMember = "ID";
            // 
            // currencyBindingSource
            // 
            this.currencyBindingSource.DataSource = typeof(EasyStock.Models.Currency);
            // 
            // colIsClosed
            // 
            this.colIsClosed.FieldName = "IsClosed";
            this.colIsClosed.Name = "colIsClosed";
            this.colIsClosed.Visible = true;
            this.colIsClosed.VisibleIndex = 7;
            // 
            // colNotes
            // 
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            this.colNotes.Visible = true;
            this.colNotes.VisibleIndex = 8;
            // 
            // PettyCashListView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Controls.Add(this.gridControl1);
            this.Name = "PettyCashListView";
            this.Text = "Liste des avances financières";
            this.Controls.SetChildIndex(this.gridControl1, 0);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoBranch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.branchBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repoholder)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pettyCashHolderBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repocostcenter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.costCenterBindingSource)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repocurrency)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.currencyBindingSource)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x0400186B RID: 6251
		private global::System.ComponentModel.IContainer components = null;

		// Token: 0x0400186C RID: 6252
		private global::DevExpress.XtraGrid.GridControl gridControl1;

		// Token: 0x0400186D RID: 6253
		private global::DevExpress.XtraGrid.Views.Grid.GridView gridView1;

		// Token: 0x0400186E RID: 6254
		private global::DevExpress.XtraGrid.Columns.GridColumn colID;

		// Token: 0x0400186F RID: 6255
		private global::DevExpress.XtraGrid.Columns.GridColumn colBranchID;

		// Token: 0x04001870 RID: 6256
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoBranch;

		// Token: 0x04001871 RID: 6257
		private global::DevExpress.XtraGrid.Columns.GridColumn colType;

		// Token: 0x04001872 RID: 6258
		private global::DevExpress.XtraGrid.Columns.GridColumn colHolderID;

		// Token: 0x04001873 RID: 6259
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repoholder;

		// Token: 0x04001874 RID: 6260
		private global::DevExpress.XtraGrid.Columns.GridColumn colCostCenter;

		// Token: 0x04001875 RID: 6261
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repocostcenter;

		// Token: 0x04001876 RID: 6262
		private global::DevExpress.XtraGrid.Columns.GridColumn colAmount;

		// Token: 0x04001877 RID: 6263
		private global::DevExpress.XtraGrid.Columns.GridColumn colCurrencyID;

		// Token: 0x04001878 RID: 6264
		private global::DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repocurrency;

		// Token: 0x04001879 RID: 6265
		private global::DevExpress.XtraGrid.Columns.GridColumn colIsClosed;

		// Token: 0x0400187A RID: 6266
		private global::DevExpress.XtraGrid.Columns.GridColumn colNotes;

		// Token: 0x0400187B RID: 6267
		private global::System.Windows.Forms.BindingSource pettyCashBindingSource;

		// Token: 0x0400187C RID: 6268
		private global::System.Windows.Forms.BindingSource branchBindingSource;

		// Token: 0x0400187D RID: 6269
		private global::System.Windows.Forms.BindingSource pettyCashHolderBindingSource;

		// Token: 0x0400187E RID: 6270
		private global::System.Windows.Forms.BindingSource costCenterBindingSource;

		// Token: 0x0400187F RID: 6271
		private global::System.Windows.Forms.BindingSource currencyBindingSource;
	}
}
