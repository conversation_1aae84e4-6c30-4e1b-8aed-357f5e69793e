﻿namespace EasyStock.ReportViews
{
	// Token: 0x02000302 RID: 770
	public partial class SalesProductCatergoriesReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x06001316 RID: 4886 RVA: 0x00151028 File Offset: 0x0014F228
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001317 RID: 4887 RVA: 0x00151060 File Offset: 0x0014F260
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 53);
            this.documentViewer1.Size = new System.Drawing.Size(800, 376);
            // 
            // SalesProductCatergoriesReportView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Name = "SalesProductCatergoriesReportView";
            this.Text = "Relevé des ventes par famille d\'articles";
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x0400191D RID: 6429
		private global::System.ComponentModel.IContainer components = null;
	}
}
