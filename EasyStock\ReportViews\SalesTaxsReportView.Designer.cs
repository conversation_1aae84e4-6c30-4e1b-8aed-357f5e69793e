﻿namespace EasyStock.ReportViews
{
	// Token: 0x0200033B RID: 827
	public partial class SalesTaxsReportView : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x06001409 RID: 5129 RVA: 0x0016420C File Offset: 0x0016240C
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x0600140A RID: 5130 RVA: 0x00164244 File Offset: 0x00162444
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 53);
            this.documentViewer1.Size = new System.Drawing.Size(800, 376);
            // 
            // SalesTaxsReportView
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 450);
            this.Name = "SalesTaxsReportView";
            this.Text = "Déclaration fiscale des factures de vente";
            this.Load += new System.EventHandler(this.SalesTaxsReportView_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

		}

		// Token: 0x040019A5 RID: 6565
		private global::System.ComponentModel.IContainer components = null;
	}
}
