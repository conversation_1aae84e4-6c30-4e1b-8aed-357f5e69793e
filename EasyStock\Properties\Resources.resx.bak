﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="actions_zoom1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\actions_zoom1.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="security_unlock" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\security_unlock.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="electronics_desktopmac" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\electronics_desktopmac.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="duplicatevalues" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\duplicatevalues.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="editwrappoints" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\editwrappoints.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="actions_zoom2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\actions_zoom2.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="actions_remove" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\actions_remove.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="bo_address" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bo_address.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="showcontainerheader" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\showcontainerheader.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="touchmode_32x32" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\touchmode_32x32.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="preview" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\preview.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Untitled-2AAAAAAhaaaaaaay" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Untitled-2AAAAAAhaaaaaaay.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="copy" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\copy.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="selecttool_pantool" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\selecttool_pantool.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="sales" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\sales.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="checkbox" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\checkbox.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="selectdatamember" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\selectdatamember.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="refreshallpivottable" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\refreshallpivottable.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="business_calculator" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\business_calculator.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="actions_zoom" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\actions_zoom.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="hide_16x16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\hide_16x16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="touchmode_16x16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\touchmode_16x16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="bo_address1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bo_address1.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="convertto" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\convertto.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="print" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\print.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tableconverttorange" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\tableconverttorange.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="verticallines" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\verticallines.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="exportas" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\exportas.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="saveas" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\saveas.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="managedatasource" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\managedatasource.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="viewsettings" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\viewsettings.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="new" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\new.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="import" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\import.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="fittopage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\fittopage.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="time" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\time.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="update" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\update.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="replace" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\replace.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="printlayoutview" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\printlayoutview.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="open2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\open2.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="duplicatevalues1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\duplicatevalues1.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="bo_product_group" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\bo_product_group.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="show_16x16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\show_16x16.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="fontsize" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\fontsize.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="delete" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\delete.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="enablesearch" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\enablesearch.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="clearheaderandfooter" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\clearheaderandfooter.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="save" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\save.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="electronics_keyboard" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\electronics_keyboard.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="filterquery" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\filterquery.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="compressweekend" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\compressweekend.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="private" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\private.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="actions_add" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\actions_add.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="exporttoxls" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\exporttoxls.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="actions_edit" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\actions_edit.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="save1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\save1.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="printcollated" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\printcollated.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="3d glass window logo mockup" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\3d glass window logo mockup.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="select" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\select.svg;DevExpress.Utils.Svg.SvgImage, DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lmc" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\lmc.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>