﻿using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class BOMDEtails
    {
        [Display(Name = "Nom du produit")]
        public string ProductName { get; set; }

        [Display(Name = "Unité")]
        public string UnitName { get; set; }

        [Display(Name = "Qté")]
        [Range(0.001, 1.7976931348623157E+308, ErrorMessage = "La quantité doit être supérieure à 0.001")]
        public double Quantity { get; set; }
    }
}
