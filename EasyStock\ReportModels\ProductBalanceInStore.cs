﻿using EasyStock.Controller;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace EasyStock.ReportModels
{
    public class ProductBalanceInStore
    {
        [Display(Name = "Article Code")]
        public int ProductID { get; set; }

        [Display(Name = "Nom du Article")]
        public string ProductName { get; set; }

        [Display(Name = "Dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Stock en unité de base")]
        public double Balance { get; set; }

        [Display(Name = "Code du magasin")]
        public int StoreID { get; set; }

        public static List<ProductBalanceInStore> GetProductBalance(int productID)
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                var q1 = from x in db.ProductTransactions
                         where (int)x.TransactionState == 2
                         select x into pt
                         where pt.ProductID == productID
                         select new
                         {
                             ProductID = pt.ProductID,
                             StoreID = pt.StoreID,
                             RawQty = pt.Quantity * pt.Factor,
                             TransactionType = pt.TransactionType
                         };
                IQueryable<ProductBalanceInStore> q2 = from q in q1
                                                       group q by new { q.ProductID, q.StoreID } into g
                                                       join pr in db.Products on g.Key.ProductID equals pr.ID
                                                       join br in db.Stores on g.Key.StoreID equals br.ID
                                                       select new ProductBalanceInStore
                                                       {
                                                           ProductID = g.Key.ProductID,
                                                           ProductName = pr.Name,
                                                           StoreName = br.Name,
                                                           StoreID = br.ID,
                                                           Balance = (g.Where(x => (int)x.TransactionType == 0).Sum(x => (double?)x.RawQty) ?? 0.0) - (g.Where(x => (int)x.TransactionType == 1).Sum(x => (double?)x.RawQty) ?? 0.0)
                                                       };
                return q2.ToList();
            }
        }
    }
}
