﻿using System;
using System.ComponentModel;
using System.Data.Entity;
using System.Data.Entity.Migrations;
using System.Drawing;
using System.Linq;
using System.Linq.Expressions;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraDataLayout;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;

namespace EasyStock.Views.Financial
{
	public partial class PettyCashHolderView : MasterForm
	{
		public static PettyCashHolderView Instance
		{
			get
			{
				bool flag = PettyCashHolderView.instance == null || PettyCashHolderView.instance.IsDisposed;
				if (flag)
				{
					PettyCashHolderView.instance = new PettyCashHolderView();
				}
				return PettyCashHolderView.instance;
			}
		}

		public PettyCashHolder Holder
		{
			get
			{
				return this.pettyCashHolderBindingSource.Current as PettyCashHolder;
			}
			set
			{
				this.pettyCashHolderBindingSource.DataSource = value;
			}
		}

		public PettyCashHolderView()
		{
			this.InitializeComponent();
			this.db = new ERPDataContext();
			base.DisableValidation(this.dataLayoutControl1);
			this.db = new ERPDataContext();
			this.gridView1.SetAlternatingColors();
			base.Shown += this.GroupView_Shown;
			this.gridView1.DoubleClick += this.GridView1_DoubleClick;
		}

		private void GroupView_Shown(object sender, EventArgs e)
		{
			bool flag = this.Holder == null;
			if (flag)
			{
				this.New();
			}
		}

		private void GridView1_DoubleClick(object sender, EventArgs e)
		{
			PettyCashHolder row = this.gridView1.GetRow(this.gridView1.FocusedRowHandle) as PettyCashHolder;
			bool flag = row != null;
			if (flag)
			{
				this.GoTo(row.ID);
			}
		}

		public void GoTo(int id)
		{
			PettyCashHolder sourceDepr = this.db.PettyCashHolders.Include((PettyCashHolder x) => x.TemporaryAccount).Include((PettyCashHolder x) => x.PermenantAccount).SingleOrDefault((PettyCashHolder x) => x.ID == id);
			bool flag = sourceDepr != null;
			if (flag)
			{
				this.Holder = sourceDepr;
				this.DataChanged = false;
				base.IsNew = false;
			}
			else
			{
                Vip.Notification.Alert.ShowError("Le document est introuvable");
            }
        }

		public override void New()
		{
			bool flag = ERPDataContext.SystemSettings.PermanentPettyCashAccount <= 0 || ERPDataContext.SystemSettings.PermanentPettyCashAccount <= 0;
			if (flag)
			{
                Vip.Notification.Alert.ShowError("Les comptes d'avance doivent être définis à partir de l'écran des paramètres système");

                base.Close();
			}
			else
			{
				this.Holder = new PettyCashHolder();
				this.Holder.PermenantAccount = new Account
				{
					ParentID = new int?(ERPDataContext.SystemSettings.PermanentPettyCashAccount),
					CanEdit = false,
					Note = "Le compte a été créé par le système",
					Secrecy = SecracyLevel.Normal
				};
				this.Holder.TemporaryAccount = new Account
				{
					ParentID = new int?(ERPDataContext.SystemSettings.TemporaryPettyCashAccount),
					CanEdit = false,
					Note = "Le compte a été créé par le système",
					Secrecy = SecracyLevel.Normal
				};
				base.New();
			}
		}

		public override void Save()
		{
			base.EnableValidation(this.dataLayoutControl1);
			bool flag = !this.ValidateChildren();
			if (!flag)
			{
				base.DisableValidation(this.dataLayoutControl1);
				bool flag2 = PettyCashHolderView.CheckForDuplcation(this.Holder);
				if (!flag2)
				{
					bool flag3 = this.Holder.ID == 0;
					if (flag3)
					{
						this.Holder.PermenantAccount.Number = AccountHelper.GetNewNumber(this.Holder.PermenantAccount.ParentID.Value);
						this.Holder.TemporaryAccount.Number = AccountHelper.GetNewNumber(this.Holder.TemporaryAccount.ParentID.Value);
					}
                    this.Holder.PermenantAccount.Name = this.Holder.Name + " - Avance permanente";
                    this.Holder.TemporaryAccount.Name = this.Holder.Name + " - Avance temporaire";

                    this.db.PettyCashHolders.AddOrUpdate(new PettyCashHolder[]
					{
						this.Holder
					});
					this.db.SaveChanges();
					base.Save();
					this.RefreshData();
				}
			}
		}
		public static bool CheckForDuplcation(PettyCashHolder personal)
		{
			using ERPDataContext db = new ERPDataContext();
			IQueryable<Customer> personals = db.Customers.Where((Customer x) => x.ID != personal.ID);
			int names = personals.Where((Customer x) => x.Name.Trim() == personal.Name.Trim()).Count();
			if (names > 0)
			{
                XtraMessageBox.Show("Le nom est déjà enregistré", "Détenteur de l'avance financière", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            return names > 0;
		}

		public override void RefreshData()
		{
			this.gridControl1.DataSource = this.db.PettyCashHolders.ToList<PettyCashHolder>();
			base.RefreshData();
		}

		public override void Delete()
		{
		}

		private static PettyCashHolderView instance;

		private ERPDataContext db;
	}
}
