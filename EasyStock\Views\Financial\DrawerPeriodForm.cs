﻿using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraLayout.Utils;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Windows.Forms;

namespace EasyStock.Views.Financial
{
    public partial class DrawerPeriodForm : MasterForm
    {
        public static DrawerPeriodForm Instance
        {
            get
            {
                if (instance == null || instance.IsDisposed)
                {
                    instance = new DrawerPeriodForm();
                }
                return instance;
            }
        }

        internal DrawerPeriod period
        {
            get
            {
                return drawerPeriodBindingSource.Current as DrawerPeriod;
            }
            set
            {
                drawerPeriodBindingSource.DataSource = value;
            }
        }

        public DrawerPeriodForm()
        {
            InitializeComponent();
            base.Shown += DrawerPeriodForm_Shown;
            lyc_OpenPeriodButton.Visibility = LayoutVisibility.Never;
            lyc_ClosePeriodButton.Visibility = LayoutVisibility.Never;
            BranchIDLookUpEdit.EditValueChanged += BranchIDLookUpEdit_EditValueChanged;
        }

        private void BranchIDLookUpEdit_EditValueChanged(object sender, EventArgs e)
        {
            object editValue = BranchIDLookUpEdit.EditValue;
            if (!(editValue is int))
            {
                return;
            }
            int branchID = (int)editValue;
            if (true)
            {
                DrawerIDLookUpEdit.BindToDataSource(CurrentSession.AccessableDrawers.Where((Drawer x) => !x.BranchID.HasValue || x.BranchID == branchID || x.BranchID == 0).ToList());
            }
        }

        private void DrawerPeriodForm_Shown(object sender, EventArgs e)
        {
            btn_Save.Visibility = BarItemVisibility.Never;
            BindDateChanged(dataLayoutControl1);
            DisableValidation(dataLayoutControl1);
            if (period == null)
            {
                New();
            }
            RemainingBalanceTextEdit.EditValueChanged += RemainingBalanceTextEdit_EditValueChanged;
        }

        private void RemainingBalanceTextEdit_EditValueChanged(object sender, EventArgs e)
        {
            if (RemainingBalanceTextEdit.ContainsFocus && period != null)
            {
                period.TransferdBalance = period.ActualBalance - period.RemainingBalance;
            }
        }

        public override void ApplyUserSettings(UserSettingsProfile profile)
        {
            ClosingDrwerIDLookUpEdit.Enabled = CurrentSession.CurrentUser.SettingsProfile.CanChangeDrawer;
            DrawerIDLookUpEdit.Enabled = CurrentSession.CurrentUser.SettingsProfile.CanChangeDrawer;
            base.ApplyUserSettings(profile);
        }

        public override void New()
        {
        }

        public override void Save()
        {
            base.Save();
        }

        public override void Delete()
        {
            if (period != null && period.ID != 0)
            {
                Delete(new List<int> { period.ID });
                New();
            }
        }

        public static void Delete(List<int> ids)
        {
            ERPDataContext db = new ERPDataContext();
            List<int> PassList = new List<int>();
            List<string> log = new List<string>();
            foreach (int item in ids)
            {
                string Msg = string.Empty;
                if (CheckIfInvoiceCanBeDeleted(item, out Msg))
                {
                    PassList.Add(item);
                }
                log.Add(Msg);
            }
            ids = PassList;
            IQueryable<DrawerPeriod> entries = db.DrawerPeriods.Where((DrawerPeriod d) => ids.Contains(d.ID));
            IQueryable<DrawerPeriodTransSummeryItem> summaries = entries.Include((DrawerPeriod x) => x.Summery).SelectMany((DrawerPeriod x) => x.Summery);
            IQueryable<Journal> journals = db.Journals.Where((Journal j) => entries.Select((DrawerPeriod x) => x.ID).Contains(j.ProcessID) && ((int)j.ProcessType == 13 || (int)j.ProcessType == 14));
            db.JournalDetails.RemoveRange(db.JournalDetails.Where((JournalDetail jd) => journals.Select((Journal x) => x.ID).Contains(jd.JournalID)));
            db.Journals.RemoveRange(journals);
            db.DrawerPeriods.RemoveRange(entries);
            db.DrawerPeriodTransSummeryItems.RemoveRange(summaries);
            db.SaveChanges();
            frm_LogViewer.ViewLog("Journal de suppression des journaux de caisse", log);
        }

        public static bool CheckIfInvoiceCanBeDeleted(int id, out string _Message)
        {
            ERPDataContext db = new ERPDataContext();
            _Message = "Le journal numéro @ a été supprimé avec succès".Replace("@", id.ToString());
            return true;
        }

        public override void RefreshData()
        {
            BranchIDLookUpEdit.BindToDataSource(CurrentSession.AccessableBranches);
            ClosingDrwerIDLookUpEdit.BindToDataSource(CurrentSession.AccessableDrawers);
            DifferenceAccountIDLookUpEdit.BindToDataSource(CurrentSession.AccessableAccounts);
            ClosingPeriodUserIDLookUpEdit.BindToDataSource(db.Users.Select((User x) => new { x.ID, x.Name }).ToList());
            PeriodUserIDLookUpEdit.BindToDataSource(db.Users.Select((User x) => new { x.ID, x.Name }).ToList());
            base.RefreshData();
        }

        public override void Print()
        {
        }

        public void GoTo(int id)
        {
            if (!IsSafeToClearData())
            {
                return;
            }
            db = new ERPDataContext();
            DrawerPeriod loadedPeriod = db.DrawerPeriods.Include((DrawerPeriod x) => x.Summery).SingleOrDefault((DrawerPeriod x) => x.ID == id);
            if (loadedPeriod == null)
            {
                XtraMessageBox.Show("La période n'existe pas !", "", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                BeginInvoke(new MethodInvoker(base.Close));
                return;
            }
            period = loadedPeriod;
            if (!period.PeriodEnd.HasValue)
            {
                GetPeriodSummery();
            }
            gridControl1.DataSource = period.Summery;
        }

        internal void GetPeriodSummery()
        {
            period.Summery?.Clear();
            Drawer drawer = db.Drawers.SingleOrDefault((Drawer x) => x.ID == period.DrawerID);
            if (drawer == null)
            {
                return;
            }
            List<IGrouping<SystemProcess, JournalDetail>> accountJornalTrans = (from jd in db.JournalDetails.Include((JournalDetail x) => x.Journal)
                                                                                where jd.Journal.Date >= period.PeriodStart && jd.AccountID == drawer.AccountID
                                                                                group jd by jd.Journal.ProcessType into g
                                                                                select (g)).ToList();
            period.Summery.AddRange(accountJornalTrans.Select((IGrouping<SystemProcess, JournalDetail> g) => new DrawerPeriodTransSummeryItem
            {
                DrawerPeriod = period,
                ProcessCount = g.Count(),
                ProcessSum = ((IEnumerable<JournalDetail>)g).Sum((Func<JournalDetail, double?>)((JournalDetail x) => x.Debit * x.CurrencyRate)).GetValueOrDefault() - ((IEnumerable<JournalDetail>)g).Sum((Func<JournalDetail, double?>)((JournalDetail x) => x.Credit * x.CurrencyRate)).GetValueOrDefault(),
                ProcessType = g.Key
            }).ToList());
            IQueryable<JournalDetail> accountJournals = from jd in db.JournalDetails.Include((JournalDetail x) => x.Journal)
                                                        where jd.Journal.Date < period.PeriodStart && jd.AccountID == drawer.AccountID
                                                        select jd;
            double totalDebit = accountJournals.Sum((Expression<Func<JournalDetail, double?>>)((JournalDetail x) => x.Debit * x.CurrencyRate)).GetValueOrDefault();
            double totalCredit = accountJournals.Sum((Expression<Func<JournalDetail, double?>>)((JournalDetail x) => x.Credit * x.CurrencyRate)).GetValueOrDefault();
            double balance = totalDebit - totalCredit;
            period.OpeningBalance = balance;
            period.ClosingBalance = period.OpeningBalance + ((IEnumerable<DrawerPeriodTransSummeryItem>)period.Summery).Sum((Func<DrawerPeriodTransSummeryItem, double?>)((DrawerPeriodTransSummeryItem x) => x.ProcessSum)).GetValueOrDefault();
            period.ActualBalance = period.ClosingBalance;
            gridControl1.DataSource = period.Summery;
        }
        private static DrawerPeriodForm instance;

        protected ERPDataContext db = new ERPDataContext();
    }
}
