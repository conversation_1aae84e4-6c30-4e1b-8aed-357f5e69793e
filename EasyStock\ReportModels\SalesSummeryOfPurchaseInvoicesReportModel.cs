﻿using System;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    internal class SalesSummeryOfPurchaseInvoicesReportModel
    {
        [Display(Name = "Code Fournisseur")]
        public int VendorID { get; set; }

        [StringLength(400)]
        [Display(Name = "Nom du Fournisseur")]
        public string VendorName { get; set; }

        [Display(Name = "Date d'échéance")]
        public DateTime? DueDate { get; set; }

        [Display(Name = "Numéro", GroupName = "Détails de la facture")]
        public int ID { get; set; }

        [Display(Name = "Code", GroupName = "Détails de la facture")]
        public string Code { get; set; }

        [Display(Name = "Date", GroupName = "Détails de la facture")]
        public DateTime Date { get; set; }

        [Display(Name = "Code Article")]
        public int ProductID { get; set; }

        [Display(Name = "Nom de l'article")]
        public string ProductName { get; set; }

        [Display(Name = "Série")]
        public string Serial { get; set; }

        [Display(Name = "Couleur")]
        public string Color { get; set; }

        [Display(Name = "Taille")]
        public string Size { get; set; }

        [Display(Name = "Date d'expiration")]
        public DateTime? Expire { get; set; }

        [Display(Name = "Quantité")]
        [Range(0.001, double.MaxValue, ErrorMessage = "La quantité doit être supérieure à 0.001")]
        public double Quantity { get; set; }

        [Display(Name = "Prix")]
        public double Price { get; set; }

        [Display(Name = "Total")]
        public double Total
        {
            get
            {
                return this.Price * this.Quantity;
            }
        }

        [Display(Name = "Quantité totale vendue")]
        public double QuantitySold { get; set; }

        [Display(Name = "Valeur totale vendue")]
        public double TotalSold { get; set; }

        [Display(Name = "Quantité restante")]
        public double RemainingQty
        {
            get
            {
                return this.Quantity - this.QuantitySold;
            }
        }
    }
}
