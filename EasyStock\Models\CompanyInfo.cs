﻿using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;

namespace EasyStock.Models
{
    [DisplayName("Informations sur l'entreprise")]
    public class CompanyInfo
    {
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int ID { get; set; }

        [StringLength(250)]
        [Display(Name = "Nom de l'entreprise")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        public string Name { get; set; }

        [Column(TypeName = "image")]
        [Display(Name = "Logo de l'entreprise")]
        public byte[] CompanyLogo { get; set; }

        [NotMapped]
        public Image Logo
        {
            get
            {
                try
                {
                    byte[] imgbyte = CompanyLogo;
                    MemoryStream strm = new MemoryStream(imgbyte, writable: false);
                    return Image.FromStream(strm);
                }
                catch
                {
                    return null;
                }
            }
            set
            {
                MemoryStream strm = new MemoryStream();
                try
                {
                    value.Save(strm, ImageFormat.Png);
                    CompanyLogo = strm.ToArray();
                }
                catch
                {
                    CompanyLogo = strm.ToArray();
                }
            }
        }


        [Display(Name = "Adresse")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string Address { get; set; }

        [Display(Name = "Ville")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string City { get; set; }

        [Display(Name = "Téléphone")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string Phone { get; set; }

        [Display(Name = "Mobile")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string Mobile { get; set; }

        [Display(Name = "N° RC")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string CommercialBook { get; set; }

        [Display(Name = "N° I.F")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string CompanyTaxCard { get; set; }

        [Display(Name = "N° I.S")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string CompanyNIS { get; set; }

        [Display(Name = "R.I.B")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string CompanyRIB { get; set; }
        [Display(Name = "ART")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [StringLength(50)]
        public string CompanyART { get; set; }

        [Display(Name = "Début de l'exercice financier")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [Column(TypeName = "smalldatetime")]
        public DateTime? FinancialYearStart { get; set; }

        [Display(Name = "Fin de l'exercice financier")]
        [Required(ErrorMessage = "Le champ ne peut pas être vide")]
        [Column(TypeName = "smalldatetime")]
        public DateTime? FinancialYearEnd { get; set; }
    }
}
