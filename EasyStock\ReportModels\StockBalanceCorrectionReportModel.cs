﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace EasyStock.ReportModels
{
    public class StockBalanceCorrectionReportModel
    {
        [Display(Name = "Nom du dépôt")]
        public string StoreName { get; set; }

        [Display(Name = "Code du dépôt")]
        public int StoreID { get; set; }

        [Display(Name = "Numéro", GroupName = "Détails de la facture")]
        public int ID { get; set; }

        [Display(Name = "Code", GroupName = "Détails de la facture")]
        public string Code { get; set; }

        [Display(Name = "Date", GroupName = "Détails de la facture")]
        public DateTime Date { get; set; }

        [Display(Name = "Notes", GroupName = "Détails de la facture")]
        public string Notes { get; set; }

        [Display(Name = "Total des articles", GroupName = "Valeur")]
        public double Total { get; set; }

        [Display(Name = "Articles")]
        public ICollection<StockBalanceCorrectionDetailReportModel> Details { get; set; }

        [Display(Name = "Remise", GroupName = "Valeur")]
        public double Discount { get; set; }

        [Display(Name = "Pourcentage de remise")]
        public double DiscountPercentage
        {
            get
            {
                return this.Discount / this.Total;
            }
        }

        [Display(Name = "Net", GroupName = "Valeur")]
        public double Net { get; set; }

        [Display(Name = "Net en lettres")]
        public string NetText { get; set; }
    }
}
