﻿namespace EasyStock.ReportViews
{
	// Token: 0x0200032A RID: 810
	public partial class ProductInOutBalance : global::EasyStock.MainViews.XtraReportForm
	{
		// Token: 0x060013C9 RID: 5065 RVA: 0x0015EFC0 File Offset: 0x0015D1C0
		private void InitializeComponent()
		{
            this.SuspendLayout();
            // 
            // documentViewer1
            // 
            this.documentViewer1.Location = new System.Drawing.Point(0, 59);
            this.documentViewer1.Size = new System.Drawing.Size(1126, 593);
            // 
            // ProductInOutBalance
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.ClientSize = new System.Drawing.Size(1126, 676);
            this.Name = "ProductInOutBalance";
            this.Text = "Articles ( Entrées - Sorties ) ";
            this.ResumeLayout(false);
            this.PerformLayout();

		}
	}
}
