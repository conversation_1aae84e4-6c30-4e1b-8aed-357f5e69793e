﻿using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class TotalPurchaseInvoicesReportView : XtraReportForm
    {
        public TotalPurchaseInvoicesReportView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Vendor,
            ReportFilter.Store
        })
        {
            this.InitializeComponent();
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            bool flag = !this.ValidateFilters();
            if (!flag)
            {
                TotalPurchaseInvoicesReport rpt = new TotalPurchaseInvoicesReport();
                ICollection<TotalPurchaseInvoicesReportModel> dataSource = this.GetData();
                rpt.DataSource = dataSource;
                rpt.SetCompanyInfo(dataSource.FirstOrDefault<TotalPurchaseInvoicesReportModel>());
                rpt.Cell_ReportName.Text = this.Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                this.documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });





            }
        }

        private ICollection<TotalPurchaseInvoicesReportModel> GetData()
        {
            int[] StoresIDs = base.FiltersForm.Stores.EditValue?.ToArray();
            int[] VendorsIDs = base.FiltersForm.Vendors.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<PurchaseInvoice> filterdInvoices = context.PurchaseInvoices.Where((PurchaseInvoice inv) => DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate);
                if (VendorsIDs != null && VendorsIDs.Count() > 0)
                {
                    filterdInvoices = filterdInvoices.Where((PurchaseInvoice c) => VendorsIDs.Contains(c.VendorID));
                }
                if (StoresIDs != null && StoresIDs.Count() > 0)
                {
                    filterdInvoices = filterdInvoices.Where((PurchaseInvoice c) => StoresIDs.Contains(c.StoreID));
                }
                IQueryable<TotalPurchaseInvoicesReportModel> filterdInv = from inv in filterdInvoices
                                                                          join Bran in context.Stores on inv.StoreID equals Bran.ID
                                                                          join vend in context.Vendors on inv.VendorID equals vend.ID
                                                                          from vendCat in context.VendorsGroups.Where((VendorGroup vendorCat) => (int?)vendorCat.ID == vend.GroupID).DefaultIfEmpty()
                                                                          select new TotalPurchaseInvoicesReportModel
                                                                          {
                                                                              InvStore = Bran.Name,
                                                                              VendCat = vendCat.Name,
                                                                              Vendor = vend.Name,
                                                                              InvoiceID = inv.ID,
                                                                              InvoiceDate = inv.Date,
                                                                              PaidAmount = (context.PayDetails.Where((PayDetail p) => (int)p.SourceType == 4 && p.SourceID == inv.ID).Sum( x=> (double?)(x.Amount * x.CurrancyRate)?? 0.0)),
                                                                              TotalAmount = inv.Total,
                                                                              Expences = inv.OtherExpenses,
                                                                              Discount = inv.Discount,
                                                                              Taxes = inv.Tax,
                                                                              NetAmount = inv.Total + inv.OtherExpenses + inv.Tax - inv.Discount,
                                                                              DueDate = inv.DueDate
                                                                          };
                return filterdInv.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
    }
}
