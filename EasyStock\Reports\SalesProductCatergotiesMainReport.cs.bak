﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using DevExpress.DataAccess.ObjectBinding;
using DevExpress.Utils;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using EasyStock.ReportModels;

namespace EasyStock.Reports
{
	// Token: 0x02000373 RID: 883
	public class SalesProductCatergotiesMainReport : MasterReport
	{
		// Token: 0x06001503 RID: 5379 RVA: 0x0000ABA7 File Offset: 0x00008DA7
		public SalesProductCatergotiesMainReport()
		{
			this.InitializeComponent();
		}

		// Token: 0x06001504 RID: 5380 RVA: 0x0000ABCA File Offset: 0x00008DCA
		public void InitializeSubReport()
		{
			this.subReport = new SalesProductCategoriesSubReport();
		}

		// Token: 0x06001505 RID: 5381 RVA: 0x00191AC8 File Offset: 0x0018FCC8
		private void xrSubreport1_BeforePrint(object sender, PrintEventArgs e)
		{
			string value = base.GetCurrentColumnValue("Category").ToString();
			((SubreportBase)sender).ReportSource.DataSource = (from x in this.subReportDataSource
			where x.Category == value
			select x).ToList<SalesProductCategoriesSubModel>();
		}

		// Token: 0x06001506 RID: 5382 RVA: 0x00006A8E File Offset: 0x00004C8E
		private void tableCell13_BeforePrint(object sender, PrintEventArgs e)
		{
		}

		// Token: 0x06001507 RID: 5383 RVA: 0x00191B20 File Offset: 0x0018FD20
		protected override void Dispose(bool disposing)
		{
			bool flag = disposing && this.components != null;
			if (flag)
			{
				this.components.Dispose();
			}
			base.Dispose(disposing);
		}

		// Token: 0x06001508 RID: 5384 RVA: 0x00191B58 File Offset: 0x0018FD58
		private void InitializeComponent()
		{
			this.components = new Container();
			XRSummary xrSummary = new XRSummary();
			this.TopMargin = new TopMarginBand();
			this.BottomMargin = new BottomMarginBand();
			this.Detail = new DetailBand();
			this.table2 = new XRTable();
			this.tableRow2 = new XRTableRow();
			this.tableCell12 = new XRTableCell();
			this.tableCell15 = new XRTableCell();
			this.tableCell16 = new XRTableCell();
			this.tableCell17 = new XRTableCell();
			this.tableCell18 = new XRTableCell();
			this.tableCell19 = new XRTableCell();
			this.tableCell20 = new XRTableCell();
			this.tableCell21 = new XRTableCell();
			this.tableCell22 = new XRTableCell();
			this.table1 = new XRTable();
			this.tableRow1 = new XRTableRow();
			this.tableCell1 = new XRTableCell();
			this.tableCell4 = new XRTableCell();
			this.tableCell5 = new XRTableCell();
			this.tableCell6 = new XRTableCell();
			this.tableCell7 = new XRTableCell();
			this.tableCell8 = new XRTableCell();
			this.tableCell9 = new XRTableCell();
			this.tableCell10 = new XRTableCell();
			this.tableCell11 = new XRTableCell();
			this.ReportHeader = new ReportHeaderBand();
			this.xrPictureBox2 = new XRPictureBox();
			this.xrTable2 = new XRTable();
			this.xrTableRow5 = new XRTableRow();
			this.Cell_ReportName = new XRTableCell();
			this.xrTableRow6 = new XRTableRow();
			this.xrTableCell4 = new XRTableCell();
			this.xrTableRow7 = new XRTableRow();
			this.Cell_Filters = new XRTableCell();
			this.xrTable1 = new XRTable();
			this.xrTableRow2 = new XRTableRow();
			this.xrTableCell13 = new XRTableCell();
			this.xrTableRow3 = new XRTableRow();
			this.xrTableCell2 = new XRTableCell();
			this.xrTableRow4 = new XRTableRow();
			this.xrTableCell3 = new XRTableCell();
			this.xrTableRow1 = new XRTableRow();
			this.xrTableCell1 = new XRTableCell();
			this.xrTableRow8 = new XRTableRow();
			this.xrTableCell6 = new XRTableCell();
			this.GroupHeader1 = new GroupHeaderBand();
			this.DetailReport = new DetailReportBand();
			this.Detail1 = new DetailBand();
			this.xrSubreport1 = new XRSubreport();
			this.GroupFooter1 = new GroupFooterBand();
			this.xrTable5 = new XRTable();
			this.xrTableRow11 = new XRTableRow();
			this.xrTableCell14 = new XRTableCell();
			this.xrTableCell28 = new XRTableCell();
			this.xrTableCell10 = new XRTableCell();
			this.xrTableCell7 = new XRTableCell();
			this.xrTableCell5 = new XRTableCell();
			this.xrTableCell29 = new XRTableCell();
			this.xrTableCell8 = new XRTableCell();
			this.xrTableCell12 = new XRTableCell();
			this.objectDataSource1 = new ObjectDataSource(this.components);
			this.xrLabel1 = new XRLabel();
			this.xrLabel2 = new XRLabel();
			this.xrLine1 = new XRLine();
			this.xrTableCell9 = new XRTableCell();
			this.TotalPer = new CalculatedField();
			((ISupportInitialize)this.table2).BeginInit();
			((ISupportInitialize)this.table1).BeginInit();
			((ISupportInitialize)this.xrTable2).BeginInit();
			((ISupportInitialize)this.xrTable1).BeginInit();
			((ISupportInitialize)this.xrTable5).BeginInit();
			((ISupportInitialize)this.objectDataSource1).BeginInit();
			((ISupportInitialize)this).BeginInit();
			this.TopMargin.HeightF = 34f;
			this.TopMargin.Name = "TopMargin";
			this.BottomMargin.HeightF = 25f;
			this.BottomMargin.Name = "BottomMargin";
			this.Detail.Controls.AddRange(new XRControl[]
			{
				this.xrLine1,
				this.xrLabel2,
				this.xrLabel1,
				this.xrSubreport1,
				this.table2,
				this.table1
			});
			this.Detail.HeightF = 148.2916f;
			this.Detail.Name = "Detail";
			this.table2.LocationFloat = new PointFloat(4.768372E-05f, 101.8334f);
			this.table2.Name = "table2";
			this.table2.Rows.AddRange(new XRTableRow[]
			{
				this.tableRow2
			});
			this.table2.SizeF = new SizeF(1041f, 25f);
			this.tableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.tableCell12,
				this.tableCell15,
				this.tableCell16,
				this.tableCell17,
				this.tableCell18,
				this.tableCell19,
				this.tableCell20,
				this.tableCell21,
				this.tableCell22
			});
			this.tableRow2.Name = "tableRow2";
			this.tableRow2.Weight = 11.5;
			this.tableCell12.Borders = BorderSide.None;
			this.tableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "sumRecordNumber([Category])")
			});
			this.tableCell12.Name = "tableCell12";
			this.tableCell12.StylePriority.UseBorders = false;
			this.tableCell12.StylePriority.UseTextAlignment = false;
			xrSummary.Running = SummaryRunning.Report;
			this.tableCell12.Summary = xrSummary;
			this.tableCell12.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell12.Weight = 0.025397518400477385;
			this.tableCell15.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalCost]")
			});
			this.tableCell15.Name = "tableCell15";
			this.tableCell15.StylePriority.UseTextAlignment = false;
			this.tableCell15.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell15.TextFormatString = "{0:#.00}";
			this.tableCell15.Weight = 0.10017136363614972;
			this.tableCell16.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalSales]")
			});
			this.tableCell16.Name = "tableCell16";
			this.tableCell16.StylePriority.UseTextAlignment = false;
			this.tableCell16.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell16.TextFormatString = "{0:#.00}";
			this.tableCell16.Weight = 0.10402580062278806;
			this.tableCell17.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Discount]")
			});
			this.tableCell17.Name = "tableCell17";
			this.tableCell17.StylePriority.UseTextAlignment = false;
			this.tableCell17.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell17.TextFormatString = "{0:#.00}";
			this.tableCell17.Weight = 0.09358735932553101;
			this.tableCell18.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalAfterDiscount]")
			});
			this.tableCell18.Name = "tableCell18";
			this.tableCell18.StylePriority.UseTextAlignment = false;
			this.tableCell18.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell18.TextFormatString = "{0:#.00}";
			this.tableCell18.Weight = 0.10053184768230827;
			this.tableCell19.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Taxes]")
			});
			this.tableCell19.Name = "tableCell19";
			this.tableCell19.StylePriority.UseTextAlignment = false;
			this.tableCell19.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell19.TextFormatString = "{0:#.00}";
			this.tableCell19.Weight = 0.08996831203354182;
			this.tableCell20.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[NetAmount]")
			});
			this.tableCell20.Name = "tableCell20";
			this.tableCell20.StylePriority.UseTextAlignment = false;
			this.tableCell20.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell20.TextFormatString = "{0:#.00}";
			this.tableCell20.Weight = 0.09813633696364668;
			this.tableCell21.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ProfitMargin]")
			});
			this.tableCell21.Name = "tableCell21";
			this.tableCell21.StylePriority.UseTextAlignment = false;
			this.tableCell21.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell21.TextFormatString = "{0:#.00}";
			this.tableCell21.Weight = 0.06554830545175282;
			this.tableCell22.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[ProfitMarginPer]")
			});
			this.tableCell22.Name = "tableCell22";
			this.tableCell22.StylePriority.UseTextAlignment = false;
			this.tableCell22.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell22.TextFormatString = "{0:0.00%}";
			this.tableCell22.Weight = 0.05744981785450172;
			this.table1.LocationFloat = new PointFloat(0f, 75.50005f);
			this.table1.Name = "table1";
			this.table1.Rows.AddRange(new XRTableRow[]
			{
				this.tableRow1
			});
			this.table1.SizeF = new SizeF(1041f, 26.33333f);
			this.tableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.tableCell1,
				this.tableCell4,
				this.tableCell5,
				this.tableCell6,
				this.tableCell7,
				this.tableCell8,
				this.tableCell9,
				this.tableCell10,
				this.tableCell11
			});
			this.tableRow1.Name = "tableRow1";
			this.tableRow1.Weight = 1.0;
			this.tableCell1.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell1.BorderColor = Color.White;
			this.tableCell1.Borders = BorderSide.Left;
			this.tableCell1.BorderWidth = 1f;
			this.tableCell1.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell1.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell1.Name = "tableCell1";
			this.tableCell1.StylePriority.UseBackColor = false;
			this.tableCell1.StylePriority.UseBorderColor = false;
			this.tableCell1.StylePriority.UseBorders = false;
			this.tableCell1.StylePriority.UseBorderWidth = false;
			this.tableCell1.StylePriority.UseFont = false;
			this.tableCell1.StylePriority.UseForeColor = false;
			this.tableCell1.StylePriority.UseTextAlignment = false;
			this.tableCell1.Text = "م";
			this.tableCell1.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell1.Weight = 0.025397570237810932;
			this.tableCell4.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell4.BorderColor = Color.White;
			this.tableCell4.Borders = BorderSide.Left;
			this.tableCell4.BorderWidth = 1f;
			this.tableCell4.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell4.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell4.Name = "tableCell4";
			this.tableCell4.StylePriority.UseBackColor = false;
			this.tableCell4.StylePriority.UseBorderColor = false;
			this.tableCell4.StylePriority.UseBorders = false;
			this.tableCell4.StylePriority.UseBorderWidth = false;
			this.tableCell4.StylePriority.UseFont = false;
			this.tableCell4.StylePriority.UseForeColor = false;
			this.tableCell4.StylePriority.UseTextAlignment = false;
			this.tableCell4.Text = "اجمالى التكلفة";
			this.tableCell4.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell4.Weight = 0.10017125461073129;
			this.tableCell5.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell5.BorderColor = Color.White;
			this.tableCell5.Borders = BorderSide.Left;
			this.tableCell5.BorderWidth = 1f;
			this.tableCell5.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell5.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell5.Name = "tableCell5";
			this.tableCell5.StylePriority.UseBackColor = false;
			this.tableCell5.StylePriority.UseBorderColor = false;
			this.tableCell5.StylePriority.UseBorders = false;
			this.tableCell5.StylePriority.UseBorderWidth = false;
			this.tableCell5.StylePriority.UseFont = false;
			this.tableCell5.StylePriority.UseForeColor = false;
			this.tableCell5.StylePriority.UseTextAlignment = false;
			this.tableCell5.Text = "اجمالى سعر البيع";
			this.tableCell5.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell5.Weight = 0.10402576434331595;
			this.tableCell6.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell6.BorderColor = Color.White;
			this.tableCell6.Borders = BorderSide.Left;
			this.tableCell6.BorderWidth = 1f;
			this.tableCell6.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell6.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell6.Name = "tableCell6";
			this.tableCell6.StylePriority.UseBackColor = false;
			this.tableCell6.StylePriority.UseBorderColor = false;
			this.tableCell6.StylePriority.UseBorders = false;
			this.tableCell6.StylePriority.UseBorderWidth = false;
			this.tableCell6.StylePriority.UseFont = false;
			this.tableCell6.StylePriority.UseForeColor = false;
			this.tableCell6.StylePriority.UseTextAlignment = false;
			this.tableCell6.Text = "الخصم النقدى";
			this.tableCell6.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell6.Weight = 0.09358736780263585;
			this.tableCell7.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell7.BorderColor = Color.White;
			this.tableCell7.Borders = BorderSide.Left;
			this.tableCell7.BorderWidth = 1f;
			this.tableCell7.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell7.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell7.Name = "tableCell7";
			this.tableCell7.StylePriority.UseBackColor = false;
			this.tableCell7.StylePriority.UseBorderColor = false;
			this.tableCell7.StylePriority.UseBorders = false;
			this.tableCell7.StylePriority.UseBorderWidth = false;
			this.tableCell7.StylePriority.UseFont = false;
			this.tableCell7.StylePriority.UseForeColor = false;
			this.tableCell7.StylePriority.UseTextAlignment = false;
			this.tableCell7.Text = "اجمالى بعد الخصم";
			this.tableCell7.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell7.Weight = 0.10053181860695697;
			this.tableCell8.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell8.BorderColor = Color.White;
			this.tableCell8.Borders = BorderSide.Left;
			this.tableCell8.BorderWidth = 1f;
			this.tableCell8.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell8.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell8.Name = "tableCell8";
			this.tableCell8.StylePriority.UseBackColor = false;
			this.tableCell8.StylePriority.UseBorderColor = false;
			this.tableCell8.StylePriority.UseBorders = false;
			this.tableCell8.StylePriority.UseBorderWidth = false;
			this.tableCell8.StylePriority.UseFont = false;
			this.tableCell8.StylePriority.UseForeColor = false;
			this.tableCell8.StylePriority.UseTextAlignment = false;
			this.tableCell8.Text = "الضريبة المضافة";
			this.tableCell8.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell8.Weight = 0.08996837919493586;
			this.tableCell9.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell9.BorderColor = Color.White;
			this.tableCell9.Borders = BorderSide.Left;
			this.tableCell9.BorderWidth = 1f;
			this.tableCell9.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell9.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell9.Name = "tableCell9";
			this.tableCell9.StylePriority.UseBackColor = false;
			this.tableCell9.StylePriority.UseBorderColor = false;
			this.tableCell9.StylePriority.UseBorders = false;
			this.tableCell9.StylePriority.UseBorderWidth = false;
			this.tableCell9.StylePriority.UseFont = false;
			this.tableCell9.StylePriority.UseForeColor = false;
			this.tableCell9.StylePriority.UseTextAlignment = false;
			this.tableCell9.Text = "الاجمالي بعد الضريبة";
			this.tableCell9.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell9.Weight = 0.09813641111892123;
			this.tableCell10.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell10.BorderColor = Color.White;
			this.tableCell10.Borders = BorderSide.Left;
			this.tableCell10.BorderWidth = 1f;
			this.tableCell10.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell10.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell10.Name = "tableCell10";
			this.tableCell10.StylePriority.UseBackColor = false;
			this.tableCell10.StylePriority.UseBorderColor = false;
			this.tableCell10.StylePriority.UseBorders = false;
			this.tableCell10.StylePriority.UseBorderWidth = false;
			this.tableCell10.StylePriority.UseFont = false;
			this.tableCell10.StylePriority.UseForeColor = false;
			this.tableCell10.StylePriority.UseTextAlignment = false;
			this.tableCell10.Text = "هامش الربح";
			this.tableCell10.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell10.Weight = 0.06554824656872292;
			this.tableCell11.BackColor = Color.FromArgb(244, 242, 237);
			this.tableCell11.BorderColor = Color.White;
			this.tableCell11.Borders = BorderSide.Left;
			this.tableCell11.BorderWidth = 1f;
			this.tableCell11.Font = new Font("Segoe UI Semibold", 9.75f, FontStyle.Bold);
			this.tableCell11.ForeColor = Color.FromArgb(97, 111, 126);
			this.tableCell11.Name = "tableCell11";
			this.tableCell11.StylePriority.UseBackColor = false;
			this.tableCell11.StylePriority.UseBorderColor = false;
			this.tableCell11.StylePriority.UseBorders = false;
			this.tableCell11.StylePriority.UseBorderWidth = false;
			this.tableCell11.StylePriority.UseFont = false;
			this.tableCell11.StylePriority.UseForeColor = false;
			this.tableCell11.StylePriority.UseTextAlignment = false;
			this.tableCell11.Text = "نسبة الربح";
			this.tableCell11.TextAlignment = TextAlignment.MiddleCenter;
			this.tableCell11.Weight = 0.05744978201233755;
			this.ReportHeader.Controls.AddRange(new XRControl[]
			{
				this.xrPictureBox2,
				this.xrTable2,
				this.xrTable1
			});
			this.ReportHeader.HeightF = 210.7917f;
			this.ReportHeader.Name = "ReportHeader";
			this.xrPictureBox2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "ImageSource", "[CompanyLogo]")
			});
			this.xrPictureBox2.LocationFloat = new PointFloat(144.9586f, 10.00001f);
			this.xrPictureBox2.Name = "xrPictureBox2";
			this.xrPictureBox2.SizeF = new SizeF(100f, 100f);
			this.xrPictureBox2.Sizing = ImageSizeMode.Squeeze;
			this.xrTable2.AnchorHorizontal = HorizontalAnchorStyles.Both;
			this.xrTable2.AnchorVertical = VerticalAnchorStyles.Both;
			this.xrTable2.LocationFloat = new PointFloat(0f, 123.4375f);
			this.xrTable2.Name = "xrTable2";
			this.xrTable2.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable2.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow5,
				this.xrTableRow6,
				this.xrTableRow7
			});
			this.xrTable2.SizeF = new SizeF(1041f, 85.01999f);
			this.xrTable2.StylePriority.UseTextAlignment = false;
			this.xrTable2.TextAlignment = TextAlignment.TopCenter;
			this.xrTableRow5.Cells.AddRange(new XRTableCell[]
			{
				this.Cell_ReportName
			});
			this.xrTableRow5.Name = "xrTableRow5";
			this.xrTableRow5.Weight = 1.0;
			this.Cell_ReportName.BackColor = Color.WhiteSmoke;
			this.Cell_ReportName.Borders = BorderSide.All;
			this.Cell_ReportName.BorderWidth = 2f;
			this.Cell_ReportName.CanGrow = false;
			this.Cell_ReportName.Font = new Font("Traditional Arabic", 21.75f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.Cell_ReportName.Multiline = true;
			this.Cell_ReportName.Name = "Cell_ReportName";
			this.Cell_ReportName.Padding = new PaddingInfo(5, 5, 5, 5, 100f);
			this.Cell_ReportName.RowSpan = 2;
			this.Cell_ReportName.StylePriority.UseBackColor = false;
			this.Cell_ReportName.StylePriority.UseBorders = false;
			this.Cell_ReportName.StylePriority.UseBorderWidth = false;
			this.Cell_ReportName.StylePriority.UseFont = false;
			this.Cell_ReportName.StylePriority.UsePadding = false;
			this.Cell_ReportName.StylePriority.UseTextAlignment = false;
			this.Cell_ReportName.Text = "اسم التقرير";
			this.Cell_ReportName.TextAlignment = TextAlignment.MiddleCenter;
			this.Cell_ReportName.Weight = 3.0;
			this.xrTableRow6.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell4
			});
			this.xrTableRow6.Name = "xrTableRow6";
			this.xrTableRow6.Weight = 1.0;
			this.xrTableCell4.BackColor = Color.WhiteSmoke;
			this.xrTableCell4.Borders = BorderSide.All;
			this.xrTableCell4.BorderWidth = 2f;
			this.xrTableCell4.CanGrow = false;
			this.xrTableCell4.Font = new Font("Arial", 12.64f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTableCell4.Multiline = true;
			this.xrTableCell4.Name = "xrTableCell4";
			this.xrTableCell4.Padding = new PaddingInfo(5, 5, 5, 5, 100f);
			this.xrTableCell4.StylePriority.UseBackColor = false;
			this.xrTableCell4.StylePriority.UseBorders = false;
			this.xrTableCell4.StylePriority.UseBorderWidth = false;
			this.xrTableCell4.StylePriority.UseFont = false;
			this.xrTableCell4.StylePriority.UsePadding = false;
			this.xrTableCell4.StylePriority.UseTextAlignment = false;
			this.xrTableCell4.Text = "xrTableCell4";
			this.xrTableCell4.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell4.Weight = 3.0;
			this.xrTableRow7.Cells.AddRange(new XRTableCell[]
			{
				this.Cell_Filters
			});
			this.xrTableRow7.Name = "xrTableRow7";
			this.xrTableRow7.Weight = 1.0;
			this.Cell_Filters.CanGrow = false;
			this.Cell_Filters.Multiline = true;
			this.Cell_Filters.Name = "Cell_Filters";
			this.Cell_Filters.StylePriority.UseTextAlignment = false;
			this.Cell_Filters.TextAlignment = TextAlignment.MiddleCenter;
			this.Cell_Filters.Weight = 3.0;
			this.xrTable1.LocationFloat = new PointFloat(698.801f, 0f);
			this.xrTable1.Name = "xrTable1";
			this.xrTable1.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable1.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow2,
				this.xrTableRow3,
				this.xrTableRow4,
				this.xrTableRow1,
				this.xrTableRow8
			});
			this.xrTable1.SizeF = new SizeF(289.5833f, 123.4375f);
			this.xrTable1.StylePriority.UseTextAlignment = false;
			this.xrTable1.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableRow2.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell13
			});
			this.xrTableRow2.Name = "xrTableRow2";
			this.xrTableRow2.Weight = 17.**************;
			this.xrTableCell13.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyName]")
			});
			this.xrTableCell13.Font = new Font("Arial", 12f, FontStyle.Bold);
			this.xrTableCell13.Multiline = true;
			this.xrTableCell13.Name = "xrTableCell13";
			this.xrTableCell13.StylePriority.UseFont = false;
			this.xrTableCell13.StylePriority.UseTextAlignment = false;
			this.xrTableCell13.Text = "xrTableCell13";
			this.xrTableCell13.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell13.Weight = 1.8461538461538458;
			this.xrTableRow3.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell2
			});
			this.xrTableRow3.Name = "xrTableRow3";
			this.xrTableRow3.Weight = 17.**************;
			this.xrTableCell2.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CommercialBook]")
			});
			this.xrTableCell2.Multiline = true;
			this.xrTableCell2.Name = "xrTableCell2";
			this.xrTableCell2.Text = "xrTableCell2";
			this.xrTableCell2.Weight = 1.8461538461538458;
			this.xrTableRow4.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell3
			});
			this.xrTableRow4.Name = "xrTableRow4";
			this.xrTableRow4.Weight = 17.**************;
			this.xrTableCell3.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyTaxCard]")
			});
			this.xrTableCell3.Multiline = true;
			this.xrTableCell3.Name = "xrTableCell3";
			this.xrTableCell3.Text = "xrTableCell3";
			this.xrTableCell3.Weight = 1.8461538461538458;
			this.xrTableRow1.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell1
			});
			this.xrTableRow1.Name = "xrTableRow1";
			this.xrTableRow1.Weight = 17.**************;
			this.xrTableCell1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyAddress]")
			});
			this.xrTableCell1.Multiline = true;
			this.xrTableCell1.Name = "xrTableCell1";
			this.xrTableCell1.Text = "xrTableCell1";
			this.xrTableCell1.Weight = 1.8461538461538458;
			this.xrTableRow8.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell6
			});
			this.xrTableRow8.Name = "xrTableRow8";
			this.xrTableRow8.Weight = 17.**************;
			this.xrTableCell6.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[CompanyPhone]")
			});
			this.xrTableCell6.Multiline = true;
			this.xrTableCell6.Name = "xrTableCell6";
			this.xrTableCell6.Text = "xrTableCell6";
			this.xrTableCell6.Weight = 1.8461538461538458;
			this.GroupHeader1.HeightF = 0f;
			this.GroupHeader1.Name = "GroupHeader1";
			this.DetailReport.Bands.AddRange(new Band[]
			{
				this.Detail1
			});
			this.DetailReport.Level = 0;
			this.DetailReport.Name = "DetailReport";
			this.Detail1.HeightF = 0f;
			this.Detail1.Name = "Detail1";
			this.xrSubreport1.LocationFloat = new PointFloat(0f, 38.95833f);
			this.xrSubreport1.Name = "xrSubreport1";
			this.xrSubreport1.ParameterBindings.Add(new ParameterBinding("Cat", null, "Category"));
			this.xrSubreport1.ReportSource = new SalesProductCategoriesSubReport();
			this.xrSubreport1.SizeF = new SizeF(1041f, 36.54172f);
			this.xrSubreport1.BeforePrint += this.xrSubreport1_BeforePrint;
			this.GroupFooter1.Controls.AddRange(new XRControl[]
			{
				this.xrTable5
			});
			this.GroupFooter1.HeightF = 41.66667f;
			this.GroupFooter1.Name = "GroupFooter1";
			this.xrTable5.BackColor = Color.NavajoWhite;
			this.xrTable5.BorderColor = Color.White;
			this.xrTable5.Borders = BorderSide.All;
			this.xrTable5.Font = new Font("Arial", 11.25f, FontStyle.Bold, GraphicsUnit.Point, 0);
			this.xrTable5.ForeColor = Color.Black;
			this.xrTable5.LocationFloat = new PointFloat(0f, 0f);
			this.xrTable5.Name = "xrTable5";
			this.xrTable5.Padding = new PaddingInfo(2, 2, 0, 0, 100f);
			this.xrTable5.Rows.AddRange(new XRTableRow[]
			{
				this.xrTableRow11
			});
			this.xrTable5.SizeF = new SizeF(1041f, 40.82f);
			this.xrTable5.StylePriority.UseBackColor = false;
			this.xrTable5.StylePriority.UseBorderColor = false;
			this.xrTable5.StylePriority.UseBorders = false;
			this.xrTable5.StylePriority.UseFont = false;
			this.xrTable5.StylePriority.UseForeColor = false;
			this.xrTableRow11.Cells.AddRange(new XRTableCell[]
			{
				this.xrTableCell14,
				this.xrTableCell28,
				this.xrTableCell10,
				this.xrTableCell7,
				this.xrTableCell5,
				this.xrTableCell29,
				this.xrTableCell8,
				this.xrTableCell12,
				this.xrTableCell9
			});
			this.xrTableRow11.Name = "xrTableRow11";
			this.xrTableRow11.Weight = 0.5609756097560976;
			this.xrTableCell14.BorderColor = Color.White;
			this.xrTableCell14.Borders = BorderSide.Right;
			this.xrTableCell14.BorderWidth = 2f;
			this.xrTableCell14.Multiline = true;
			this.xrTableCell14.Name = "xrTableCell14";
			this.xrTableCell14.StylePriority.UseBorderColor = false;
			this.xrTableCell14.StylePriority.UseBorders = false;
			this.xrTableCell14.StylePriority.UseBorderWidth = false;
			this.xrTableCell14.StylePriority.UseTextAlignment = false;
			this.xrTableCell14.Text = "المجموع : ";
			this.xrTableCell14.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell14.Weight = 0.04181054250177997;
			this.xrTableCell28.BorderColor = Color.White;
			this.xrTableCell28.Borders = BorderSide.Right;
			this.xrTableCell28.BorderWidth = 2f;
			this.xrTableCell28.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([TotalCost])")
			});
			this.xrTableCell28.Multiline = true;
			this.xrTableCell28.Name = "xrTableCell28";
			this.xrTableCell28.StylePriority.UseBorderColor = false;
			this.xrTableCell28.StylePriority.UseBorders = false;
			this.xrTableCell28.StylePriority.UseBorderWidth = false;
			this.xrTableCell28.StylePriority.UseTextAlignment = false;
			this.xrTableCell28.Text = "قيمه الخصم";
			this.xrTableCell28.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell28.TextFormatString = "{0:#.00}";
			this.xrTableCell28.Weight = 0.07056098928617469;
			this.xrTableCell10.BorderColor = Color.White;
			this.xrTableCell10.Borders = BorderSide.Right;
			this.xrTableCell10.BorderWidth = 2f;
			this.xrTableCell10.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([TotalSales])")
			});
			this.xrTableCell10.Multiline = true;
			this.xrTableCell10.Name = "xrTableCell10";
			this.xrTableCell10.StylePriority.UseBorderColor = false;
			this.xrTableCell10.StylePriority.UseBorders = false;
			this.xrTableCell10.StylePriority.UseBorderWidth = false;
			this.xrTableCell10.StylePriority.UseTextAlignment = false;
			this.xrTableCell10.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell10.TextFormatString = "{0:#.00}";
			this.xrTableCell10.Weight = 0.09309262422306548;
			this.xrTableCell7.BorderColor = Color.White;
			this.xrTableCell7.Borders = BorderSide.Right;
			this.xrTableCell7.BorderWidth = 2f;
			this.xrTableCell7.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([Discount])")
			});
			this.xrTableCell7.Multiline = true;
			this.xrTableCell7.Name = "xrTableCell7";
			this.xrTableCell7.StylePriority.UseBorderColor = false;
			this.xrTableCell7.StylePriority.UseBorders = false;
			this.xrTableCell7.StylePriority.UseBorderWidth = false;
			this.xrTableCell7.StylePriority.UseTextAlignment = false;
			this.xrTableCell7.Text = "xrTableCell7";
			this.xrTableCell7.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell7.TextFormatString = "{0:#.00}";
			this.xrTableCell7.Weight = 0.0837513607811176;
			this.xrTableCell5.BorderColor = Color.White;
			this.xrTableCell5.Borders = BorderSide.Right;
			this.xrTableCell5.BorderWidth = 2f;
			this.xrTableCell5.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([TotalAfterDiscount])")
			});
			this.xrTableCell5.Multiline = true;
			this.xrTableCell5.Name = "xrTableCell5";
			this.xrTableCell5.StylePriority.UseBorderColor = false;
			this.xrTableCell5.StylePriority.UseBorders = false;
			this.xrTableCell5.StylePriority.UseBorderWidth = false;
			this.xrTableCell5.StylePriority.UseTextAlignment = false;
			this.xrTableCell5.Text = "xrTableCell5";
			this.xrTableCell5.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell5.TextFormatString = "{0:#.00}";
			this.xrTableCell5.Weight = 0.08996591295897508;
			this.xrTableCell29.BorderColor = Color.White;
			this.xrTableCell29.Borders = BorderSide.Right;
			this.xrTableCell29.BorderWidth = 2f;
			this.xrTableCell29.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([TotalSales])")
			});
			this.xrTableCell29.Multiline = true;
			this.xrTableCell29.Name = "xrTableCell29";
			this.xrTableCell29.StylePriority.UseBorderColor = false;
			this.xrTableCell29.StylePriority.UseBorders = false;
			this.xrTableCell29.StylePriority.UseBorderWidth = false;
			this.xrTableCell29.StylePriority.UseTextAlignment = false;
			this.xrTableCell29.Text = "قيمه الضريبه";
			this.xrTableCell29.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell29.TextFormatString = "{0:#.00}";
			this.xrTableCell29.Weight = 0.08051257530613165;
			this.xrTableCell8.BorderColor = Color.White;
			this.xrTableCell8.Borders = BorderSide.Right;
			this.xrTableCell8.BorderWidth = 2f;
			this.xrTableCell8.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([NetAmount])")
			});
			this.xrTableCell8.Multiline = true;
			this.xrTableCell8.Name = "xrTableCell8";
			this.xrTableCell8.StylePriority.UseBorderColor = false;
			this.xrTableCell8.StylePriority.UseBorders = false;
			this.xrTableCell8.StylePriority.UseBorderWidth = false;
			this.xrTableCell8.StylePriority.UseTextAlignment = false;
			this.xrTableCell8.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell8.TextFormatString = "{0:#.00}";
			this.xrTableCell8.Weight = 0.08782228987926216;
			this.xrTableCell12.BorderColor = Color.White;
			this.xrTableCell12.Borders = BorderSide.Right;
			this.xrTableCell12.BorderWidth = 2f;
			this.xrTableCell12.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "Sum([ProfitMargin])")
			});
			this.xrTableCell12.Multiline = true;
			this.xrTableCell12.Name = "xrTableCell12";
			this.xrTableCell12.StylePriority.UseBorderColor = false;
			this.xrTableCell12.StylePriority.UseBorders = false;
			this.xrTableCell12.StylePriority.UseBorderWidth = false;
			this.xrTableCell12.StylePriority.UseTextAlignment = false;
			this.xrTableCell12.Text = "ألصافي";
			this.xrTableCell12.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell12.TextFormatString = "{0:#.00}";
			this.xrTableCell12.Weight = 0.05503544519797317;
			this.objectDataSource1.DataSource = typeof(SalesProductCategoriesMainModel);
			this.objectDataSource1.Name = "objectDataSource1";
			this.xrLabel1.BackColor = Color.FromArgb(218, 218, 218);
			this.xrLabel1.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[Category]")
			});
			this.xrLabel1.Font = new Font("Segoe UI Semibold", 18.75f, FontStyle.Bold);
			this.xrLabel1.ForeColor = Color.FromArgb(243, 120, 0);
			this.xrLabel1.LocationFloat = new PointFloat(232.5755f, 0f);
			this.xrLabel1.Multiline = true;
			this.xrLabel1.Name = "xrLabel1";
			this.xrLabel1.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrLabel1.SizeF = new SizeF(808.4244f, 38.95833f);
			this.xrLabel1.StylePriority.UseBackColor = false;
			this.xrLabel1.StylePriority.UseFont = false;
			this.xrLabel1.StylePriority.UseForeColor = false;
			this.xrLabel1.Text = "xrLabel1";
			this.xrLabel2.BackColor = Color.FromArgb(221, 128, 71);
			this.xrLabel2.Font = new Font("Segoe UI", 15f, FontStyle.Bold);
			this.xrLabel2.ForeColor = Color.White;
			this.xrLabel2.LocationFloat = new PointFloat(0f, 0f);
			this.xrLabel2.Multiline = true;
			this.xrLabel2.Name = "xrLabel2";
			this.xrLabel2.Padding = new PaddingInfo(2, 2, 0, 0, 96f);
			this.xrLabel2.SizeF = new SizeF(232.5756f, 38.95833f);
			this.xrLabel2.StylePriority.UseBackColor = false;
			this.xrLabel2.StylePriority.UseFont = false;
			this.xrLabel2.StylePriority.UseForeColor = false;
			this.xrLabel2.StylePriority.UseTextAlignment = false;
			this.xrLabel2.Text = "مبيعات المجموعه";
			this.xrLabel2.TextAlignment = TextAlignment.MiddleLeft;
			this.xrLine1.LocationFloat = new PointFloat(3.91671f, 141.4167f);
			this.xrLine1.Name = "xrLine1";
			this.xrLine1.SizeF = new SizeF(1027.083f, 2.083344f);
			this.xrTableCell9.BorderColor = Color.White;
			this.xrTableCell9.Borders = BorderSide.Right;
			this.xrTableCell9.BorderWidth = 2f;
			this.xrTableCell9.ExpressionBindings.AddRange(new ExpressionBinding[]
			{
				new ExpressionBinding("BeforePrint", "Text", "[TotalPer]")
			});
			this.xrTableCell9.Multiline = true;
			this.xrTableCell9.Name = "xrTableCell9";
			this.xrTableCell9.StylePriority.UseBorderColor = false;
			this.xrTableCell9.StylePriority.UseBorders = false;
			this.xrTableCell9.StylePriority.UseBorderWidth = false;
			this.xrTableCell9.StylePriority.UseTextAlignment = false;
			this.xrTableCell9.Text = "xrTableCell9";
			this.xrTableCell9.TextAlignment = TextAlignment.MiddleCenter;
			this.xrTableCell9.TextFormatString = "{0:0.00%}";
			this.xrTableCell9.Weight = 0.05503544519797317;
			this.TotalPer.Expression = "Sum([ProfitMargin])/Sum([TotalCost])";
			this.TotalPer.Name = "TotalPer";
			base.Bands.AddRange(new Band[]
			{
				this.TopMargin,
				this.BottomMargin,
				this.Detail,
				this.ReportHeader,
				this.GroupHeader1,
				this.DetailReport,
				this.GroupFooter1
			});
			base.CalculatedFields.AddRange(new CalculatedField[]
			{
				this.TotalPer
			});
			base.ComponentStorage.AddRange(new IComponent[]
			{
				this.objectDataSource1
			});
			base.DataSource = this.objectDataSource1;
			this.Font = new Font("Arial", 9.75f);
			base.Landscape = true;
			base.Margins = new Margins(34, 25, 34, 25);
			base.PageHeight = 850;
			base.PageWidth = 1100;
			this.RightToLeft = RightToLeft.Yes;
			base.RightToLeftLayout = RightToLeftLayout.Yes;
			base.Version = "20.2";
			((ISupportInitialize)this.table2).EndInit();
			((ISupportInitialize)this.table1).EndInit();
			((ISupportInitialize)this.xrTable2).EndInit();
			((ISupportInitialize)this.xrTable1).EndInit();
			((ISupportInitialize)this.xrTable5).EndInit();
			((ISupportInitialize)this.objectDataSource1).EndInit();
			((ISupportInitialize)this).EndInit();
		}

		// Token: 0x04001CAF RID: 7343
		private SalesProductCategoriesSubReport subReport;

		// Token: 0x04001CB0 RID: 7344
		public List<SalesProductCategoriesSubModel> subReportDataSource;

		// Token: 0x04001CB1 RID: 7345
		private string Cat = "";

		// Token: 0x04001CB2 RID: 7346
		private IContainer components = null;

		// Token: 0x04001CB3 RID: 7347
		private TopMarginBand TopMargin;

		// Token: 0x04001CB4 RID: 7348
		private BottomMarginBand BottomMargin;

		// Token: 0x04001CB5 RID: 7349
		private DetailBand Detail;

		// Token: 0x04001CB6 RID: 7350
		private ObjectDataSource objectDataSource1;

		// Token: 0x04001CB7 RID: 7351
		private ReportHeaderBand ReportHeader;

		// Token: 0x04001CB8 RID: 7352
		public XRTable xrTable1;

		// Token: 0x04001CB9 RID: 7353
		public XRTableRow xrTableRow2;

		// Token: 0x04001CBA RID: 7354
		public XRTableCell xrTableCell13;

		// Token: 0x04001CBB RID: 7355
		public XRTableRow xrTableRow3;

		// Token: 0x04001CBC RID: 7356
		public XRTableCell xrTableCell2;

		// Token: 0x04001CBD RID: 7357
		public XRTableRow xrTableRow4;

		// Token: 0x04001CBE RID: 7358
		public XRTableCell xrTableCell3;

		// Token: 0x04001CBF RID: 7359
		public XRTableRow xrTableRow1;

		// Token: 0x04001CC0 RID: 7360
		public XRTableCell xrTableCell1;

		// Token: 0x04001CC1 RID: 7361
		public XRTableRow xrTableRow8;

		// Token: 0x04001CC2 RID: 7362
		public XRTableCell xrTableCell6;

		// Token: 0x04001CC3 RID: 7363
		public XRPictureBox xrPictureBox2;

		// Token: 0x04001CC4 RID: 7364
		public XRTable xrTable2;

		// Token: 0x04001CC5 RID: 7365
		public XRTableRow xrTableRow5;

		// Token: 0x04001CC6 RID: 7366
		public XRTableCell Cell_ReportName;

		// Token: 0x04001CC7 RID: 7367
		public XRTableRow xrTableRow6;

		// Token: 0x04001CC8 RID: 7368
		public XRTableCell xrTableCell4;

		// Token: 0x04001CC9 RID: 7369
		public XRTableRow xrTableRow7;

		// Token: 0x04001CCA RID: 7370
		public XRTableCell Cell_Filters;

		// Token: 0x04001CCB RID: 7371
		private GroupHeaderBand GroupHeader1;

		// Token: 0x04001CCC RID: 7372
		private XRTable table1;

		// Token: 0x04001CCD RID: 7373
		private XRTableRow tableRow1;

		// Token: 0x04001CCE RID: 7374
		private XRTableCell tableCell1;

		// Token: 0x04001CCF RID: 7375
		private XRTableCell tableCell4;

		// Token: 0x04001CD0 RID: 7376
		private XRTableCell tableCell5;

		// Token: 0x04001CD1 RID: 7377
		private XRTableCell tableCell6;

		// Token: 0x04001CD2 RID: 7378
		private XRTableCell tableCell7;

		// Token: 0x04001CD3 RID: 7379
		private XRTableCell tableCell8;

		// Token: 0x04001CD4 RID: 7380
		private XRTableCell tableCell9;

		// Token: 0x04001CD5 RID: 7381
		private XRTableCell tableCell10;

		// Token: 0x04001CD6 RID: 7382
		private XRTableCell tableCell11;

		// Token: 0x04001CD7 RID: 7383
		private XRTable table2;

		// Token: 0x04001CD8 RID: 7384
		private XRTableRow tableRow2;

		// Token: 0x04001CD9 RID: 7385
		private XRTableCell tableCell12;

		// Token: 0x04001CDA RID: 7386
		private XRTableCell tableCell15;

		// Token: 0x04001CDB RID: 7387
		private XRTableCell tableCell16;

		// Token: 0x04001CDC RID: 7388
		private XRTableCell tableCell17;

		// Token: 0x04001CDD RID: 7389
		private XRTableCell tableCell18;

		// Token: 0x04001CDE RID: 7390
		private XRTableCell tableCell19;

		// Token: 0x04001CDF RID: 7391
		private XRTableCell tableCell20;

		// Token: 0x04001CE0 RID: 7392
		private XRTableCell tableCell21;

		// Token: 0x04001CE1 RID: 7393
		private XRTableCell tableCell22;

		// Token: 0x04001CE2 RID: 7394
		private DetailReportBand DetailReport;

		// Token: 0x04001CE3 RID: 7395
		private DetailBand Detail1;

		// Token: 0x04001CE4 RID: 7396
		private XRSubreport xrSubreport1;

		// Token: 0x04001CE5 RID: 7397
		private GroupFooterBand GroupFooter1;

		// Token: 0x04001CE6 RID: 7398
		private XRTable xrTable5;

		// Token: 0x04001CE7 RID: 7399
		private XRTableRow xrTableRow11;

		// Token: 0x04001CE8 RID: 7400
		private XRTableCell xrTableCell14;

		// Token: 0x04001CE9 RID: 7401
		private XRTableCell xrTableCell28;

		// Token: 0x04001CEA RID: 7402
		private XRTableCell xrTableCell10;

		// Token: 0x04001CEB RID: 7403
		private XRTableCell xrTableCell7;

		// Token: 0x04001CEC RID: 7404
		private XRTableCell xrTableCell5;

		// Token: 0x04001CED RID: 7405
		private XRTableCell xrTableCell29;

		// Token: 0x04001CEE RID: 7406
		private XRTableCell xrTableCell8;

		// Token: 0x04001CEF RID: 7407
		private XRTableCell xrTableCell12;

		// Token: 0x04001CF0 RID: 7408
		private XRLine xrLine1;

		// Token: 0x04001CF1 RID: 7409
		private XRLabel xrLabel2;

		// Token: 0x04001CF2 RID: 7410
		private XRLabel xrLabel1;

		// Token: 0x04001CF3 RID: 7411
		private XRTableCell xrTableCell9;

		// Token: 0x04001CF4 RID: 7412
		private CalculatedField TotalPer;
	}
}
