﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [Table("DefaultCosts")]
    [DisplayColumn("Dépenses par défaut")]
    public class DefaultExpenses : BaseNotifyPropertyChangedModel
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "N°")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this.id;
            }
            set
            {
                base.SetProperty<int>(ref this.id, value, "ID");
            }
        }

        [Display(Name = "Code de l'ordre")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int OrderID
        {
            get
            {
                return this.orderID;
            }
            set
            {
                base.SetProperty<int>(ref this.orderID, value, "OrderID");
            }
        }

        public WorkOrder Order { get; set; }

        [Display(Name = "N° de compte")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public int AccountID
        {
            get
            {
                return this.accountID;
            }
            set
            {
                base.SetProperty<int>(ref this.accountID, value, "AccountID");
            }
        }

        [Display(Name = "Coût")]
        [Required(ErrorMessage = "Ce champ est obligatoire")]
        public double Cost
        {
            get
            {
                return this.cost;
            }
            set
            {
                base.SetProperty<double>(ref this.cost, value, "Cost");
            }
        }

        [Display(Name = "Notes")]
        [StringLength(250)]
        public string Notes
        {
            get
            {
                return this.notes;
            }
            set
            {
                base.SetProperty<string>(ref this.notes, value, "Notes");
            }
        }

        private int id;

        private int orderID;

        private int accountID;

        private double cost;

        private string notes;
    }
}
