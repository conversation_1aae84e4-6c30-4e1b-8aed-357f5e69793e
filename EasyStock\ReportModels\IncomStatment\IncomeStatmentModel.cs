﻿using System.Collections.Generic;
using System.Linq;

namespace EasyStock.ReportModels.IncomStatment
{
    public class IncomeStatmentModel : CompanyInfoReportModel
    {
        public IncomeStatmentModelDetial SalesIncome { get; set; }
        public IncomeStatmentModelDetial SalesReturn { get; set; }
        public IncomeStatmentModelDetial SalesDiscount { get; set; }

        public IncomeStatmentModelDetial TotalSalesIncome
        {
            get
            {
                IncomeStatmentModelDetial total = new IncomeStatmentModelDetial
                {
                    Account = "Ventes Nettes"
                };

                total.Debit = (this.SalesIncome?.Debit ?? 0.0) +
                              (this.SalesReturn?.Debit ?? 0.0) +
                              (this.SalesDiscount?.Debit ?? 0.0);

                total.Credit = (this.SalesIncome?.Credit ?? 0.0) +
                               (this.SalesReturn?.Credit ?? 0.0) +
                               (this.SalesDiscount?.Credit ?? 0.0);

                total.CalculateBalance();
                return total;
            }
        }

        public IncomeStatmentModelDetial OpeningStock { get; set; }
        public IncomeStatmentModelDetial Purchases { get; set; }
        public IncomeStatmentModelDetial PurchasesReturn { get; set; }
        public IncomeStatmentModelDetial PurchasesDiscount { get; set; }

        public IncomeStatmentModelDetial TotalPurchase
        {
            get
            {
                IncomeStatmentModelDetial total = new IncomeStatmentModelDetial
                {
                    Account = "Achats Nets"
                };

                total.Debit = (this.Purchases?.Debit ?? 0.0) +
                              (this.PurchasesReturn?.Debit ?? 0.0) +
                              (this.PurchasesDiscount?.Debit ?? 0.0);

                total.Credit = (this.Purchases?.Credit ?? 0.0) +
                               (this.PurchasesReturn?.Credit ?? 0.0) +
                               (this.PurchasesDiscount?.Credit ?? 0.0);

                total.CalculateBalance();
                return total;
            }
        }

        public IncomeStatmentModelDetial GoodsAvailableForSale
        {
            get
            {
                IncomeStatmentModelDetial total = new IncomeStatmentModelDetial
                {
                    Account = "Stock Disponible pour Vente"
                };

                total.Debit = (this.OpeningStock?.Debit ?? 0.0) +
                              (this.TotalPurchase?.Debit ?? 0.0);

                total.Credit = (this.OpeningStock?.Credit ?? 0.0) +
                               (this.TotalPurchase?.Credit ?? 0.0);

                total.CalculateBalance();
                return total;
            }
        }

        public IncomeStatmentModelDetial ClosingStock { get; set; }

        public IncomeStatmentModelDetial CostOfSoldGoods
        {
            get
            {
                IncomeStatmentModelDetial total = new IncomeStatmentModelDetial
                {
                    Account = "Coût des Marchandises Vendues"
                };

                total.Debit = (this.GoodsAvailableForSale?.Debit ?? 0.0) +
                              (this.ClosingStock?.Debit ?? 0.0);

                total.Credit = (this.GoodsAvailableForSale?.Credit ?? 0.0) +
                               (this.ClosingStock?.Credit ?? 0.0);

                total.CalculateBalance();
                return total;
            }
        }

        public IncomeStatmentModelDetial TotalProfits
        {
            get
            {
                IncomeStatmentModelDetial total = new IncomeStatmentModelDetial
                {
                    Account = "Profit Brut"
                };

                total.Debit = (this.TotalSalesIncome?.Debit ?? 0.0) -
                              (this.CostOfSoldGoods?.Debit ?? 0.0);

                total.Credit = (this.TotalSalesIncome?.Credit ?? 0.0) -
                               (this.CostOfSoldGoods?.Credit ?? 0.0);

                total.CalculateBalance();
                return total;
            }
        }

        public List<IncomeStatmentModelDetial> Expences { get; set; }
        public List<IncomeStatmentModelDetial> Revnue { get; set; }

        public IncomeStatmentModelDetial NetProfits
        {
            get
            {
                IncomeStatmentModelDetial total = new IncomeStatmentModelDetial
                {
                    Account = "Bénéfice Net"
                };

                total.Debit = (this.TotalProfits?.Debit ?? 0.0) +
                              (this.Expences?.Sum(x => x.Debit) ?? 0.0) +
                              (this.Revnue?.Sum(x => x.Debit) ?? 0.0);

                total.Credit = (this.TotalProfits?.Credit ?? 0.0) +
                               (this.Expences?.Sum(x => x.Credit) ?? 0.0) +
                               (this.Revnue?.Sum(x => x.Credit) ?? 0.0);

                total.CalculateBalance();
                if (total.Debit > 0.0)
                {
                    total.Account = "Perte Net";
                }

                return total;
            }
        }
    }
}