﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EasyStock.Models
{
    [DisplayName("Résumé des opérations de caisse")]
    public class DrawerPeriodTransSummeryItem : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [ReadOnly(true)]
        public int ID { get; set; }

        public DrawerPeriod DrawerPeriod { get; set; }

        public int DrawerPeriodID { get; set; }

        [Display(Name = "Type d'opération")]
        public SystemProcess ProcessType { get; set; }

        [Display(Name = "Nombre")]
        public int ProcessCount
        {
            get
            {
                return this.processCount;
            }
            set
            {
                base.SetProperty<int>(ref this.processCount, value, "ProcessCount");
            }
        }

        [Display(Name = "Total")]
        public double ProcessSum
        {
            get
            {
                return this.processSum;
            }
            set
            {
                base.SetProperty<double>(ref this.processSum, value, "ProcessSum");
            }
        }

        private int processCount;

        private double processSum;
    }
}
