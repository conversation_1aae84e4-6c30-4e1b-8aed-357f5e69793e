﻿using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.ReportModels;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace EasyStock.ReportViews
{
    public partial class TotalSalesProductCategoriesView : XtraReportForm
    {
        public TotalSalesProductCategoriesView() : base(new ReportFilter[]
        {
            ReportFilter.Date,
            ReportFilter.Product
        })
        {
            this.InitializeComponent();
        }

        internal override bool ValidateFilters()
        {
            bool flag = !base.FiltersForm.hasStartDate;
            bool result;
            if (flag)
            {
                base.FiltersForm.FromDate.ErrorText = "La date doit être sélectionnée";
                result = false;
            }
            else
            {
                bool flag2 = !base.FiltersForm.hasEndtDate;
                if (flag2)
                {
                    base.FiltersForm.ToDate.ErrorText = "La date doit être sélectionnée";
                    result = false;
                }
                else
                {
                    result = true;
                }
            }
            return result;
        }

        public override void RefreshDataSource()
        {
            if (ValidateFilters())
            {
                TotalSalesProductCategoriesReport rpt = new TotalSalesProductCategoriesReport();
                ICollection<TotalSalesProductCategoriesModel> dataSource = (ICollection<TotalSalesProductCategoriesModel>)(rpt.DataSource = GetData());
                rpt.SetCompanyInfo(dataSource.FirstOrDefault());
                rpt.Cell_ReportName.Text = Text;
                rpt.Cell_Filters.Text = base.FiltersForm.FilterText;
                documentViewer1.DocumentSource = rpt;
                Task.Run(() =>
                {
                    rpt.CreateDocument();
                    this.Invoke(new Action(() =>
                    {
                        this.documentViewer1.Refresh();
                    }));
                });
            }
        }

        private ICollection<TotalSalesProductCategoriesModel> GetData()
        {
            int[] ProductIDs = base.FiltersForm.Products.EditValue?.ToArray();
            ERPDataContext context = new ERPDataContext();
            try
            {
                IQueryable<Product> products = context.Products.AsQueryable();
                if (ProductIDs != null && ProductIDs.Count() > 0)
                {
                    products = products.Where((Product Prod) => ProductIDs.Contains(Prod.ID));
                }
                var filterdTrans = from x in context.ProductTransactions
                                   where (int)x.TransactionState == 2
                                   select x into inv
                                   where (int)inv.Type == 4 && DbFunctions.TruncateTime(inv.Date) >= FiltersForm.startDate && DbFunctions.TruncateTime(inv.Date) <= FiltersForm.endtDate
                                   select new
                                   {
                                       ProductID = inv.ProductID,
                                       Quantity = inv.Quantity * inv.Factor,
                                       Price = inv.Price,
                                       CostValue = inv.CostValue,
                                       ID = inv.ID
                                   };
                var data = from tr in filterdTrans
                           group tr by tr.ProductID into g
                           join pr in products on g.Key equals pr.ID
                           from cat in context.ProductCategories.Where((ProductCategory x) => (int?)x.ID == pr.CategoryID).DefaultIfEmpty()
                           select new
                           {
                               Category = ((cat != null) ? cat.Name : "(Sans Catégorie)"),
                               Qty = g.Sum(x => x.Quantity),
                               InvCount = g.Select(x => x.ID).Distinct().Count(),
                               TotalSales = (g.Sum(x => (double?)x.Quantity * (double?)x.Price) ?? 0.0)
                           };
                IQueryable<TotalSalesProductCategoriesModel> CatData = from ct in data
                                                                       group ct by ct.Category into gCat
                                                                       select new TotalSalesProductCategoriesModel
                                                                       {
                                                                           Category = gCat.Key,
                                                                           Qty = gCat.Sum(x => x.Qty),
                                                                           TotalAmount = (gCat.Sum(x => (double?)x.TotalSales) ?? 0.0),
                                                                           InvoiceCount = gCat.Select(x => x.InvCount).Distinct().Count()
                                                                       };
                return CatData.ToList();
            }
            finally
            {
                if (context != null)
                {
                    ((IDisposable)context).Dispose();
                }
            }
        }
    }
}
