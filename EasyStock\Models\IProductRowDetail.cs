﻿using System;

namespace EasyStock.Models
{
    public interface IProductRowDetail
    {
        int ID { get; set; }

        Product Product { get; }

        int ProductID { get; set; }

        ProductUnit Unit { get; }

        int UnitID { get; set; }

        double Factor { get; set; }

        int? ColorID { get; set; }

        int? SizeID { get; set; }

        double Quantity { get; set; }

        double RowQuantity { get; }

        double Price { get; set; }

        double Total { get; }

        string ProductCode { get; set; }

        DateTime? Expire { get; set; }

        double Discount { get; set; }

        double DiscountPercentage { get; set; }

        double Net { get; }

        double Tax { get; set; }

        double TaxPercentage { get; set; }
    }
}
