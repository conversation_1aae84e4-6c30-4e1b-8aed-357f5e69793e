﻿using DevExpress.Data.Linq;
using DevExpress.Utils;
using DevExpress.Utils.Menu;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Grid.ViewInfo;
using EasyStock.Classes;
using EasyStock.Common;
using EasyStock.Controller;
using EasyStock.MainViews;
using EasyStock.Models;
using EasyStock.Properties;
using EasyStock.Reports;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace EasyStock.Views.Financial
{
    public partial class PettyCashListView : MasterForm
    {
        public static PettyCashListView Instance
        {
            get
            {
                bool flag = PettyCashListView.instance == null || PettyCashListView.instance.IsDisposed;
                if (flag)
                {
                    PettyCashListView.instance = new PettyCashListView();
                }
                return PettyCashListView.instance;
            }
        }

        public PettyCashListView()
        {
            this.InitializeComponent();
            this.btn_Save.Visibility = BarItemVisibility.Never;
            this.btn_Print.Visibility = BarItemVisibility.Always;
            this.btn_Refresh.Visibility = BarItemVisibility.Always;
            this.btn_Print.Enabled = true;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.FocusRectStyle = DrawFocusRectStyle.RowFocus;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.SetAlternatingColors();
            this.gridView1.DoubleClick += this.GridView1_DoubleClick;
        }

        private void GridView1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
        {
            bool flag = e.HitInfo.InRow || e.HitInfo.InRowCell;
            if (flag)
            {
                DXMenuItem dxButtonPrint = new DXMenuItem
                {
                    Caption = "Imprimer"
                };
                dxButtonPrint.ImageOptions.SvgImage = Resources.print;
                dxButtonPrint.Click += this.DxButtonPrint_Click;
                e.Menu.Items.Add(dxButtonPrint);
                DXMenuItem dxButtonDelete = new DXMenuItem
                {
                    Caption = "Supprimer"
                };
                dxButtonDelete.ImageOptions.SvgImage = Resources.delete;
                dxButtonDelete.Click += this.DxButtonDelete_Click;
                e.Menu.Items.Add(dxButtonDelete);
            }
        }

        private void DxButtonDelete_Click(object sender, EventArgs e)
        {
            this.btn_Delete.PerformClick();
        }

        public override void Delete()
        {
            bool flag = this.gridView1.SelectedRowsCount == 0;
            if (!flag)
            {
                bool flag2 = CurrentSession.CurrentUser.Type == UserType.User && !CurrentSession.CurrentUser.AccessProfile.GetWindowProfile("PettyCashView").CheckAction(WindowActions.Delete);
                if (!flag2)
                {
                    List<int> ids = new List<int>();
                    foreach (int item in this.gridView1.GetSelectedRows())
                    {
                        ids.Add(Convert.ToInt32(this.gridView1.GetRowCellValue(item, "ID")));
                    }
                    this.RefreshData();
                }
            }
        }

        private void DxButtonPrint_Click(object sender, EventArgs e)
        {
            bool flag = CurrentSession.CurrentUser.Type == UserType.User && !CurrentSession.CurrentUser.AccessProfile.GetWindowProfile("PettyCashView").CheckAction(WindowActions.Print);
            if (!flag)
            {
                int[] handles = this.gridView1.GetSelectedRows();
                List<int> ids = new List<int>();
                foreach (int handel in handles)
                {
                    ids.Add(Convert.ToInt32(this.gridView1.GetRowCellValue(handel, "ID")));
                }
                bool flag2 = ids.Count == 0;
                if (flag2)
                {
                    Vip.Notification.Alert.ShowError("Veuillez sélectionner au moins une avance.");
                }
            }
        }

        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            DXMouseEventArgs ea = e as DXMouseEventArgs;
            GridView view = sender as GridView;
            GridHitInfo info = view.CalcHitInfo(ea.Location);
            bool flag = info.InRow || info.InRowCell;
            if (flag)
            {
                this.SelectedBOM = Convert.ToInt32(view.GetFocusedRowCellValue("ID"));
                bool flag2 = !this.IsSelectionMode;
                if (flag2)
                {
                    this.OpenForm(this.SelectedBOM);
                }
                else
                {
                    base.Close();
                }
            }
        }

        public virtual void OpenForm(int id)
        {
            HomeForm.OpenForm(PettyCashView.Instance, false, false, false);
            PettyCashView.Instance.GoTo(id);
        }

        public override void New()
        {
            HomeForm.OpenForm(PettyCashView.Instance, false, false, false);
            PettyCashView.Instance.btn_New.PerformClick();
        }

        public override void Print()
        {
            GridReportP.Print(this.gridControl1, this.Text, "");
            base.Print();
        }

        public override void RefreshData()
        {
            using (ERPDataContext db = new ERPDataContext())
            {
                this.repoBranch.BindToDataSource((from x in db.Accounts
                                                  select new
                                                  {
                                                      x.ID,
                                                      x.Name
                                                  }).ToList(), "ID", "Name");
                this.repocostcenter.BindToDataSource((from x in db.CostCenters
                                                      select new
                                                      {
                                                          x.ID,
                                                          x.Name
                                                      }).ToList(), "ID", "Name");
                this.repocurrency.BindToDataSource((from x in db.Currencies
                                                    select new
                                                    {
                                                        x.ID,
                                                        x.Name
                                                    }).ToList(), "ID", "Name");
                this.repoholder.BindToDataSource((from x in db.PettyCashHolders
                                                  select new
                                                  {
                                                      x.ID,
                                                      x.Name
                                                  }).ToList(), "ID", "Name");
            }
            this.entityInstantFeedbackSource1 = new EntityInstantFeedbackSource();
            this.entityInstantFeedbackSource1.DesignTimeElementType = typeof(PettyCash);
            this.entityInstantFeedbackSource1.KeyExpression = "ID";
            this.entityInstantFeedbackSource1.DismissQueryable += this.EntityInstantFeedbackSource1_DismissQueryable;
            this.entityInstantFeedbackSource1.GetQueryable += this.EntityInstantFeedbackSource1_GetQueryable;
            this.gridControl1.DataSource = this.entityInstantFeedbackSource1;
            base.RefreshData();
        }

        private void EntityInstantFeedbackSource1_DismissQueryable(object sender, GetQueryableEventArgs e)
        {
        }

        private void EntityInstantFeedbackSource1_GetQueryable(object sender, GetQueryableEventArgs e)
        {
            ERPDataContext context = new ERPDataContext();
            DbSet<PettyCash> q2 = context.PettyCashes;
            e.QueryableSource = q2;
        }

        private static PettyCashListView instance;

        public bool IsSelectionMode;

        private int SelectedBOM;

        private EntityInstantFeedbackSource entityInstantFeedbackSource1;
    }
}
