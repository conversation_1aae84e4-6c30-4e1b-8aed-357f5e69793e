﻿using EasyStock.Controller;
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace EasyStock.Models
{
    public abstract class Bill : BaseNotifyPropertyChangedModel
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        [Display(Name = "Numéro", GroupName = "Données de la facture")]
        [ReadOnly(true)]
        public int ID
        {
            get
            {
                return this._id;
            }
            set
            {
                base.SetProperty<int>(ref this._id, value, "ID");
            }
        }

        [Display(Name = "Code", GroupName = "Données de la facture")]
        public string Code
        {
            get
            {
                return this._code;
            }
            set
            {
                base.SetProperty<string>(ref this._code, value, "Code");
            }
        }

        [NotMapped]
        public Store Store
        {
            get
            {
                bool flag = this._branch == null || this._branch.ID != this.StoreID;
                if (flag)
                {
                    using (ERPDataContext db = new ERPDataContext())
                    {
                        this._branch = db.Stores.SingleOrDefault((Store x) => x.ID == this.StoreID);
                    }
                }
                return this._branch;
            }
        }

        [Display(Name = "Dépôt", GroupName = "Données de la facture")]
        [Range(1, 2147483647, ErrorMessage = "Veuillez sélectionner un magasin")]
        public int StoreID
        {
            get
            {
                return this._branchID;
            }
            set
            {
                base.SetProperty<int>(ref this._branchID, value, "StoreID");
            }
        }

        [Display(Name = "")]
        public Branch Branch
        {
            get
            {
                return this.branch;
            }
            set
            {
                base.SetProperty<Branch>(ref this.branch, value, "Branch");
            }
        }

        [Display(Name = "Succursale")]
        [Range(1, 2147483647, ErrorMessage = "Ce champ est requis")]
        public int BranchID
        {
            get
            {
                return this.branchID;
            }
            set
            {
                base.SetProperty<int>(ref this.branchID, value, "BranchID");
            }
        }

        [Display(Name = "Date", GroupName = "Données de la facture")]
        public DateTime Date
        {
            get
            {
                return this._date;
            }
            set
            {
                base.SetProperty<DateTime>(ref this._date, value, "Date");
            }
        }

        [Display(Name = "Notes", GroupName = "Données de la facture")]
        public string Notes
        {
            get
            {
                return this._notes;
            }
            set
            {
                base.SetProperty<string>(ref this._notes, value, "Notes");
            }
        }

        [Display(Name = "Total", GroupName = "Valeur")]
        public double Total
        {
            get
            {
                return this._total;
            }
            set
            {
                base.SetProperty<double>(ref this._total, value, "Total");
            }
        }

        public Journal Journal { get; set; }

        public int? JournalID { get; set; }

        public abstract void GetNewCode();

        [Display(Name = "Utilisateur")]
        public int UserID { get; set; }

        [Display(Name = "Date d'échéance")]
        public DateTime? DueDate { get; set; }

        public Store _branch;

        private double _total;

        private string _notes;

        private DateTime _date;

        private int _branchID;

        private string _code;

        private int _id;

        private Branch branch;

        private int branchID;
    }
}
